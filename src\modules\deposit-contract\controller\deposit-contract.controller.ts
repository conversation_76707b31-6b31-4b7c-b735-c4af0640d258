import { PermissionEnum } from "../../../modules/shared/enum/permission.enum";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../../shared-modules";
import { AddDepositPropertyUnitDto, AddInstallmentsDto, CreateDepositContractDto, FindAllDepositContractQueryDto, UpdateDepositContractDto, UpdateTransactionSuccessfulDto } from '../dto/deposit-contract.dto';
import { DepositContractService } from "../service/deposit-contract.service";

import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Injectable,
  Param,
  Post,
  Put, Query,
  UseGuards,
} from '@nestjs/common';

@Injectable()
@Controller('v2/deposit-contract')
@UseGuards(JwtAuthGuard)
export class DepositContractController {
  constructor(private readonly depositContractService: DepositContractService) { }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEPOSIT_CONTRACT_CREATE)
  @Post('')
  async createDepositContract(@Body() body: CreateDepositContractDto, @User() user, @Headers() headers: any) {
    return await this.depositContractService.createDepositContract(body, user, headers);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEPOSIT_CONTRACT_UPDATE)
  @Put(':id')
  async updateDepositContract(@Param('id') id: string, @Body() body: UpdateDepositContractDto, @User() user, @Headers() headers: any) {
    return await this.depositContractService.updateDepositContract(id, body, user, headers);
  }

  @Post('add-property-units')
  async addDepositPropertyUnit(@Body() body: AddDepositPropertyUnitDto, @User() user, @Headers() headers: any) {
    return await this.depositContractService.addDepositPropertyUnit(body, user, headers);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEPOSIT_CONTRACT_GET_ALL)
  @Get('')
  async getDepositContractByQuery(@Query() query?: FindAllDepositContractQueryDto) {
    return await this.depositContractService.getDepositContractByQuery(query);
  }

  @Delete(':id/delete-property-unit/:propertyUnitId')
  async deleteDepositContractPropertyUnit(@Param('id') id: string, @Param('propertyUnitId') propertyUnitId: string, @User() user, @Headers() headers: any) {
    return await this.depositContractService.deleteDepositPropertyUnit(id, propertyUnitId, user, headers);
  }

  @Post('add-installments')
  async addInstallments(@Body() body: AddInstallmentsDto, @User() user) {
    return await this.depositContractService.addInstallments(body, user);
  }

  @Delete(':id/delete-installment/:installmentId')
  async deleteDepositContractInstallment(@Param('id') id: string, @Param('installmentId') installmentId: string, @User() user) {
    return await this.depositContractService.deleteDepositContractInstallment(id, installmentId, user);
  }
  
  @Put('installment/transactionSuccessful')
  async updateTransactionSuccessful(@Body() body: UpdateTransactionSuccessfulDto, @User() user) {
    return await this.depositContractService.updateTransactionSuccessful(body, user);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEPOSIT_CONTRACT_GET_ID)
  @Get(':id')
  async getDepositContractById(@Param('id') id: string) {
    return await this.depositContractService.getDepositContractById(id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEPOSIT_CONTRACT_DELETE)
  @Delete(':id')
  async deleteDepositContract(@Param('id') id: string, @User() user) {
    return await this.depositContractService.deleteDepositContract(id, user);
  }

}