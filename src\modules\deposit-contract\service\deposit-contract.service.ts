import { IDepositContract } from "../interfaces/deposit-contract.interface";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { DepositContractRepository } from "../repository/deposit-contract.repository";
import { BaseServiceRepository, IListQueryInterface } from "../../../../shared-modules";
import {
  AddDepositPropertyUnitDto,
  AddInstallmentsDto,
  CreateDepositContractDto,
  FindAllDepositContractQueryDto,
  InstallmentTypeEnum,
  UpdateDepositContractDto,
  UpdateTransactionSuccessfulDto,
} from "../dto/deposit-contract.dto";
import { CmdPatternConst, CommonConst } from "../../shared/constant";
import { CodeGenerateService } from "../../code-generate/service";
import { PropertyClient } from "../../mgs-sender/property.client";
import { OrgchartClient } from "../../mgs-sender/orgchart.client";
import { OrgchartEnum } from "../../shared/enum/deposit-contract.enum";
import { isEmpty } from "lodash";
import axios from "axios";
import { CommissionClient } from "../../mgs-sender/commission.client";

@Injectable()
export class DepositContractService extends BaseServiceRepository<IDepositContract> {
  constructor(
    private readonly depositContractRepository: DepositContractRepository,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly propertyClient: PropertyClient,
    private readonly orgchartClient: OrgchartClient,
    private readonly commissionClient: CommissionClient
  ) {
    super(depositContractRepository);
  }

  private escapeRegex(text) {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
  }

  async getSalePolicyById(id: string): Promise<any> {
    return await this.commissionClient.sendDataPromise(
      { commandPayload: {salePolicy: {
        id
      } }},
      CmdPatternConst.SALE_POLICY.GET
    );
  }

  async getProjectById(id: string): Promise<any> {
    return await this.propertyClient.sendDataPromise(
      { id },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
    );
  }

  async getOrgchartById(query: any): Promise<any> {
    return await this.orgchartClient.sendDataPromise(
      { type: query.type, id: query.id },
      CmdPatternConst.ORGCHART.GET_ORGCHART_BY_ID
    );
  }

  async getPropertyUnitIds(query: any): Promise<any> {
    return await this.propertyClient.sendDataPromise(
      { ids: query.ids},
      CmdPatternConst.PROPERTY.GET_PROPERTY_UNIT_BY_IDS
    );
  }

  private async updateEmailOrgchart(data, headers) {
    await axios.put(
      process.env.UPDATE_EMAIL_ORGCHART_INTERNAL_OR_EXTERNAL,
      data,
      {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      }
    );
  }

  async propertyDepositContractUpdate(
    user: any,
    depositContract: any
  ): Promise<any> {
    return await this.propertyClient.sendData(
      { user, depositContract },
      CmdPatternConst.PROPERTY.PROPERTY_DEPOSIT_CONTRACT_UPDATE_QUEUE
    );
  }

  private async updatePropertyUnitsWithDepositContract(
    depositContract: any,
    isDeposited: boolean,
    headers: any
  ): Promise<void> {
    const data = {
        propertyUnitIds: depositContract.propertyUnitIds,
        depositContract: {
          id: depositContract.id,
          orgchartId: depositContract.orgchart.id,
          orgchartType: depositContract.orgchart.type,
        },
        isDeposited,
      }
    await axios.put(
      process.env.MSX_PROPERTY_UPDATE_DEPOSIT_CONTRACT_URI,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: headers.authorization,
        },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000,
      }
    );
  }

  async createDepositContract(
    body: CreateDepositContractDto,
    user: any,
    headers: any
  ): Promise<IDepositContract> {
    const existingProject = await this.getProjectById(body.projectId);
    if (!existingProject) {
      throw new HttpException({ errorCode: "PRJE0002" }, HttpStatus.OK);
    }
    const orgchartPromise = this.getOrgchartById({
      type: body.orgchart.type,
      id: body.orgchart.id,
    });


    if (body.orgchartPartnerId) {
      const existingOrgchartPartner = await this.getOrgchartById({
        type: OrgchartEnum.INTERNAL,
        id: body.orgchartPartnerId,
      });
      if (existingOrgchartPartner && !existingOrgchartPartner.id) {
        throw new HttpException({ errorCode: "DEPCT0004" }, HttpStatus.OK);
      }
    }

    const [existingOrgchart] = await Promise.all([
      orgchartPromise,
    ]);

    const isEmpty = (obj: any) =>
      !obj || (typeof obj === "object" && Object.keys(obj).length === 0);

    if (isEmpty(existingOrgchart)) {
      throw new HttpException({ errorCode: "ORGE0004" }, HttpStatus.OK);
    }

    const prefix = `${CommonConst.AGGREGATES.DEPOSIT_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}`;
    const createDepositContract = await super.create({
      ...body,
      propertyQuantity: body.propertyUnitIds ? body.propertyUnitIds.length : 0,
      createdBy: user.id,
      code: await this.codeGenerateService.generateCode(
        CommonConst.AGGREGATES.DEPOSIT_CONTRACT.NAME,
        prefix
      ),
    });
    if (!createDepositContract.id){
      throw new HttpException({ errorCode: "DEPCT0009" }, HttpStatus.OK);
    }
    if (createDepositContract.propertyUnitIds.length > 0){
      await this.updatePropertyUnitsWithDepositContract(createDepositContract, true, headers);
    }
    if (body.orgchart.email) {
      const isInternal = body.orgchart.type === OrgchartEnum.INTERNAL;
      const isExternal = body.orgchart.type === OrgchartEnum.EXTERNAL;

      const shouldUpdate =
        (isInternal && existingOrgchart.representEmail !== body.orgchart.email) ||
        (isExternal && existingOrgchart.email !== body.orgchart.email);

      if (shouldUpdate) {
        await this.updateEmailOrgchart(
          {
            id: body.orgchart.id,
            representEmail: body.orgchart.email,
            type: body.orgchart.type,
          },
          headers,
        );
      }
    }
    return createDepositContract
  }

  private async comparePropertyUnits(
    oldPropertyUnitIds: any,
    newPropertyUnitIds: any
  ): Promise<any> {
    const oldIds = Array.isArray(oldPropertyUnitIds)
      ? oldPropertyUnitIds
      : [];
    const newIds = Array.isArray(newPropertyUnitIds)
      ? newPropertyUnitIds
      : [];

    // Các id bị xóa (có trong cũ, không còn trong mới)
    const removed = oldIds.filter(id => !newIds.includes(id));
    // Các id thêm mới (có trong mới, không có trong cũ)
    const added = newIds.filter(id => !oldIds.includes(id));
    // Danh sách id cuối cùng (không trùng lặp)
    const finalIds = Array.from(new Set(newIds));
    return { removed, added, finalIds };
  }

  private async updateOrgchart(
    exitingDepositContract: any,
    body: any
  ): Promise<any> {
    let existingOrgchart = exitingDepositContract.orgchart;
    let existingOrgchartPartner = exitingDepositContract.orgchartPartnerId;
    if (
      exitingDepositContract.orgchart.id !== body.orgchart.id ||
      exitingDepositContract.orgchart.type !== body.orgchart.type
    ) {
      const orgchartPromise = await this.getOrgchartById({
        type: body.orgchart.type,
        id: body.orgchart.id,
      });
      if (!orgchartPromise || Object.keys(orgchartPromise).length === 0) {
        throw new HttpException({ errorCode: "ORGE0004" }, HttpStatus.OK);
      }
      existingOrgchart = {
        id: orgchartPromise.id,
        code: orgchartPromise.code,
        name: orgchartPromise.name ? orgchartPromise.name : orgchartPromise.nameVN,
        taxCode: orgchartPromise.taxCode,
        email: orgchartPromise.email,
        type: body.orgchart.type,
      }
    }

    if (exitingDepositContract.orgchartPartnerId !== body.orgchartPartnerId) {
      const orgchartPartnerPromise = await this.getOrgchartById({
        type: OrgchartEnum.INTERNAL,
        id: body.orgchartPartnerId,
      });
      if (!orgchartPartnerPromise || Object.keys(orgchartPartnerPromise).length === 0) {
        throw new HttpException({ errorCode: "DEPCT0004" }, HttpStatus.OK);
      }
      existingOrgchartPartner = orgchartPartnerPromise.id;
    }
    return { existingOrgchart, existingOrgchartPartner };
  }

  private async updatePropertyUnits(
    exitingDepositContract: any,
    added: any,
    removed: any,
    headers: any
  ): Promise<any> {
    if (added.length > 0){
      const depositContract = {
        id: exitingDepositContract.id,
        orgchart: exitingDepositContract.orgchart,
        propertyUnitIds: added,
      }
      await this.updatePropertyUnitsWithDepositContract(depositContract, true, headers);
    }

    if (removed.length > 0){
      const depositContract = {
        id: exitingDepositContract.id,
        orgchart: exitingDepositContract.orgchart,
        propertyUnitIds: removed,
      }
      await this.updatePropertyUnitsWithDepositContract(depositContract, false, headers);
    }
  }

  async updateDepositContract(
    id: string,
    body: UpdateDepositContractDto,
    user: any,
    headers: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id,
      softDelete: false,
      status: 1,
    });
    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }

    if (exitingDepositContract.projectId !== body.projectId) {
      const existingProject = await this.getProjectById(body.projectId);
      if (!existingProject) {
        throw new HttpException({ errorCode: "PRJE0002" }, HttpStatus.OK);
      }
    }
    
    const { existingOrgchart, existingOrgchartPartner } = await this.updateOrgchart(exitingDepositContract, body);
    const { removed, added, finalIds } = await this.comparePropertyUnits(
      exitingDepositContract.propertyUnitIds,
      body.propertyUnitIds
    );

    const updateDepositContract = await super.update(id, {
      ...body,
      propertyQuantity: body.propertyUnitIds ? body.propertyUnitIds.length : 0,
      propertyUnitIds: finalIds,
      orgchart: existingOrgchart,
      orgchartPartnerId: existingOrgchartPartner,
      modifiedBy: user.id,
      modifiedDate: new Date(),
    });
    await this.updatePropertyUnits(updateDepositContract, added, removed, headers);
    if (body.orgchart.email) {
      const isInternal = body.orgchart.type === OrgchartEnum.INTERNAL;
      const isExternal = body.orgchart.type === OrgchartEnum.EXTERNAL;

      const shouldUpdate =
        (isInternal && existingOrgchart.representEmail !== body.orgchart.email) ||
        (isExternal && existingOrgchart.email !== body.orgchart.email);

      if (shouldUpdate) {
        await this.updateEmailOrgchart(
          {
            id: body.orgchart.id,
            representEmail: body.orgchart.email,
            type: body.orgchart.type,
          },
          headers,
        );
      }
    }
    return updateDepositContract
  }

  async addDepositPropertyUnit(
    body: AddDepositPropertyUnitDto,
    user: any,
    headers: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id: body.id,
      softDelete: false,
      status: 1,
    });

    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }

    const oldIds = Array.isArray(exitingDepositContract.propertyUnitIds)
      ? exitingDepositContract.propertyUnitIds
      : [];

    const newIds = Array.isArray(body.propertyUnitIds)
      ? body.propertyUnitIds
      : [];

    // Lọc ra các id chưa tồn tại
    const filteredNewIds = newIds.filter((id) => !oldIds.includes(id));

    // Merge old + new (không trùng)
    exitingDepositContract.propertyUnitIds = [...oldIds, ...filteredNewIds];

    // Nếu filteredNewIds trống → không cần update bên property unit
    if (filteredNewIds.length > 0) {
      await axios.put(
        process.env.MSX_PROPERTY_UPDATE_DEPOSIT_CONTRACT_URI,
        {
          propertyUnitIds: filteredNewIds,
          depositContract: {
            id: exitingDepositContract.id,
            orgchartId: exitingDepositContract.orgchart.id,
            orgchartType: exitingDepositContract.orgchart.type,
          },
          isDeposited: true,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: headers.authorization,
          },
          timeout: parseInt(process.env.TIMEOUT_API) || 120000,
        }
      );
    }

    return await super.update(body.id, {
      ...exitingDepositContract,
      modifiedDate: new Date(),
      modifiedBy: user.id,
    });
  }

  async deleteDepositPropertyUnit(
    id: string,
    propertyUnitId: string,
    user: any,
    headers: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id,
      softDelete: false,
      status: 1,
    });
    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }
    const oldIds = Array.isArray(exitingDepositContract.propertyUnitIds)
      ? exitingDepositContract.propertyUnitIds
      : [];
    exitingDepositContract.propertyUnitIds = oldIds.filter(
      (unitId) => unitId !== propertyUnitId
    );

    await axios.put(
      process.env.MSX_PROPERTY_UPDATE_DEPOSIT_CONTRACT_URI,
      {
        propertyUnitIds: [propertyUnitId],
        depositContract: {
          id: exitingDepositContract.id,
          orgchartId: exitingDepositContract.orgchart.id,
          orgchartType: exitingDepositContract.orgchart.type,
        },
        isDeposited: false,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: headers.authorization,
        },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000,
      }
    );
    return await super.update(id, {
      ...exitingDepositContract,
      modifiedDate: new Date(),
      modifiedBy: user.id,
    });
  }

  async addInstallments(
    body: AddInstallmentsDto,
    user: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id: body.id,
      softDelete: false,
      status: 1,
    });

    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }

    const oldInstallments = Array.isArray(exitingDepositContract.installments)
      ? exitingDepositContract.installments
      : [];

    // Chuẩn hóa installments mới
    const plainInstallments = body.installments.map((item) => {
      const newItem = { ...item };

      // Convert exactDays -> Date nếu hợp lệ
      if (newItem.exactDays) {
        const date = new Date(newItem.exactDays);
        newItem.exactDays = isNaN(date.getTime()) ? null : date;
      }

      return newItem;
    });

    // Check duplicate name trong new installments
    await this.checkDuplicateNames(plainInstallments, oldInstallments);

    // Validate tổng tiền
    await this.validateTotalAmount(exitingDepositContract.depositAmount, [
      ...oldInstallments,
      ...plainInstallments,
    ]);

    return await super.update(body.id, {
      ...exitingDepositContract,
      installments: [...oldInstallments, ...plainInstallments],
      modifiedDate: new Date(),
      modifiedBy: user.id,
    });
  }

  async checkDuplicateNames(newInstallments: any[], oldInstallments: any[]) {
    const oldNames = oldInstallments.map((i) => i.name?.trim().toLowerCase());

    const newNames = newInstallments.map((i) => i.name?.trim().toLowerCase());

    // Trùng trong chính new
    const hasDupInNew = newNames.some(
      (name, idx) => newNames.indexOf(name) !== idx
    );
    if (hasDupInNew) {
      throw new HttpException({ errorCode: "DEPCT0005" }, HttpStatus.OK);
    }

    const hasDupWithOld = newNames.some((name) => oldNames.includes(name));
    if (hasDupWithOld) {
      throw new HttpException({ errorCode: "DEPCT0006" }, HttpStatus.OK);
    }
  }

  async validateTotalAmount(contractAmount: number, installments: any[]) {
    const totalAmount = installments.reduce((sum, item) => {
      if (item.type === InstallmentTypeEnum.CURRENCY) {
        return sum + item.value;
      }
      if (item.type === InstallmentTypeEnum.PERCENT) {
        return sum + (item.value * contractAmount) / 100;
      }
      return sum;
    }, 0);

    if (totalAmount > contractAmount) {
      throw new HttpException({ errorCode: "DEPCT0007" }, HttpStatus.OK);
    }
  }

  //  async deleteDepositContractInstallment(
  //     id: string,
  //     installmentId: string,
  //     user: any
  //   ): Promise<IDepositContract> {
  //     const exitingDepositContract = await super.findOne({
  //       id,
  //       softDelete: false,
  //       status: 1,
  //     });
  //     if (!exitingDepositContract) {
  //       throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
  //     }
  //     const oldIds = Array.isArray(exitingDepositContract.installments)
  //       ? exitingDepositContract.installments
  //       : [];
  //     exitingDepositContract.installments = oldIds.filter(
  //       (unitId) => unitId.id !== installmentId
  //     );
  //     return await super.update(id, {
  //       ...exitingDepositContract,
  //       modifiedDate: new Date(),
  //       modifiedBy: user.id,
  //     });
  //   }

  async deleteDepositContractInstallment(
    id: string,
    installmentId: string,
    user: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id,
      softDelete: false,
      status: 1,
    });

    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }

    const oldInstallments = Array.isArray(exitingDepositContract.installments)
      ? exitingDepositContract.installments
      : [];

    const targetInstallment = oldInstallments.find(
      (item) => item.id === installmentId
    );

    if (!targetInstallment) {
      throw new HttpException({ errorCode: "DEPCT0003" }, HttpStatus.OK);
    }

    if (targetInstallment.transactionSuccessful === true) {
      throw new HttpException({ errorCode: "DEPCT0008" }, HttpStatus.OK);
    }

    const updatedInstallments = oldInstallments.filter(
      (item) => item.id !== installmentId
    );

    return await super.update(id, {
      ...exitingDepositContract,
      installments: updatedInstallments,
      modifiedDate: new Date(),
      modifiedBy: user.id,
    });
  }

  async updateTransactionSuccessful(
    body: UpdateTransactionSuccessfulDto,
    user: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id: body.id,
      softDelete: false,
      status: 1,
    });
    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }
    const installments = Array.isArray(exitingDepositContract.installments)
      ? exitingDepositContract.installments
      : [];
    const idx = installments.findIndex(
      (item: any) => item.id === body.installmentId
    );
    if (idx === -1) {
      throw new HttpException({ errorCode: "DEPCT0003" }, HttpStatus.OK);
    }
    installments[idx].transactionSuccessful =
      !installments[idx].transactionSuccessful;
    exitingDepositContract.installments = installments;
    return await super.update(body.id, {
      ...exitingDepositContract,
      modifiedDate: new Date(),
      modifiedBy: user.id,
    });
  }

  convertStringInputToArrayDto(val) {
    if (val && typeof val === 'string') {
      return val.split(',').map(item => item.trim());
    }
    return [];
  }

  async getOrCreateCache<K, V>(
  cache: Map<K, Promise<V>>,
  key: K,
  fetcher: () => Promise<V>
): Promise<V> {
  if (cache.has(key)) {
    return cache.get(key)!;
  } else {
    const promise = fetcher();
    cache.set(key, promise);
    return promise;
  }
}

  async getDepositContractByQuery(
    queryParams: Partial<FindAllDepositContractQueryDto>
  ): Promise<any> {
    const query: any = {};
    if (!isEmpty(queryParams.search)) {
      const search = this.escapeRegex(queryParams.search);
      const regexSearch = new RegExp(".*" + search + ".*", "i");
      query.$or = [{ code: { $regex: regexSearch } }];
    }
    if (!isEmpty(queryParams.projectIds)) {
      const projectIds = await this.convertStringInputToArrayDto(queryParams.projectIds);
      query.projectId = { $in: projectIds };
    }

    if (!isEmpty(queryParams.orgType)) {
      const orgTypes = await this.convertStringInputToArrayDto(queryParams.orgType);
      query["orgchart.type"] = { $in: orgTypes };
    }

    if (!isEmpty(queryParams.depositForm)) {
      const depositForms = await this.convertStringInputToArrayDto(queryParams.depositForm);
      query.depositForm = { $in: depositForms };
    }
    query.status = 1;
    query.softDelete = false;
    const result = await super.pagination(
      query,
      queryParams.page,
      queryParams.pageSize,
      { createdDate: -1 }
    );

  const projectCache = new Map<string, Promise<any>>();
  const orgchartCache = new Map<string, Promise<any>>();

  result.rows = await Promise.all(
  result.rows.map(async (item) => {
    const projectPromise = this.getOrCreateCache(
      projectCache,
      item.projectId,
      () => this.getProjectById(item.projectId)
    );

    const orgchartKey = `${item.orgchart.type}:${item.orgchart.id}`;
    const orgchartPromise = this.getOrCreateCache(
      orgchartCache,
      orgchartKey,
      () =>
        this.getOrgchartById({
          type: item.orgchart.type,
          id: item.orgchart.id,
        })
    );

    const [existingProject, orgchart] = await Promise.all([
      projectPromise,
      orgchartPromise,
    ]);

    if (!existingProject) {
      throw new HttpException(
        { errorCode: "PRJE0002" },
        HttpStatus.OK
      );
    }
    const plain = item.toObject();
    return {
      ...plain,
      project: {
        id: item.projectId,
        name: existingProject.name,
        code: existingProject.code,
      },
      orgchart: {
        id: item.orgchart.id,
        code: item.orgchart.type === OrgchartEnum.INTERNAL ? orgchart.code : orgchart.partnershipCode,
        name: item.orgchart.type === OrgchartEnum.INTERNAL ? orgchart.name ? orgchart.name : orgchart.nameVN : orgchart.partnershipName,
        taxCode: orgchart.taxCode,
        type: item.orgchart.type,
        },
      };
    })
  );
    // let listAllPolicy: IDepositContract[] = [];
    // for (const item of result.rows) {
    //   if (item.project?.id) {
    //     const getProject = await this.propertyClient.sendDataPromise(
    //       { id: item.project.id },
    //       CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT
    //     );
    //     if (getProject) item.project = getProject;
    //   }
    //   listAllPolicy.push(item);
    // }

    return {
      rows: result.rows,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      totalPages: result.totalPages,
    };
  }

  async getDepositContractById(
    id: string,
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id,
      softDelete: false,
      status: 1,
    });

    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }

    const projectCache = new Map<string, Promise<any>>();
    const orgchartCache = new Map<string, Promise<any>>();
    const orgchartPartnerCache = new Map<string, Promise<any>>();
    const salePolicyCache = new Map<string, Promise<any>>();
    if (exitingDepositContract.propertyUnitIds.length > 0){
      exitingDepositContract.propertyUnits = await this.getPropertyUnitIds({
        ids: exitingDepositContract.propertyUnitIds,
      });
    }
    const projectPromise = this.getOrCreateCache(
      projectCache,
      exitingDepositContract.projectId,
      () => this.getProjectById(exitingDepositContract.projectId)
    );

    const orgchartKey = `${exitingDepositContract.orgchart.type}:${exitingDepositContract.orgchart.id}`;
    const orgchartPromise = this.getOrCreateCache(
      orgchartCache,
      orgchartKey,
      () =>
        this.getOrgchartById({
          type: exitingDepositContract.orgchart.type,
          id: exitingDepositContract.orgchart.id,
        })
    );
    let orgchartPartnerPromise:any;
    if (exitingDepositContract.orgchartPartnerId){
      const orgchartCachePartnerKey = `${OrgchartEnum.INTERNAL}:${exitingDepositContract.orgchartPartnerId}`;
      orgchartPartnerPromise = this.getOrCreateCache(
        orgchartPartnerCache,
        orgchartCachePartnerKey,
        () =>
          this.getOrgchartById({
            type: OrgchartEnum.INTERNAL,
            id: exitingDepositContract.orgchartPartnerId,
          })
      );
    }
    
    let salePolicyPromise = Promise.resolve(null);
    if (exitingDepositContract.salePolicyId) {
    salePolicyPromise = this.getOrCreateCache(
      salePolicyCache,
      exitingDepositContract.salePolicyId,
      () =>
        this.getSalePolicyById(exitingDepositContract.salePolicyId)
    );
    }

    const [existingProject, orgchart, orgchartPartner, salePolicy] = await Promise.all([
      projectPromise,
      orgchartPromise,
      orgchartPartnerPromise,
      salePolicyPromise
    ]);

    if (!existingProject) {
      throw new HttpException(
        { errorCode: "PRJE0002" },
        HttpStatus.OK
      );
    }

    (exitingDepositContract as any).project = {
      id: exitingDepositContract.projectId,
      name: existingProject.name,
      code: existingProject.code,
    };
    (exitingDepositContract as any).orgchart = {
      id: exitingDepositContract.orgchart.id,
      code: exitingDepositContract.orgchart.type === OrgchartEnum.INTERNAL ? orgchart.code : orgchart.partnershipCode,
      name: exitingDepositContract.orgchart.type === OrgchartEnum.INTERNAL ? orgchart.name ? orgchart.name : orgchart.nameVN : orgchart.partnershipName,
      email: exitingDepositContract.orgchart.email,
      taxCode: orgchart.taxCode,
      bpID: exitingDepositContract.orgchart.bpID,
      type: exitingDepositContract.orgchart.type,
    };
    if(orgchartPartner){
      (exitingDepositContract as any).orgchartPartner = {
      id: exitingDepositContract.orgchartPartnerId,
      code: orgchartPartner.code,
      name: orgchartPartner.name ? orgchartPartner.name : orgchartPartner.nameVN,
      email: exitingDepositContract.orgchart.type === OrgchartEnum.INTERNAL ? orgchartPartner.representEmail : orgchartPartner.email,
      taxCode: orgchartPartner.taxCode
      };
    }
    if (salePolicy) {
      (exitingDepositContract as any).salePolicy = {
        id: exitingDepositContract.salePolicyId,
        name: salePolicy.name,
        code: salePolicy.code,
      };
    }
    
    return exitingDepositContract;
  }

  async deleteDepositContract(
    id: string,
    user: any
  ): Promise<IDepositContract> {
    const exitingDepositContract = await super.findOne({
      id,
      softDelete: false,
      status: 1,
    });
    if (!exitingDepositContract) {
      throw new HttpException({ errorCode: "DEPCT0001" }, HttpStatus.OK);
    }
    return await super.update(id, {
      ...exitingDepositContract,
      softDelete: true,
      modifiedDate: new Date(),
      modifiedBy: user.id,
    });
  }
}
