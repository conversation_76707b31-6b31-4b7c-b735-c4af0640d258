import { Controller, Put, Post, Body, Headers, UseGuards, UseInterceptors, UploadedFiles, Param, Res, HttpCode } from "@nestjs/common";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { DebtCommissionListService } from "./service";
import { Action } from "../shared/enum/action.enum";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { ApiUseTags, ApiBearerAuth, ApiForbiddenResponse, ApiCreatedResponse } from "@nestjs/swagger";
import { FilesInterceptor } from "@nestjs/platform-express";
import { Query } from "@nestjs/common";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
import * as _ from 'lodash';
import { CalculateBaseDebtCommissionDto, UpdateDebtCommissionListDto } from "./dto/commission-list.dto";

@ApiBearerAuth()
@ApiUseTags("v1/debt/commission-list")
@Controller("v1/debt/commission-list")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard, RoleGuard)
export class DebtCommissionListController {

  private resSuccess = { success: true };
  private actionName: string = Action.NOTIFY;

  constructor(
    private readonly service: DebtCommissionListService
  ) { }

  @Roles(PermissionEnum.DEBT_COMMISSION_CALCULATE)
  @Post('calculate')
  @HttpCode(200)
  async calculateCommissionList(
    @User() userLogged,
    @Body() dto: CalculateBaseDebtCommissionDto
  ) {
    return await this.service.calculateBaseCommission(userLogged, dto);
  }

  // update commission list
  @Roles(PermissionEnum.DEBT_COMMISSION_UPDATE)
  @Put()
  @ApiCreatedResponse({ description: "The record has been successfully updated." })
  @ApiForbiddenResponse({ description: "Forbidden." })
  async updateCommissionList(
    @User() userLogged,
    @Body() dto: UpdateDebtCommissionListDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.service.updateCommissionList(userLogged, dto, this.actionName);
  }

  // import bang ke dieu chinh
  @Roles(PermissionEnum.DEBT_COMMISSION_UPDATE)
  @UseInterceptors(FilesInterceptor("files"))
  @Post("import")
  @ApiCreatedResponse({ description: "The record has been successfully created." })
  @ApiForbiddenResponse({ description: "Forbidden." })
  async import(
    @UploadedFiles() files,
    @User() userLogged,
    @Query() query: any,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return (
      (await this.service.importCommissionList(
        userLogged,
        query.commissionId,
        files,
        this.actionName
      )) || this.resSuccess
    );
  }
}
