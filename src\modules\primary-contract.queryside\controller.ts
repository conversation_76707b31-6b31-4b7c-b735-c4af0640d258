import {
  Controller,
  Get,
  NotFoundException,
  Param,
  Query,
  Res,
  UseInterceptors,
} from "@nestjs/common";
import { PrimaryContractQueryService } from "./service";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { ACGuard, UseRoles } from "nest-access-control";
import { RolesGuard } from "../../common/guards/roles.guard";
import { UseGuards } from "@nestjs/common";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { AuthGuard } from "@nestjs/passport";
import { FileGenerationService } from "./file-generation.service";
import { existsSync, unlinkSync } from "fs";
import { join } from "path";
import { IPrimaryContractDocument } from "./interfaces/document.interface";
import { BadRequestException } from "@nestjs/common";
import { ErrorConst } from "../shared/constant/error.const";
import { CommonUtils } from "../shared/classes/class-utils";
import { ScheduleQueryRepository } from "../schedule.queryside/repository/schedule.query.repository";
import { StaticAssetService } from "../config/static-asset.service";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
const momentTz = require("moment-timezone");
import { Response } from 'express';
import * as Bluebird from 'bluebird';
const fs = Bluebird.promisifyAll(require('fs'));
const XlsxTemplate = require('xlsx-template');
import * as path from 'path';
import moment = require('moment');
import * as ExcelJS from 'exceljs';
import { StsClient } from "../mgs-sender/sts.client";
import { FindPrimaryContractQueryDto } from "./dto/primary-contract.dto";
import { HandoverDeliveryItemType, HandoverStatusEnum } from "../shared/enum/status.enum";
const _ = require("lodash");
@Controller("v1/primary-contract")
@UseGuards(JwtAuthGuard)
export class PrimaryContractQueryController {
  constructor(
    private readonly primaryContractService: PrimaryContractQueryService,
    private readonly fileGenerationService: FileGenerationService,
    private readonly scheduleQueryRepository: ScheduleQueryRepository,
    private readonly staticAssetService: StaticAssetService,
    private readonly stsClient: StsClient
  ) { }


  @Get("/testPublish")
  async testPublish(): Promise<any> {
    return await this.stsClient.sendData({
      data: [{
        "permissions": ["demand.create", "demand.update", "demand.delete"],
        "msxName": "demand"
      }, {
        "permissions": ["demand.template.create", "demand.template.update"],
        "msxName": "demand-template"
      }]
    }, 'test.publish');

  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get()
  async getContract(@User() user: any, @Query() query: FindPrimaryContractQueryDto) {
    query.createdBy = user.id;
    let isSeeAll: boolean = user.roles.includes(
      PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    );
    return await this.primaryContractService.getContract(user, query, isSeeAll);
  }

  @Get("download")
  async downloadContract(@User() user: any, @Query() query: any, @Res() res) {
    query.createdBy = user.id;
    let isSeeAll: boolean = user.roles.includes(
      PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    );
    const data = await this.primaryContractService.getContract(
      user,
      query,
      isSeeAll
    );
    const fileName = "Danh_sach_contract" + new Date().getTime();
    await this.fileGenerationService.exportContract(user, fileName, data);
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      `${fileName}.xlsx`
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(`${fileName}.xlsx`, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }
  @Get("download/:id")
  async downloadContractById(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any,
    @Res() res
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    const project = await this.primaryContractService.getProjectById(
      data.primaryTransaction.project.id
    );
    let url = null;
    const scheduleId = data?.policyPayment?.schedule?.id;
    const schedule: any = scheduleId
      ? await this.scheduleQueryRepository.findOne({ id: scheduleId })
      : null;
    if (schedule && schedule.templates && schedule.templates.length) {
      url = schedule.templates[0].absoluteUrl;
    } else if (_.get(project, "setting.templateFileContract", null)) {
      const file = project.setting.templateFileContract.find(
        (item) => item.type === query.type
      );
      url = file ? file.file.absoluteUrl : null;
    }
    if (!url) {
      throw new NotFoundException("File template not found");
    }

    const fileName = "contract" + id + new Date().getTime();

    await this.fileGenerationService.downloadContractById(
      user,
      fileName,
      data,
      url,
      fileName
    );
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      `${fileName}.docx`
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(`${fileName}.docx`, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }
  @Get("download/:id/confirmPayment")
  async confirmPayment(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any,
    @Res() res
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    const project = await this.primaryContractService.getProjectById(
      data.primaryTransaction.project.id
    );
    let url = _.get(project, "setting.templateFileHasStatus", []).find(
      (e) =>
        e.stage &&
        e.stage.includes(parseInt(query.stage)) &&
        e.fileId === query.fileId
    )?.file?.absoluteUrl;
    if (!url) {
      throw new NotFoundException("File template not found");
    }

    const fileName = "contract" + id + new Date().getTime();

    await this.fileGenerationService.downloadContractById(
      user,
      fileName,
      data,
      url,
      fileName
    );
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      `${fileName}.docx`
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(`${fileName}.docx`, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }

  @Get("download/:id/historyPayment")
  async historyPayment(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    let url = null;
    if (
      data?.policyPayment &&
      data?.policyPayment?.schedule &&
      data?.policyPayment?.schedule.id
    ) {
      const scheduleId = data?.policyPayment?.schedule?.id;
      const schedule: any = scheduleId
        ? await this.scheduleQueryRepository.findOne({ id: scheduleId })
        : null;
      if (schedule && schedule.templateFiles && schedule.templateFiles.length) {
        url = schedule.templateFiles[0].absoluteUrl;
      }
    } else if (
      data?.policyPayment &&
      data?.policyPayment?.schedule &&
      !data?.policyPayment?.schedule.id
    ) {
      const schedule = data?.policyPayment?.schedule;
      if (schedule && schedule.templateFiles && schedule.templateFiles.length) {
        url = schedule.templateFiles[0].absoluteUrl;
      }
    }
    if (!url) {
      throw new NotFoundException("File template not found");
    }

    const fileName = "Tien_Do_Thanh_Toan";
    const fileNameUnique =
      "Tien_Do_Thanh_Toan_" + id + "_" + new Date().getTime();

    const result = await this.fileGenerationService.downloadContractById(
      user,
      fileNameUnique,
      data,
      url,
      fileName,
      true,
      true
    );
    return result;
  }
  @Get("/contract/download-query")
  async downloadTicketByQuery(
    @User() user: any,
    @Query() query: any,
    @Res() res
  ) {
    query.createdBy = user.id;
    let isSeeAll: boolean = user.roles.includes(
      PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    );
    return await this.primaryContractService.printTicketByQuery(
      user,
      query,
      res,
      isSeeAll
    );
  }
  @Get("/getContractReminder")
  findContractReminder(@User() user: any) {
    return this.primaryContractService.getContractReminder();
  }

  @Get("/getDepositContract")
  async findDepositForPurchaseContract(@User() user: any, @Query() query: any) {
    let isSeeAll: boolean = user.roles.includes(
      PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    );
    return await this.primaryContractService.getDepositForPurchaseContract(
      user,
      query,
      isSeeAll
    );
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_ALL,
    action: "read",
    possession: "own",
  })
  @Get("/getDebtReportContract")
  async getDebtReportContract(@User() user: any, @Query() query: any) {
    return await this.primaryContractService.getDebtReportContract(user, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_EXPORT_DEBT_REPORT,
    action: "read",
    possession: "own",
  })
  @Get("/getDebtReportContract/export")
  async exportDebtReportContract(@User() user: any, @Query() query: any) {
    // check xem user login có được cấu hình template thống kê công nợ không
    const fileTemplate =
      await this.primaryContractService.getFileByPosIdUserLogin(user);
    if (fileTemplate) {
      // Lấy data từ db
      let data = await this.primaryContractService.getDebtReportContract(
        user,
        query
      );
      data = data.map((item, idx) => {
        item.stt = idx + 1;
        item.debt =
          item.debt.toString().length < 5
            ? item.debt
            : CommonUtils.parseVND(item.debt);
        item.countPaymentDueDate =
          item.paymentDueDate &&
            this.primaryContractService.countDayBetweenCurrentDate(
              item.paymentDueDate
            ) > 0
            ? this.primaryContractService
              .countDayBetweenCurrentDate(item.paymentDueDate)
              .toString()
            : "";
        item.paymentDueDate = item.paymentDueDate
          ? momentTz(item.paymentDueDate)
            .tz("Asia/Ho_Chi_Minh")
            .format("DD/MM/YYYY")
          : "";
        item.expiredDate = item.expiredDate
          ? momentTz(item.expiredDate)
            .tz("Asia/Ho_Chi_Minh")
            .format("DD/MM/YYYY")
          : "";
        return item;
      });
      const fileName = `Cong_no.xlsx`;
      // đẩy data vào file
      return await this.fileGenerationService.generateDebtReportTemplate(
        data,
        fileTemplate,
        fileName,
        true
      );
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "template"),
      });
    }
  }

  @Get("downloadInterestCalculation/:contractId")
  async downloadInterestCalculation(
    @User() user: any,
    @Param("contractId") id: string,
    @Query() query: any,
    @Res() res
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    let projectInfo: any = {};
    if (
      data &&
      data.primaryTransaction &&
      data.primaryTransaction.project &&
      data.primaryTransaction.project.id
    ) {
      projectInfo = await this.primaryContractService.getProjectById(
        data.primaryTransaction.project.id
      );
    }
    let url = this.staticAssetService.getTemplateFileDownload(
      "Template_Interest_Calculation.docx"
    );
    if (!url) {
      throw new NotFoundException("File template not found");
    }
    let interestCalculation: any = {};
    if (query.interestId) {
      interestCalculation = data.interestCalculations.find(
        (interset) => interset.id === query.interestId
      );
    }
    interestCalculation.customerName =
      data?.primaryTransaction?.customer?.personalInfo?.name;
    interestCalculation.address =
      data?.primaryTransaction?.customer?.info?.address?.fullAddress;
    interestCalculation.productCode =
      data?.primaryTransaction?.propertyUnit?.code;
    interestCalculation.projectName = data?.primaryTransaction?.project?.name;
    interestCalculation.contractName = data?.name;
    interestCalculation.signedDate = data?.signedDate;
    interestCalculation.companyName = projectInfo?.investor;
    interestCalculation.banks = projectInfo?.banks;
    const fileName =
      "interestCalculation" + query.interestId + new Date().getTime() + ".docx";

    await this.fileGenerationService.generateAndSaveInterestCalculationFile(
      user.name,
      interestCalculation,
      url,
      fileName
    );
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      fileName
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(fileName, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }
  //xem chi tiết căn hộ bàn giao
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ID)
  @Get("/:id")
  findById(
    @User() user: any,
    @Param("id") id: string
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getContractByIdNew(user, id);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET_ID,
    action: "read",
    possession: "own",
  })
  @Get("byPropertyUnitId/:propertyUnitId")
  findByUnitId(
    @User() user: any,
    @Param("propertyUnitId") propertyUnitId: string
  ): Promise<IPrimaryContractDocument> {
    if (!propertyUnitId) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.getContractByPropertyUnitId(
      user,
      propertyUnitId
    );
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: "read",
    possession: "own",
  })
  @Get("/getByDiscountPolicy/ids")
  async findContractByDiscountPolicyByIds(
    @User() user: any,
    @Query("ids") ids: string
  ): Promise<IPrimaryContractDocument[]> {
    if (!ids) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "discount-policy",
          "ID",
          "null"
        ),
      });
    }
    const arrId = ids.split(",");
    return await Promise.all(
      arrId.map((id) =>
        this.primaryContractService.getContractByDiscountPolicy(user, id)
      )
    );
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: "read",
    possession: "own",
  })
  @Get("/getByDiscountPolicy/:id")
  findContractByDiscountPolicy(
    @User() user: any,
    @Param("id") id: string
  ): Promise<IPrimaryContractDocument> {
    if (!id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "discount-policy",
          "ID",
          "null"
        ),
      });
    }
    return this.primaryContractService.getContractByDiscountPolicy(user, id);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET_ID,
    action: "read",
    possession: "own",
  })
  @Get("calculateInterest/:id")
  calculateInterestById(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any
  ): Promise<any> {
    if (!id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.calculateInterestById(user, id, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: "read",
    possession: "own",
  })
  @Get("/getByCareCustomer/:id")
  getByCareCustomer(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getByCustomer({ id: id }, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/handover/listPropertyUnit")
  getHandoverListPropertyUnit(
    @User() user: any,
    @Query() query: any
  ): Promise<any> {
    return this.primaryContractService.getHandoverListPropertyUnit(query);
  }
  //danh sách căn hộ đã lên lịch, xác nhận bàn giao
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/handover/:handoverId")
  getHandoverByProject(
    @User() user: any,
    @Param("handoverId") handoverId: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getHandoverByProjectNew(handoverId, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/handover/report/:handoverId")

  async reportHandoverByProject(
    @User() user,
    @Param("handoverId") handoverId: string,
    @Query() query: any,
    @Res() res: Response
  ) {
    try {
      let fileName = 'DanhSachCanHoBanGiao.xlsx';

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Danh sách');

      // 1️⃣ Header
      const headers = [
        'STT',
        'Số sản phẩm',
        'Loại sản phẩm',
        'Khách hàng',
        'Email',
        'Số điện thoại',
        'Địa chỉ',
        'Ngày bàn giao theo lịch',
        'Ngày xác nhận bàn giao',
        'Nhân viên hỗ trợ',
        'Trạng thái bàn giao sản phẩm',
        'Trạng thái bàn giao sổ',
        'Gửi email'
      ];

      worksheet.addRow(headers);

      // Style cho header
      const headerRow = worksheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFC000' }, // vàng cam như ảnh
        };
        cell.font = { bold: true };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
      headerRow.height = 20;

      // 2️⃣ Lấy data
      const data = await this.primaryContractService.getHandoverByProject(
        handoverId,
        query
      );

      let STT = 1;
      const HandoverStatusViMap: Record<HandoverStatusEnum, string> = {
        [HandoverStatusEnum.init]: "Chưa bàn giao",
        [HandoverStatusEnum.later]: "Bàn giao sau",
        [HandoverStatusEnum.handed]: "Đã bàn giao",
        [HandoverStatusEnum.scheduled]: "Đã lên lịch",
        [HandoverStatusEnum.Eligible]: "Sản phẩm đủ điều kiện làm sổ",
        [HandoverStatusEnum.Cer_in_process]: "Sản phẩm đang làm sổ",
        [HandoverStatusEnum.Cer_ready_handover]: "Sản phẩm đã có sổ đợi bàn giao",
        [HandoverStatusEnum.Cer_handed_over]: "Sản phẩm đã bàn giao sổ",
      };

      for (let rowData of data.rows) {
        worksheet.addRow([
          STT++,
          rowData.primaryTransaction?.propertyUnit?.code,
          rowData.projectType,
          rowData.primaryTransaction?.customer?.personalInfo?.name,
          rowData.primaryTransaction?.customer?.personalInfo?.email,
          rowData.primaryTransaction?.customer?.personalInfo?.phone,
          rowData.primaryTransaction?.customer?.info?.address?.fullAddress ||
          rowData.primaryTransaction?.customer?.info?.address?.address || '',
          rowData.handoverSchedule?.handoverStartTime
            ? this.formatDate(rowData.handoverSchedule.handoverStartTime)
            : '',
          rowData.deliveryResult?.deliveryDate
            ? this.formatDate(rowData.deliveryResult.deliveryDate)
            : '',
          rowData.handoverSchedule?.supportEmployee?.name || '',
          rowData.handoverStatus
            ? HandoverStatusViMap[rowData.handoverStatus as HandoverStatusEnum] || rowData.handoverStatus
            : "",
          rowData.cerHandoverStatus
            ? HandoverStatusViMap[rowData.cerHandoverStatus as HandoverStatusEnum] || rowData.cerHandoverStatus
            : "",
          rowData.isSendDelivery === true ? "Đã gửi email" : "Chưa gửi email",
        ]);
      }

      // 3️⃣ Auto-fit cột
      worksheet.columns.forEach((column) => {
        let maxLength = 10;
        column.eachCell({ includeEmpty: true }, (cell) => {
          const cellValue = cell.value ? cell.value.toString() : '';
          if (cellValue.length > maxLength) {
            maxLength = cellValue.length;
          }
        });
        column.width = maxLength + 2;
      });

      // 4️⃣ Xuất file
      const buffer = await workbook.xlsx.writeBuffer();
      const now = moment();
      const date = now.format('YYYY-MM-DD');
      const time = now.format('HHmmss');
      fileName = `DanhSachCanHoBanGiao_${date}_${time}.xlsx`;
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      return res.send(buffer);

    } catch (error) {
      throw new Error(`Export failed: ${error.message}`);
    }
  }

  // helper format ngày
  private formatDate(dateStr: string) {
    const date = new Date(dateStr);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  //danh sách căn hộ chưa lên lịch
  @UseGuards(RoleGuard)
  // @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Roles(PermissionEnum.HANDOVER_SCHEDULE_GET_ALL)
  @Get("/handover-by-status/:handoverId")
  getHandoverByStatus(
    @User() user: any,
    @Param("handoverId") handoverId: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getHandoverByStatusNew(handoverId, query);
  }

  @Get('/handover-report/export/:id')
  async exportHandoverReport(@User() user: any,
    @Param("id") id: string, @Res() res: Response) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Chi tiết bàn giao');

      const contract = await this.primaryContractService.getContractById(user, id);
      const {
        primaryTransaction,
        deliveryHistories,
        handoverStatus,
      } = contract

      const deliveryGroups = deliveryHistories
        .filter(all => all.isPass)
        .flatMap((history, index) => {
          const stt = String(index + 1).padStart(2, '0'); // "01", "02", ...
          return (history.items || []).map(item => ({
            ...item,
            stt,
            deliveryDate: history.deliveryDate
          }));
        });

      const deliveryItems = deliveryGroups.flatMap(groupItem =>
        (groupItem.list || []).map((listItem, subIndex) => ({
          ...listItem,
          categoryName: groupItem.name,
          deliveryDate: groupItem.deliveryDate,
          stt: `${groupItem.stt}.${String(subIndex + 1).padStart(2, '0')}`, // "01.01", "01.02", ...
        }))
      );

      const files = deliveryHistories
        .filter(all => all.isPass)
        .flatMap((history, index) => {
          const stt = String(index + 1).padStart(2, '0'); // "01", "02", ...
          return (history.files || []).map(file => ({
            ...file,
            stt
          }));
        });
      const deliveryPassedItems = deliveryItems
        .filter(all => all.isPass && all.type === HandoverDeliveryItemType.category)
        .sort((a, b) => a.stt.localeCompare(b.stt));

      const deliveryFailedItems = deliveryItems
        .filter(all => !all.isPass && all.type === HandoverDeliveryItemType.category)
        .sort((a, b) => a.stt.localeCompare(b.stt));

      const deliveryIndexes = deliveryItems
        .filter(all => all.type === HandoverDeliveryItemType.index)
        .sort((a, b) => a.stt.localeCompare(b.stt));


      // const deliveryPassedItems = deliveryItems.filter(all => all.isPass && all.type === HandoverDeliveryItemType.category);
      // const deliveryFailedItems = deliveryItems.filter(all => !all.isPass && all.type === HandoverDeliveryItemType.category);
      // const deliveryIndexes = deliveryItems.filter(all => all.type === HandoverDeliveryItemType.index);
      let currentRow = 1;

      // ===== 1. Thông tin chung =====
      worksheet.mergeCells(currentRow, 1, currentRow, 2);
      worksheet.getCell(`A${currentRow}`).value = 'Thông tin chung';
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
      currentRow++;
      const mapHandoverStatusToVietnamese = (status: string): string => {
        switch (status) {
          case 'init':
            return 'Chưa bàn giao';
          case 'later':
            return 'Bàn giao sau';
          case 'handed':
            return 'Đã bàn giao';
          case 'scheduled':
            return 'Đã lên lịch';
          case 'Eligible':
            return 'Đủ điều kiện làm sổ';
          case 'Cer_in_process':
            return 'Đang làm sổ';
          case 'Cer_ready_handover':
            return 'Có sổ - chờ bàn giao';
          case 'Cer_handed_over':
            return 'Đã bàn giao sổ';
          default:
            return status; // fallback nếu không khớp enum
        }
      };

      const infoRows = [
        ['Mã sản phẩm', primaryTransaction.propertyUnit.code],
        ['Dự án', primaryTransaction.project.name],
        ['Loại sản phẩm', contract.type],
        ['Block/ Khu', primaryTransaction.propertyUnit.block],
        ['Tầng/ Lô', primaryTransaction.propertyUnit.floor],
        ['Diện tích', primaryTransaction.propertyUnit.area],
        ['Hướng', primaryTransaction.propertyUnit.direction],
        ['Khách hàng', primaryTransaction.customer.personalInfo.name],
        ['Email', primaryTransaction.customer.personalInfo.email],
        ['Điện thoại', primaryTransaction.customer.personalInfo.phone],
        ['Địa chỉ liên hệ', primaryTransaction.customer.info.address.fullAddress],
        ['Ngày bàn giao', contract.deliveryDate],
        ['Trạng thái', mapHandoverStatusToVietnamese(handoverStatus)],
      ];
      infoRows.forEach(([label, value]) => {
        worksheet.addRow([label, value]);
        currentRow++;
      });

      const addSection = (title: string, headers: string[], data: any[][]) => {
        worksheet.addRow([]);
        currentRow++;

        worksheet.mergeCells(currentRow, 1, currentRow, headers.length);
        worksheet.getCell(`A${currentRow}`).value = title;
        worksheet.getCell(`A${currentRow}`).font = { bold: true };
        currentRow++;

        worksheet.addRow(headers);
        worksheet.getRow(currentRow).font = { bold: true };
        currentRow++;

        if (Array.isArray(data) && data.length > 0) {
          data.forEach((row) => {
            worksheet.addRow(row);
            currentRow++;
          });
        } else {
          // Nếu không có dữ liệu vẫn chèn dòng trống hoặc dòng thông báo
          const emptyRow = headers.map(() => '');
          worksheet.addRow(emptyRow);
          currentRow++;
        }
      };


      // ===== 2. Hạng mục đạt =====
      addSection('Hạng mục đạt', ['Lần bàn giao', 'Tên hạng mục', "Thời gian", 'Nội dung', 'Mô tả'],
        deliveryPassedItems.map(item => [
          item.stt,
          item.categoryName,
          item.deliveryDate,
          item.title,
          item.description
        ]) ?? []
      );

      // ===== 3. Hạng mục chưa đạt =====
      addSection('Hạng mục chưa đạt', ['Lần bàn giao', 'Tên hạng mục', "Thời gian", 'Nội dung', 'Mô tả', 'Lý do'],
        deliveryFailedItems.map(item => [
          item.stt,
          item.categoryName,
          item.deliveryDate,
          item.title,
          item.description,
          item.reason
        ]) ?? []
      );

      // ===== 4. Chỉ số =====
      addSection('Chỉ số', ['Lần bàn giao', 'Tên hạng mục', "Thời gian", 'Nội dung', 'Mô tả', 'Chỉ số'],
        deliveryIndexes.map(item => [
          item.stt,
          item.categoryName,
          item.deliveryDate,
          item.title,
          item.description,
          item.isPass
        ]) ?? []
      );

      // ===== 5. Đánh giá bàn giao =====
      worksheet.addRow([]);
      currentRow++;
      worksheet.mergeCells(currentRow, 1, currentRow, 2);
      worksheet.getCell(`A${currentRow}`).value = 'Đánh giá bàn giao';
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
      currentRow++;
      worksheet.addRow(['Chất lượng', '']);
      worksheet.addRow(['Nhân viên hỗ trợ', '']);
      worksheet.addRow(['Điểm đánh giá', '']);
      currentRow += 3;

      // ===== 6. Tài liệu đính kèm =====
      addSection('Tài liệu đính kèm', ['STT', 'Tên tài liệu', 'Link'],
        files.map((file, index) => [index + 1, file, file]) ?? []
      );

      // ===== Auto width =====
      worksheet.columns.forEach(column => {
        let maxLength = 10;
        column.eachCell({ includeEmpty: true }, (cell) => {
          const val = cell.value?.toString() ?? '';
          maxLength = Math.max(maxLength, val.length);
        });
        column.width = maxLength + 2;
      });

      const fileName = `Chi-tiet-ban-giao_${moment().format('YYYY-MM-DD_HHmmss')}.xlsx`;
      const buffer = await workbook.xlsx.writeBuffer();
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      return res.send(buffer);
    } catch (error) {
      throw new Error(`Export failed: ${error.message}`);
    }
  }

}
