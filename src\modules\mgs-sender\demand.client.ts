import { CmdPatternConst } from "../shared/constant";
import { QueueConst } from "../shared/constant/queue.const";
import { Injectable, Inject } from "@nestjs/common";
import { MsgSenderService } from "./mgs.sender.service";
import { ClientProxy } from "@nestjs/microservices";


@Injectable()
export class DemandClient {
  constructor(
    @Inject(QueueConst.DEMAND_QUEUE) private readonly client: ClientProxy,
    private readonly msgSenderService: MsgSenderService
  ) { }

  async sendData(content: any, cmdPatern: string) {
    this.msgSenderService.subscribe(this.client, content, cmdPatern);
  }

  sendDataPromise(content: any, pattern: string) {
    return this.msgSenderService.promise(this.client, content, pattern);
  }

  async findDemandCustomer(customerId: string[], userId: string) {
    return this.msgSenderService.promise(this.client, { customerId, userId }, CmdPatternConst.DEMAND.GET_BY_CUSTOMER_ID);
  }
}