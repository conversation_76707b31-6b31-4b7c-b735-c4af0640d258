import { Connection } from 'mongoose';
import { DebtCommissionSchema } from '../schemas/debt-commission.schema';
import { CommonConst } from '../../shared/constant';

export const DebtCommissionProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) => connection.model(CommonConst.AGGREGATES.COMMISSION_DEBT.NAME, DebtCommissionSchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
