import { Document } from 'mongoose';

interface Item {
  id: string;
  name: string;
  path: string;
  sizeFile: number;
}
export interface IDepositContractDocumentFolder extends Document {
  id: string;
  depositContractId: string;
  type: string;
  name: string;
  path: string;
  sizeFile: number;
  items: Item[];
  softDelete: boolean;
  createdDate: Date;
  modifiedDate: Date;
  createdBy: string;
  modifiedBy: string;
}