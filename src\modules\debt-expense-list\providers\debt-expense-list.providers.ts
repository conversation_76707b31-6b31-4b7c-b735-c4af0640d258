import { Connection } from 'mongoose';
import { CommonConst } from '../../shared/constant';
import { DebtExpenseListSchema } from '../schemas/schema';

export const DebtExpenseListProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) =>
      connection.model(
        CommonConst.AGGREGATES.DEBT_EXPENSE_LIST.NAME,
        DebtExpenseListSchema
      ),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
