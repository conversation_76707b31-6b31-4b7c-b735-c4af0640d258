import { IBaseInterface } from '../../../interfaces/base.interface';

export interface IDebtCommissionRate extends IBaseInterface {
    topPrice: number;                               // Gi<PERSON> trị cận trên
    bottomPrice: number;                            // Gi<PERSON> trị cận dưới
    unit: number;                                   // Đơn vị tính (0 == VNĐ / 1 == %)
    recordedCommission: number;                     // Thù lao ghi nhận
    recordedCommissionUnit: number;                 // Đơn vị tính (0 == VNĐ / 1 == %)
}
