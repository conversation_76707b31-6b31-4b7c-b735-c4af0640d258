import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { CommonConst } from '../../shared/constant/index';
import { MsxLoggerService } from '../../logger/logger.service';
import { IDebtCommissionPolicyDocument } from '../interfaces/document.interface';

@Injectable()
export class DebtCommissionPolicyQueryRepository {
  private readonly context = DebtCommissionPolicyQueryRepository.name;

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IDebtCommissionPolicyDocument>,
    private readonly loggerService: MsxLoggerService
  ) { }

  async findAll(page: number, pageSize: number, query: any = {}): Promise<any[]> {
    this.loggerService.log(this.context, 'Find all Commission Policy query side');
    const conditions: any[] = [
      {
        $match: query,
      },
      {
        $sort: { modifiedDate: -1 },
      },
      {
        $facet: {
          paginatedResults: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
          totalCount: [{ $count: 'count' }],
        }
      },
    ];
    return await this.readModel.aggregate(conditions)
      .allowDiskUse(true)
      .exec()
      .then(result => {
        const total = result[0].totalCount[0] ? result[0].totalCount[0].count : 0;
        return {
          rows: result[0].paginatedResults,
          page,
          pageSize,
          total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize)
        };
      });
  }


  async findOne(query: any = {}): Promise<IDebtCommissionPolicyDocument> {
    this.loggerService.log(this.context, 'Find one Commission Policy query side');
    return await this.readModel.findOne(query)
      .exec()
      .then(result => {
        return result;
      });
  }

  async findOneFarthestPeriod(query: any = {}): Promise<IDebtCommissionPolicyDocument> {
    this.loggerService.log(this.context, 'Find one Commission Policy query side');
    return await this.readModel.findOne(query)
      .sort({ periodTo: -1 })
      .exec()
      .then(result => {
        return result;
      });
  }

  async findByCodeIn(listCode: string[]): Promise<IDebtCommissionPolicyDocument[]> {
    return await this.readModel.find({ code: { $in: listCode } })
      .exec()
      .then(result => result);
  }

  async findByCode(code: string): Promise<IDebtCommissionPolicyDocument> {
    return await this.readModel.findOne({ code })
      .lean()
      .exec()
      .then(result => result);
  }

  async findById(id: string, exceptQuery?: any): Promise<IDebtCommissionPolicyDocument> {
    this.loggerService.log(this.context, 'Find by id Commission Policy query side');
    return await this.readModel.findOne({ id }, exceptQuery)
      .exec()
      .then((response) => {
        return response;
      });
  }


  async create(model): Promise<IDebtCommissionPolicyDocument> {
    this.loggerService.log(this.context, 'Create Commission Policy at query side');
    return await this.readModel.create(model)
      .then((response) => {
        return response;
      });
  }


  async update(model): Promise<IDebtCommissionPolicyDocument> {
    this.loggerService.log(this.context, 'Update Commission Policy at query side');
    return await this.readModel.updateOne({ id: model.id }, model)
      .then((response) => {
        return response;
      });
  }

  async delete(model): Promise<any> {
    this.loggerService.log(this.context, 'Delete Commission Policy at query side');
    return await this.readModel.deleteOne({ id: model.id })
      .then((response) => {
        return response;
      });
  }
}
