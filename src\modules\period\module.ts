import { QueryDatabaseModule } from "../database/query/query.database.module";
import { QueryProviders } from "./providers/query.cqrs.providers";
import { Module } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { CqrsModule } from "@nestjs/cqrs";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { PeriodController } from "./controller";
import { PeriodService } from "./service";
import { PeriodRepository } from "./repository/period.repository";

@Module({
  imports: [
    QueryDatabaseModule,
    AuthModule,
    CqrsModule,
    MgsSenderModule,
  ],
  controllers: [PeriodController],
  providers: [PeriodService, PeriodRepository, ...QueryProviders],
  exports: [
    PeriodRepository,
    PeriodModule,
    PeriodService,
  ],
})
export class PeriodModule {}
