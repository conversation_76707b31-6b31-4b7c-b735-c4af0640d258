import * as mongoose from "mongoose";
import uuid = require("uuid");
import { StatusEnum } from "../../shared/enum/primary-contract.enum";
import { HandoverDeliveryItemType } from "../../shared/enum/status.enum";

const deliveryItem = new mongoose.Schema( {
  title: {type: String},
  description: {type: String},
  type: {type: HandoverDeliveryItemType},
  isPass: {type: Boolean, default: true},
  indexValue: {type: String},
  reason: {type: String},
  image: {type: String},
  files: { type: [Object], default: [] }
}, {_id: false} );
const email = new mongoose.Schema( {
  emailTitle: { type: String },
  emailFrom: { type: String },
  emailCC: { type: String },
  emailBCC: { type: String },
  emailTemplate: { type: String },
  smsBrandName: { type: String },
  smsTemplate: { type: String },
}, {_id: false} );
const deliveryGroup = new mongoose.Schema({
  name: { type: String },
  list: { type: [deliveryItem] }
}, { _id: false });

export const ownershipCertificateQuerySchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  createdBy: { type: Object, default: {} },
  updatedBy: { type: Object, default: {} },
  createdDate: { type: Date, default: () => Date.now() },
  updatedDate: { type: Date, default: () => Date.now() },
  reasonDelete: { type: String, default: null },
  softDelete: { type: Boolean, default: false },
  project: { type: Object },
  isActive: { type: Number, default: StatusEnum.ACTIVE },
  orgCharts: { type: [Object] },
  hotline: { type: String },
  mondayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  tuesdayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  wednesdayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  thursdayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  fridayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  saturdayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  sundayFrame: { type: [Object], default: []  }, // Khung giờ bàn giao
  itemsForEligible: { type: [deliveryGroup], default: [] },
  itemsCerInProcess: { type: [deliveryGroup], default: [] },
  itemsForCerReadyHandover: { type: [deliveryGroup], default: [] },
  emailForEligible: { type: email, default: {} },
  emailCerHandedOver: { type: email, default: {} },
  emailForCerReadyHandover: { type: email, default: {} },
});

ownershipCertificateQuerySchema.pre("save", function (next) {
  this._id = this.get("id");
  next();
});

// =====
