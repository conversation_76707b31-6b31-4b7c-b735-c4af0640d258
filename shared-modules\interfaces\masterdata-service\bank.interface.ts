import { Document } from 'mongoose';

export interface IBank {
  // 1. Thông tin định danh
  bankCode: string;
  bankName: string;
  branchCode: string;
  branchName: string;
  transactionCode: string;
  source: string;
  isActive: number;
  companyCode: string;
  description: string;
  gLAccount: string;
  bankAccountNumber: any;

  // 2. Thông tin vị trí / địa chỉ
  country: string; // Quốc gia
  city: string;
  district: string;
  ward: string;
  street: string;

  // 3. Thông tin liên hệ
  telephone: string;
  extension: string; // Thông tin mở rộng
  fax: string;

  // 4. Thông tin hệ thống
  createdDate?: Date;
  modifiedDate?: Date;
  lastUpdate: Date;
}


export interface IBankDocument extends Document, IBank {
  id: string;
}
