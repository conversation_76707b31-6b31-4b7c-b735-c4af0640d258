import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { PrimaryContractQueryRepository } from "./repository/primary-contract-query.repository";
import { EmployeeQueryRepository } from "../employee.query/repository/query.repository";
import { PropertyClient } from "../mgs-sender/property.client";
import { CmdPatternConst, CommonConst } from "../shared/constant";
import { MailerClient } from "../mgs-sender/mailer.client";
import { NotificationClient } from "../mgs-sender/notification.client";
import {
  HandoverStatusEnum,
  TransactionStatusEnum,
} from "../shared/enum/status.enum";
import {
  ContractEnum,
  StatusContractEnum,
  StatusEnum,
} from "../shared/enum/primary-contract.enum";
import { CommonUtils } from "../shared/classes/class-utils";
import { CareClient } from "../mgs-sender/care.client";
import { ErrorConst } from "../shared/constant/error.const";
import { HandoverConst } from "../shared/constant/handover.const";
import { HandoverQueryService } from "../handover.queryside/service";
import { HandoverScheduleQueryService } from "../handover-schedule.queryside/service";
import { LiquidationTypeEnum } from "../shared/enum/liquidation.enum";
import { VerifyAccountStatusEnum } from "../shared/enum/care-customer.enum";
import { TransactionClient } from "../mgs-sender/transaction.clien";
import { TransferHistoryRepository } from "../transfer-history/repository/transfer-history.query.repository";
import { IHandoverScheduleDocument } from "../handover-schedule.queryside/interfaces/document.interface";
import { OrgchartClient } from "../mgs-sender/orgchart.client";
import { EmployeeClient } from "../mgs-sender/employee.client";
import { StatushistoryService } from "../statusHistory/application/service";
import { existsSync, unlinkSync } from "fs";
import { join } from "path";
import { FileGenerationService } from "./file-generation.service";
import { ConfigService } from "@nestjs/config";
import { StaticAssetService } from "../config/static-asset.service";
import _ = require("lodash");
import moment = require("moment");
import { BaseService, BusinessErrorCode, BusinessException, ErrorService } from "../../../shared-modules";
import { OwnershipCertificateQueryRepository } from "../ownership-certificate/repository/ownership-certificate.query.repository";

@Injectable()
export class PrimaryContractQueryService extends BaseService {
  constructor(
    private readonly repository: PrimaryContractQueryRepository,
    private readonly employeeRepository: EmployeeQueryRepository,
    private readonly mailerClient: MailerClient,
    private readonly notificationClient: NotificationClient,
    private readonly propertyClient: PropertyClient,
    private readonly careClient: CareClient,
    private readonly transactionClient: TransactionClient,
    private readonly handoverQueryService: HandoverQueryService,
    private readonly transferHistoryRepository: TransferHistoryRepository,
    private readonly ownershipCertificateQueryRepository: OwnershipCertificateQueryRepository,
    private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
    private readonly orgchartClient: OrgchartClient,
    private readonly employeeClient: EmployeeClient,
    private readonly statushistoryService: StatushistoryService,
    private readonly fileGenerationService: FileGenerationService,
    private readonly configService: ConfigService,
    private readonly staticAssetService: StaticAssetService,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }

  public async getContract(
    user,
    query,
    isSeeAll: boolean = false
  ): Promise<any> {
    let res = [];
    let response: any = {};
    // let authoredIDs = [];
    let queryEmpIDs = [];
    // let isManager = false;
    // if (user) {
    //   const projects = await this.propertyClient.sendDataPromise(
    //     user,
    //     CmdPatternConst.LISTENER.GET_ALL_PROJECT_BY_USER
    //   );

    //   query["projectIds"] = (projects || []).map((e) => e._id) || [];

    //   // const employee = await this.employeeRepository.findOne({ id: user.id });
    //   const employee = await this.employeeClient.sendDataPromise(
    //     { where: { "account.id": user.id, active: true } },
    //     cmd2.EMPLOYEE.LISTENER.GET_BY_QUERY
    //   );

    //   isManager =
    //     employee && employee.managerAt && employee.managerAt.toString() !== ""
    //       ? true
    //       : false;

    //   authoredIDs =
    //     employee && employee["managerAt"] ? employee["staffIds"] : [];
    //   authoredIDs.push(user.id);
    //   if (isManager) {
    //     queryEmpIDs = authoredIDs;
    //   } else {
    //     queryEmpIDs =
    //       employee && employee["staffIds"] ? employee["staffIds"] : [];
    //     queryEmpIDs.push(user.id);
    //   }
    // } else {
    //   query["projectIds"] = [];
    // }
    query.queryEmpIDs = queryEmpIDs;
    if (!_.isEmpty(query["projectId"])) {
      if (query.projectId.includes(",")) {
        query.projectId = query.projectId.split(",");
      }

      if (Array.isArray(query.projectId)) {
        query["primaryTransaction.project.id"] = { $in: query.projectId };
      } else {
        query["primaryTransaction.project.id"] = query.projectId;
      }
    }
    if (query.type) {
      query["type"] = query.type;
    }

    if (query.isTransferred) {
      query["isTransferred"] = query.isTransferred;
    }

    if (query.searchInLiquidation) {
      query["searchInLiquidation"] = query.searchInLiquidation;
    }

    if (query["page"] || query["pageSize"]) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      res = await this.repository
        .contractFindAll(true, isSeeAll, query, page, pageSize)
        .then((result: any) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          response = {
            page,
            pageSize,
            total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
          return result[0].rows;
        });
    } else {
      res = await this.repository.contractFindAll(false, isSeeAll, query, 0, 0);
    }
    response.rows = res;
    return response;
  }
  async getContractById(user, id: string): Promise<any> {
    let contract = await this.repository.findOne({ id })

    if (!contract)
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0004" },
        HttpStatus.OK
      );

    contract.hasInterest = false;
    contract.interest = {};
    if (
      contract &&
      contract.status === StatusContractEnum.APPROVED &&
      contract.policyPayment &&
      contract.policyPayment.schedule &&
      contract.policyPayment.schedule.installments
    ) {
      if (contract.primaryTransaction.project.setting) {
        // check xem có chậm thanh toán hay không
        contract.interest = this.getInterestOfContract(contract);
      }
    }

    const project = await this.getProjectById(
      contract.primaryTransaction.project.id
    );
    contract.projectType = project?.type;
    const transferHistories = await this.transferHistoryRepository.find({
      "propertyUnit.id": contract.primaryTransaction?.propertyUnit?.id,
    });

    if (contract.syncErpData) {
      contract.statusErp = contract.syncErpData.status;
      contract.statusHistory = await this.statushistoryService.findAll({
        contractid: contract.syncErpData.contractid,
      });

      // Lấy statuscode cuối cùng.
      const syncStatusHistory = contract.statusHistory
        ? contract.statusHistory.find(
          (x) => x.contractid === contract.syncErpData.contractid
        )
        : null;
      const statusCodes = syncStatusHistory
        ? syncStatusHistory.statusHistory
          .filter((x) => !!x.statuscode)
          .map((x) => x.statuscode)
        : [];
      contract.statusCode = statusCodes ? statusCodes.slice(-1)[0] : "";
    }

    contract.transferHistories = transferHistories || [];

    if (contract.deliveryItems.length === 0) {
      const handOver = await this.handoverQueryService.findOneByQuery({
        status: StatusEnum.ACTIVE,
        'project.id': contract.primaryTransaction.project.id
      });

      contract.deliveryItems = handOver?.items || []
    }
    const contractObj = contract;
    contractObj.deliveryConfirmTimes = contract.deliveryHistories?.length ?? 0;
    if (contractObj.cerHandoverStatus === HandoverStatusEnum.Eligible
      || contractObj.cerHandoverStatus === HandoverStatusEnum.scheduled
      || contractObj.cerHandoverStatus === HandoverStatusEnum.Cer_in_process
      || contractObj.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover
      || contractObj.cerHandoverStatus === HandoverStatusEnum.Cer_handed_over) {
      const ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
        isActive: StatusEnum.ACTIVE,
        'project.id': contractObj.primaryTransaction.project.id
      });
      if (ownershipCertificate) {
        contractObj.cerReadyHandoverItems = contractObj.cerReadyHandoverItems?.length === 0 ? ownershipCertificate.itemsForCerReadyHandover : contractObj.cerReadyHandoverItems;
        contractObj.cerInProcessItems = contractObj.cerInProcessItems?.length === 0 ? ownershipCertificate.itemsCerInProcess : contractObj.cerInProcessItems;
        contractObj.eligibleItems = contractObj.eligibleItems?.length === 0 ? ownershipCertificate.itemsForEligible : contractObj.eligibleItems;
        contractObj.ownershipCertificate = ownershipCertificate;
      }
    }
    return contractObj;
  }
  async getContractByIdNew(user, id: string): Promise<any> {
    let contract = await this.repository.findOne({ id })

    if (!contract)
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0004" },
        HttpStatus.OK
      );

    contract.hasInterest = false;
    contract.interest = {};
    if (
      contract &&
      contract.status === StatusContractEnum.APPROVED &&
      contract.policyPayment &&
      contract.policyPayment.schedule &&
      contract.policyPayment.schedule.installments
    ) {
      if (contract.primaryTransaction.project.setting) {
        // check xem có chậm thanh toán hay không
        contract.interest = this.getInterestOfContract(contract);
      }
    }

    const project = await this.getProjectById(
      contract.primaryTransaction.project.id
    );
    contract.projectType = project?.type;
    const transferHistories = await this.transferHistoryRepository.find({
      "propertyUnit.id": contract.primaryTransaction?.propertyUnit?.id,
    });

    if (contract.syncErpData) {
      contract.statusErp = contract.syncErpData.status;
      contract.statusHistory = await this.statushistoryService.findAll({
        contractid: contract.syncErpData.contractid,
      });

      // Lấy statuscode cuối cùng.
      const syncStatusHistory = contract.statusHistory
        ? contract.statusHistory.find(
          (x) => x.contractid === contract.syncErpData.contractid
        )
        : null;
      const statusCodes = syncStatusHistory
        ? syncStatusHistory.statusHistory
          .filter((x) => !!x.statuscode)
          .map((x) => x.statuscode)
        : [];
      contract.statusCode = statusCodes ? statusCodes.slice(-1)[0] : "";
    }

    contract.transferHistories = transferHistories || [];

    if (contract.deliveryItems.length === 0) {
      const handOver = await this.handoverQueryService.findOneByQuery({
        status: StatusEnum.ACTIVE,
        'project.id': contract.primaryTransaction.project.id
      });

      contract.deliveryItems = handOver?.items || []
    }
    const contractObj = contract;
    contractObj.deliveryConfirmTimes = contract.deliveryHistories?.length ?? 0;
    if (contractObj.cerHandoverStatus === HandoverStatusEnum.Eligible
      || contractObj.cerHandoverStatus === HandoverStatusEnum.scheduled
      || contractObj.cerHandoverStatus === HandoverStatusEnum.Cer_in_process
      || contractObj.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover
      || contractObj.cerHandoverStatus === HandoverStatusEnum.Cer_handed_over) {
      const ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
        isActive: StatusEnum.ACTIVE,
        'project.id': contractObj.primaryTransaction.project.id
      });
      if (ownershipCertificate) {
        contractObj.cerReadyHandoverItems = contractObj.cerReadyHandoverItems?.length === 0 ? ownershipCertificate.itemsForCerReadyHandover : contractObj.cerReadyHandoverItems;
        contractObj.cerInProcessItems = contractObj.cerInProcessItems?.length === 0 ? ownershipCertificate.itemsCerInProcess : contractObj.cerInProcessItems;
        contractObj.eligibleItems = contractObj.eligibleItems?.length === 0 ? ownershipCertificate.itemsForEligible : contractObj.eligibleItems;
        contractObj.ownershipCertificate = ownershipCertificate;
      }
    }
    return contractObj;
  }
  async getContractByPropertyUnitId(
    user,
    propertyUnitId: string
  ): Promise<any> {
    return await this.repository.findOne({
      "primaryTransaction.propertyUnit.id": propertyUnitId,
    });
  }

  private getInterestOfContract(contract, query: any = {}) {
    let installments = contract.policyPayment.schedule.installments || [];
    let interestIdx = -1;
    const interestRate =
      contract?.primaryTransaction?.project?.setting?.interestRate || 0;
    const daysPerYear =
      contract?.primaryTransaction?.project?.setting?.daysPerYear || 1;
    const delayDay =
      contract?.primaryTransaction?.project?.setting?.delayDay || 0;
    const onlyWorkday =
      contract?.primaryTransaction?.project?.setting?.onlyWorkday || false;
    let interest: any = {
      title: contract.name, // tiêu đề
      customer: contract.primaryTransaction.customer.personalInfo.name, // khách hàng
      rate: interestRate,
      needTransfer: 0,
      delayFrom: "",
      delayTo: "",
      totalDelayDate: 0,
      amount: 0, // số tiền lãi
    };
    installments.forEach((i, idx) => {
      i.totalTransfered = i.totalTransfered || 0;
      if (interestIdx === -1) {
        // Nếu đợt chưa thanh toán đủ tiền
        if (
          i.paymentDueDate &&
          moment().isAfter(moment(i.paymentDueDate), "day") &&
          i.totalAmount > i.totalTransfered
        ) {
          contract.hasInterest = true;
          interestIdx = idx;

          // số tiền cần thanh toán
          const startDate = query.startDate
            ? moment(query.startDate)
            : moment(i.paymentDueDate).add(1, "days"); // ngày bắt đầu tính lãi
          const endDate = query.endDate ? moment(query.endDate) : moment(); // ngày kết thúc tính lãi

          // tính tổng số ngày đang bị chậm ( đã trừ số ngày cho phép chậm không tính lãi )
          const totalDelayDate = onlyWorkday
            ? CommonUtils.workdayCount(moment(i.paymentDueDate), endDate) - 1
            : CommonUtils.getDateDiffIgnoreTime(
              moment(i.paymentDueDate),
              endDate
            );

          // nếu vượt số ngày không tính lãi, bắt đầu tính lãi
          if (totalDelayDate > delayDay) {
            // tổng số ngày trễ hạn thanh toán tính tới hôm nay
            const interestDate =
              CommonUtils.getDateDiffIgnoreTime(startDate, endDate) + 1;

            interest.name = i.name; // tên đợt
            interest.needTransfer = i.totalAmount - i.totalTransfered; // số tiền gốc
            interest.delayFrom = startDate.format("YYYY-MM-DD"); // tính lãi từ ngày
            interest.delayTo = endDate.format("YYYY-MM-DD"); // tính lãi đến ngày
            interest.totalDelayDate = interestDate; // số ngày trễ hạn thanh toán

            // lấy các phiếu thu ĐÃ THANH TOÁN của đợt nằm trong khoảng cần tính
            const receipts = i.receipts.filter(
              (r) =>
                r.status === TransactionStatusEnum.transfered &&
                moment(r.receiptDate).isSameOrBefore(endDate)
            );

            // nếu có tồn tại phiếu thu, tính lãi của từng mốc
            if (receipts.length > 0) {
              let total = i.totalAmount;
              let dateFrom = moment(interest.delayFrom).clone();
              receipts.forEach((receipt) => {
                // lấy ngày thanh toán phiếu thu
                const receiptDate = moment(receipt.receiptDate);
                if (receiptDate.isSameOrAfter(dateFrom)) {
                  // tính tiền từ mốc trước cho đến mốc phiếu thu sau
                  const totalDays = CommonUtils.getDateDiffIgnoreTime(
                    dateFrom,
                    receiptDate
                  );
                  interest.amount += CommonUtils.getTotalInterest(
                    total,
                    interestRate,
                    daysPerYear,
                    totalDays
                  );
                  dateFrom = moment(receipt.receiptDate).clone();
                }
                // trừ số tiền tổng đi cho mốc sau
                total -= receipt.amount;
              });
              // sau khi không còn phiếu nào, tính tiền từ phiếu thu cuối => ngày kết thúc
              const totalDays =
                CommonUtils.getDateDiffIgnoreTime(dateFrom, endDate) + 1;
              interest.amount += CommonUtils.getTotalInterest(
                total,
                interestRate,
                daysPerYear,
                totalDays
              );
            } else {
              // nếu không tồn tại phiếu thu, tính lãi của tất cả các ngày chậm
              interest.amount = CommonUtils.getTotalInterest(
                i.totalAmount - i.totalTransfered,
                interestRate,
                daysPerYear,
                interestDate
              );
            }
            interest.amount = Math.ceil(interest.amount);
          }
        }
      }
    });
    return interest;
  }

  async calculateInterestById(user, id, query) {
    let contract = await this.repository.findOne({ id });
    contract.interest = {};
    if (
      contract &&
      contract.status === StatusContractEnum.APPROVED &&
      contract.policyPayment &&
      contract.policyPayment.schedule &&
      contract.policyPayment.schedule.installments &&
      contract.primaryTransaction.project.setting
    ) {
      // check xem có chậm thanh toán hay không
      contract.interest = this.getInterestOfContract(contract, query);
    }
    return contract.interest;
  }

  async getProjectById(id: string): Promise<any> {
    return await this.propertyClient.sendDataPromise(
      { id },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
    );
  }
  async getProjectByIds(ids: string[], _fields?: any): Promise<any> {
    return await this.propertyClient.sendDataPromise(
      { ids, _fields },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );
  }
  async getContractByDiscountPolicy(user, id: string): Promise<any> {
    const contracts = await this.repository.findContractByDiscountPolicy(id);
    return {
      id,
      total: contracts.length,
    };
  }

  public async getDepositForPurchaseContract(
    user,
    query,
    isSeeAll: boolean = false
  ): Promise<any> {
    let res = [];
    let response: any = {};
    query.status = StatusContractEnum.APPROVED;
    query.type = ContractEnum.DEPOSIT;
    if (query.isCheckProject) {
      const projectIds = await this.propertyClient.sendDataPromise(
        { id: user.id },
        CmdPatternConst.PROJECT.GET_PROJECT_BY_ACCOUNTANT
      );
      query["primaryTransaction.project.id"] = { $in: projectIds };
    }
    if (!_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"])) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      res = await this.repository
        .contractFindAll(true, isSeeAll, query, page, pageSize)
        .then((result: any) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          response = {
            page,
            pageSize,
            total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
          return result[0].rows;
        });
    } else {
      res = await this.repository.contractFindAll(false, isSeeAll, query, 0, 0);
    }
    response.rows = res;
    return response;
  }

  async getContractReminder(): Promise<any> {
    const firstReminder = this.configService
      .get("FIRST_DEBT_REMINDER")
      .split(",");
    const otherReminder = this.configService
      .get("DEBT_REMINDED_BEFORE_DATE")
      .split(",");
    const agg = [
      {
        $match: {
          "policyPayment.schedule.installments.0": { $exists: true },
          isDebtRemind: true,
          status: {
            $in: [
              StatusContractEnum.APPROVED,
              StatusContractEnum.ACCOUNTANT_WAITING,
            ],
          },
        },
      },
      {
        $addFields: {
          firstRemindDateArray: {
            $cond: {
              if: {
                $lte: [
                  "$$ROOT.primaryTransaction.project.setting.dateBeforeFirstDebtRemind",
                  [],
                ],
              },
              then: firstReminder.map((i) => parseInt(i)),
              else: "$$ROOT.primaryTransaction.project.setting.dateBeforeFirstDebtRemind",
            },
          },
        },
      },
      {
        $addFields: {
          firstRemindDateArray: {
            $map: {
              input: "$firstRemindDateArray",
              as: "date",
              in: {
                $dateToString: {
                  format: "%Y-%m-%d",
                  date: {
                    $convert: {
                      input: {
                        $add: [
                          new Date(),
                          {
                            $multiply: ["$$date", 86400000],
                          },
                        ],
                      },
                      to: "date",
                    },
                  },
                  timezone: "+07",
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          otherRemindDateArray: {
            $cond: {
              if: {
                $lte: [
                  "$$ROOT.primaryTransaction.project.setting.dateBeforeNextDebtRemind",
                  [],
                ],
              },
              then: otherReminder.map((i) => parseInt(i)),
              else: "$$ROOT.primaryTransaction.project.setting.dateBeforeNextDebtRemind",
            },
          },
        },
      },
      {
        $addFields: {
          otherRemindDateArray: {
            $map: {
              input: "$otherRemindDateArray",
              as: "date",
              in: {
                $dateToString: {
                  format: "%Y-%m-%d",
                  date: {
                    $convert: {
                      input: {
                        $add: [
                          new Date(),
                          {
                            $multiply: ["$$date", 86400000],
                          },
                        ],
                      },
                      to: "date",
                    },
                  },
                  timezone: "+07",
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          installDate: {
            $map: {
              input: "$policyPayment.schedule.installments",
              as: "installment",
              in: {
                $cond: {
                  if: { $lte: ["$$installment.paymentDueDate", null] },
                  then: {
                    $dateToString: {
                      format: "",
                      date: {
                        $convert: {
                          input: new Date(),
                          to: "date",
                        },
                      },
                      timezone: "+07",
                    },
                  },
                  else: {
                    $dateToString: {
                      format: "%Y-%m-%d",
                      date: {
                        $convert: {
                          input: "$$installment.paymentDueDate",
                          to: "date",
                        },
                      },
                      timezone: "+07",
                    },
                  },
                },
              },
            },
          },
        },
      },
      {
        $match: {
          $expr: {
            $or: [
              {
                $gt: [
                  {
                    $size: {
                      $setIntersection: [
                        "$installDate",
                        "$otherRemindDateArray",
                      ],
                    },
                  },
                  0,
                ],
              },
              {
                $gt: [
                  {
                    $size: {
                      $setIntersection: [
                        "$installDate",
                        "$firstRemindDateArray",
                      ],
                    },
                  },
                  0,
                ],
              },
            ],
          },
        },
      },
    ];

    let contracts: any = await this.repository.findPrimaryContractReminder(agg);
    let projectIds = contracts.map((contract: any) => {
      return contract?.primaryTransaction?.project?.id;
    });
    projectIds = [...new Set(projectIds)];
    let _fields: any = {
      id: 1,
      emailTemplate: 1,
      smsTemplate: 1,
    };
    let templates = await this.getProjectByIds(projectIds, _fields);

    contracts = contracts.map((contract: any) => {
      const installments = contract.policyPayment.schedule.installments;
      installments.map(async (item, idx) => {
        const paymentAmount =
          installments[idx]?.totalAmount - installments[idx]?.totalTransfered;
        const isFirst = contract.firstRemindDateArray.includes(
          moment(item.paymentDueDate).format("YYYY-MM-DD")
        );
        const isOther = contract.otherRemindDateArray.includes(
          moment(item.paymentDueDate).format("YYYY-MM-DD")
        );
        if (paymentAmount > 0 && (isFirst || isOther)) {
          if ((idx == 0 && isFirst) || (idx != 0 && isOther)) {
            if (templates.length) {
              let temp = templates.find(
                (p) => p.id === contract?.primaryTransaction?.project?.id
              );
              if (temp) {
                let emailTemplate: any;
                let smsTemplate: any;

                if (idx == 0) {
                  emailTemplate = temp.emailTemplate
                    .reverse()
                    .find((e) => e.type === CommonConst.EMAIL_THANH_TOAN_DOT_1);
                  smsTemplate = temp.smsTemplate
                    .reverse()
                    .find(
                      (e) => e.type === CommonConst.SMS_NHAC_NO_THANH_TOAN_DOT_1
                    );
                } else {
                  emailTemplate = temp.emailTemplate
                    .reverse()
                    .find(
                      (e) =>
                        e.type === CommonConst.EMAIL_THANH_TOAN_DOT_TIEP_THEO
                    );
                  smsTemplate = temp.smsTemplate
                    .reverse()
                    .find(
                      (e) =>
                        e.type ===
                        CommonConst.SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO
                    );
                }

                if (emailTemplate) {
                  const dataEmail = {
                    emailTo:
                      contract?.primaryTransaction?.customer?.personalInfo
                        ?.email,
                    productCode:
                      contract?.primaryTransaction?.propertyUnit?.code,
                    projectName: contract?.primaryTransaction?.project?.name,
                    paymentAmount: paymentAmount.toLocaleString(),
                    date: moment(item.paymentDueDate).format("DD/MM/YYYY"),
                    installment: idx,
                    installmentName: installments[idx]?.name,
                    htmlContentMail: emailTemplate.body,
                    htmlSubjectMail: emailTemplate.subject,
                  };
                  // TODO: add notiSystem => schedule
                  this.mailerClient.sendData(
                    dataEmail,
                    CmdPatternConst.LISTENER.PRIMARY_CONTRACT_QUERY_SEND_MAIL
                  );
                }
                if (smsTemplate) {
                  const dataSMS = {
                    productCode:
                      contract?.primaryTransaction?.propertyUnit?.code,
                    projectName: contract?.primaryTransaction?.project?.name,
                    amount: paymentAmount.toLocaleString(),
                    date: moment(item.paymentDueDate).format("DD/MM/YYYY"),
                    phone:
                      contract?.primaryTransaction?.customer?.personalInfo
                        ?.phone,
                    contentSMS: smsTemplate.body,
                  };
                  this.notificationClient.sendDataSubscribe(
                    dataSMS,
                    CmdPatternConst.SMS.PRIMARY_CONTRACT_PAYMENT_REMINDER
                  );
                }
              }
            }

            // send notification for care customer
            const identityNumber =
              contract?.primaryTransaction?.customer?.identities[0].value;
            const careCustomer = await this.careClient.sendDataPromise(
              { identityNumber },
              CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY
            );
            if (careCustomer && careCustomer.id) {
              this.notificationClient.createNotificationCare(
                "care_PrimaryContract_PaymentReminder",
                null,
                careCustomer.id,
                CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
                contract.id,
                {
                  productCode: contract?.primaryTransaction?.propertyUnit?.code,
                  projectName: contract?.primaryTransaction?.project?.name,
                  amount: paymentAmount.toLocaleString(),
                  date: moment(item.paymentDueDate).format("DD/MM/YYYY"),
                }
              );
            }
          }
        }
      });
    });
    return { "email was sent": contracts.length };
  }
  async getTransactionHistory(customer: any, query: any, getDetail: boolean = true) {
    // const customer = await this.careClient.sendDataPromise(
    //   { userId: user.id },
    //   CmdPatternConst.CARE.GET_CUSTOMER_BY_ID
    // );

    // if (
    //   !customer ||
    //   customer.verifyAccount !== VerifyAccountStatusEnum.APPROVED
    // ) {
    //   throw new BadRequestException({
    //     errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "customer"),
    //   });
    // }

    let identitiesArr: string[] = [];

    if (customer.identities?.value) {
      identitiesArr.push(customer.identities.value);
    }

    const values = customer.personalInfo?.identities
      ?.filter(i => !!i?.value)
      ?.map(i => i.value) || [];

    identitiesArr.push(...values);


    // lấy tất cả hợp đồng của KH
    let _query = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
          StatusContractEnum.LIQUIDATED,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.customer.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer.identities.value": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identities.value": {
                $in: identitiesArr,
              },
            },
          ],
        },
      ],
      sort: "-modifiedDate",
    };

    let models: any;
    // let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);
    // if (isPaging) {
    const page: number = parseInt(query["page"]) || 1;
    const pageSize: number = parseInt(query["pageSize"]) || 10;
    const result: any[] = await this.repository.findPrimaryContractForCustomer(_query, true, page, pageSize);

    const data = result?.[0];
    const rows = data?.rows || [];
    const totalCountData = data?.totalCount?.[0];
    const total: number = totalCountData?.count || 0;

    const totalPages = Math.ceil(total / pageSize);

    models = {
      rows,
      page,
      pageSize,
      total,
      totalPages,
    };
    // } else {
    //   models = await this.repository.findPrimaryContractForCustomer(_query);
    // }

    // const data = models.rows;

    // if (!getDetail) {
    //   return data;
    // }

    // lọc unique dự án và lấy thông tin
    let projectIds = models.rows.map(
      (contract) => contract.primaryTransaction.project.id
    );
    projectIds = [...new Set(projectIds)];
    const projects = await this.propertyClient.sendDataPromise(
      { ids: projectIds },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );

    // lấy thông tin bàn giao
    const handOvers = await this.handoverQueryService.findAllByQuery({
      status: true,
      "project.id": {
        $in: projectIds,
      },
      // sessions: {
      //   $elemMatch: {
      //     status: true,
      //     startDate: { $lte: new Date() },
      //     endDate: { $gte: new Date() }
      //   }
      // }
    });

    let transactionCodes = [];
    ((models.rows as any[]) || []).forEach((item, index) => {
      let installments = item?.policyPayment?.schedule?.installments || [];
      installments.forEach((i) => {
        (i.receipts || []).forEach((r, index) => {
          transactionCodes.push(r.code);
        });
      });
    });

    const transactions =
      ((await this.transactionClient.sendDataPromise(
        transactionCodes,
        CmdPatternConst.LISTENER.GET_TRANSACTIONS_BY_TRANSACTION_CODES
      )) as any[]) || [];
    const transactionsMapping = transactions.reduce((pv, cu) => {
      pv[cu.code] = cu;
      return pv;
    }, {});

    // transform lại data
    const transformedData = this.transformContractForCustomer(
      models.rows,
      projects,
      handOvers,
      transactionsMapping
    );
    return transformedData;
  }
  async getByCustomer(user: any, query: any, getDetail: boolean = true) {
    const customer = await this.careClient.sendDataPromise(
      { userId: user.id },
      CmdPatternConst.CARE.GET_CUSTOMER_BY_ID
    );

    if (
      !customer ||
      customer.verifyAccount !== VerifyAccountStatusEnum.APPROVED
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "customer"),
      });
    }

    const identities = customer.identities || [];

    if (identities.length < 1) {
      return [];
    }

    const identitiesArr = identities.map((identity) => {
      return identity.value;
    });

    // lấy tất cả hợp đồng của KH
    let _query = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
          StatusContractEnum.LIQUIDATED,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.customer.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer.identities.value": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identities.value": {
                $in: identitiesArr,
              },
            },
          ],
        },
      ],
      sort: "-modifiedDate",
    };

    let models: any;
    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      models = await this.repository
        .findPrimaryContractForCustomer(_query, true, page, pageSize)
        .then((result: any[]) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          return {
            rows: result[0].rows,
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
        });
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
    }

    const data = isPaging ? models.rows : models;

    if (!getDetail) {
      return data;
    }
    // lọc unique dự án và lấy thông tin
    let projectIds = data.map(
      (contract) => contract.primaryTransaction.project.id
    );
    projectIds = [...new Set(projectIds)];
    const projects = await this.propertyClient.sendDataPromise(
      { ids: projectIds },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );

    // lấy thông tin bàn giao
    const handOvers = await this.handoverQueryService.findAllByQuery({
      status: true,
      "project.id": {
        $in: projectIds,
      },
      // sessions: {
      //   $elemMatch: {
      //     status: true,
      //     startDate: { $lte: new Date() },
      //     endDate: { $gte: new Date() }
      //   }
      // }
    });

    let transactionCodes = [];
    ((data as any[]) || []).forEach((item, index) => {
      let installments = item?.policyPayment?.schedule?.installments || [];
      installments.forEach((i) => {
        (i.receipts || []).forEach((r, index) => {
          transactionCodes.push(r.code);
        });
      });
    });

    const transactions =
      ((await this.transactionClient.sendDataPromise(
        transactionCodes,
        CmdPatternConst.LISTENER.GET_TRANSACTIONS_BY_TRANSACTION_CODES
      )) as any[]) || [];
    const transactionsMapping = transactions.reduce((pv, cu) => {
      pv[cu.code] = cu;
      return pv;
    }, {});

    // transform lại data
    const transformedData = this.transformContractForCustomer(
      data,
      projects,
      handOvers,
      transactionsMapping
    );
    return isPaging
      ? Object.assign(models, { rows: transformedData })
      : transformedData;
  }

  async getAllCustomerByProjectId(lstProjectId): Promise<any> {
    const _query: any = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
          StatusContractEnum.LIQUIDATED,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.project.id": { $in: lstProjectId },
            },
          ],
        },
      ],
    };

    const data =
      await this.repository.findCustomerIdentitiesPrimaryContractByProjectId(
        _query
      );
    let lstCustomerIdentities: any[] = [];
    if (data.length > 0) {
      lstCustomerIdentities = data[0].identties;
    }
    return lstCustomerIdentities;
  }

  private async transformContractForCustomer(
    models,
    projects,
    handOvers,
    transactionsMapping = {}
  ) {
    const contractsMap = {};
    const modelsSyncErpData = models.filter((x) => !!x.syncErpData);
    const syncHistorys: any = await this.statushistoryService.findByQuery({
      "statusHistory.contractid": {
        $in: modelsSyncErpData.map((x) => x.syncErpData.contractid),
      },
    });
    models.forEach((model) => {
      const project: any = projects.find(
        (p) => p.id === model.primaryTransaction.project.id
      );
      let status = "waiting";
      let installment = -1;
      let installments = model?.policyPayment?.schedule?.installments || [];
      const { imageUrl, ward, province, district, city, address, type } =
        project;

      let interest = {};

      const {
        _id,
        code,
        price,
        bedroom,
        toilet,
        area,
        block,
        floor,
        direction,
      } = model.primaryTransaction.propertyUnit;
      const propertyUnitstatus = model.primaryTransaction.propertyUnit.status
      let deliveryItems = model.deliveryItems || [];
      let ownerStatus = "owner";

      if (model && model.status === StatusContractEnum.LIQUIDATED) {
        // đã thanh lý
        ownerStatus = model.liquidation
          ? model.liquidation.type
          : LiquidationTypeEnum.TRANSFER;
      }

      if (
        model.handoverStatus &&
        model.handoverStatus !== HandoverStatusEnum.init &&
        model.handoverStatus !== HandoverStatusEnum.scheduled
      ) {
        status = model.handoverStatus;
        installment = model.deliveryHistories.length + 1;
      } else {
        if (
          model &&
          (model.status === StatusContractEnum.APPROVED ||
            model.status === StatusContractEnum.LIQUIDATED)
        ) {
          if (installments.length > 0) {
            // get interest from contract;
            interest = this.getInterestOfContract(model);

            let totalTransfered = 0;
            let installmentChoose = 0;
            // check xem có đợt thanh toán nào chưa thanh toán đủ tiền không
            installments.forEach((i, idx) => {
              i.totalTransfered = i.totalTransfered || 0;
              if (installment === -1 && i.totalAmount > i.totalTransfered) {
                status = "payment";
                installment = idx;
              }
              totalTransfered += i.totalTransfered;
            });
            if (installment === -1) {
              status = "paymentSuccess";
              installment = installments.length;
            }
            const paymentPercent = model.paymentPercent
              ? model.paymentPercent
              : price != 0
                ? Math.floor((totalTransfered * 100) / price)
                : 0;
            // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
            const handover = handOvers.find(
              (h) => h.project.id === model.primaryTransaction.project.id
            );
            if (handover) {
              let minPercent =
                handover.paymentPercent || HandoverConst.CH_MIN_PERCENT;
              if (paymentPercent >= minPercent) {
                if (model.handoverStatus === HandoverStatusEnum.scheduled) {
                  status = HandoverStatusEnum.scheduled;
                } else {
                  status = HandoverStatusEnum.init;
                }
                deliveryItems = handover.items;
              }
            }
          }
        }
      }

      // lọc các đợt đã thanh toán
      // let transferedInstallments = installments.filter(i => {
      //   return i.totalTransfered > 0
      // });

      ///change get all các đượt thanh toán
      let transferedInstallments = [...installments];

      // lọc các phiếu thu đã được duyệt
      transferedInstallments = transferedInstallments.map((i) => {
        //i.receipts = i.receipts.filter(r => r.status === TransactionStatusEnum.transfered);
        let filteredReceipts = [];
        if (i.receipts) {
          for (let r of i.receipts) {
            const transaction = transactionsMapping[r.code] || {};
            if (r.status === TransactionStatusEnum.transfered) {
              r.state = transaction.state;
              filteredReceipts.push(r);
            }
          }
          i.receipts = filteredReceipts;
        }
        return i;
      });

      let totalAmount = 0;
      if (transferedInstallments && transferedInstallments.length > 0) {
        transferedInstallments.forEach((item: any) => {
          if (item.totalAmount) {
            totalAmount = totalAmount + item.totalAmount;
          }
        });
      }

      // lấy statuscode gần nhất
      let statusCode;
      if (model.syncErpData && model.syncErpData.contractid) {
        const syncHistory = syncHistorys
          ? syncHistorys.find(
            (x) => x.contractid === model.syncErpData.contractid
          )
          : [];
        const stthistory = syncHistory
          ? syncHistory.statusHistory
            .filter((x) => !!x.statuscode)
            .map((x) => x.statuscode)
          : [];
        statusCode = stthistory ? stthistory.slice(-1)[0] : "";
      }

      const result = {
        id: model.id,
        code: model.code,
        project: {
          id: model.primaryTransaction.project.id,
          name: model.primaryTransaction.project.name,
          priceFrom: project.priceFrom
            ? project.priceFrom["$numberDecimal"]
            : "",
          imageUrl,
          ward,
          province,
          district,
          city,
          address,
          type,
          interestRate: project.setting ? project.setting.interestRate : null,
          daysPerYear: project.setting ? project.setting.daysPerYear : null,
          delayDay: project.setting ? project.setting.delayDay : null,
          onlyWorkday: project.setting ? project.setting.onlyWorkday : null,
        },
        propertyUnit: {
          _id,
          code,
          price,
          bedroom: parseInt(bedroom),
          toilet,
          area,
          status: propertyUnitstatus,
          block,
          floor,
          direction,
        },
        status: {
          code: status,
          installment: installment,
          ownerStatus: ownerStatus,
        },
        installments: transferedInstallments,
        totalTransfered: transferedInstallments.reduce((prev, cur) => {
          return prev + cur.totalTransfered;
        }, 0),
        depositConfirm: model.depositConfirmFromCustomer || false,
        transferConfirm: model.transferConfirmFromCustomer || false,
        files: model.files || [],
        deliveryItems: deliveryItems,
        interest: interest,
        interestCalculations: model.interestCalculations,
        policyPayment: model.policyPayment,
        policyDiscounts: model.policyDiscounts,
        totalAmount: totalAmount,
        liquidation: model.liquidation,
        liquidate: model.liquidate,
        statusCode: statusCode,
        contractStatus: model.status,
      };

      // propertyUnitId - contract type
      const keyMap = `${_id}_${model.type}`;
      if (
        model.type === ContractEnum.PURCHASE ||
        model.type === ContractEnum.RENT
      ) {
        if (contractsMap[`${_id}_${ContractEnum.DEPOSIT}`]) {
          contractsMap[`${_id}_${ContractEnum.DEPOSIT}`] = result;
        } else {
          contractsMap[`${_id}_${ContractEnum.PURCHASE}`] = result;
        }
      } else if (model.type === ContractEnum.DEPOSIT) {
        if (
          !contractsMap[`${_id}_${ContractEnum.PURCHASE}`] &&
          !contractsMap[`${_id}_${ContractEnum.RENT}`]
        ) {
          contractsMap[keyMap] = result;
        }
      } else {
        contractsMap[keyMap] = result;
      }
    });

    return Object.keys(contractsMap).map((key) => contractsMap[key]);
  }
  //danh sách căn hộ đã lên lịch, xác nhận bàn giao
  async getHandoverByProject(handoverId, query) {
    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: StatusEnum.ACTIVE,
    });

    if (!handOverSetting) {
      return this.getResponse("PRIMARYCONTRACT0001");
    }

    const project = await this.getProjectById(handOverSetting.project.id);

    if (!project) {
      return this.getResponse("PRIMARYCONTRACT0002");
    }

    // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
    const minPercent =
      handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    let andQuery: any = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.search)) {
      andQuery = andQuery.concat({
        $or: [
          {
            "primaryTransaction.customer.personalInfo.name": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.email": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.phone": {
              $regex: query.search,
              $options: "i",
            },
          },
        ],
      });
    }

    if (!_.isEmpty(query.floors)) {
      const lstFloor = query.floors.includes(',')
        ? query.floors.split(',').map(id => id.trim())
        : [query.floors.trim()];
      andQuery = andQuery.concat({
        "primaryTransaction.propertyUnit.floor": { $in: lstFloor },
      });
    }

    if (!_.isEmpty(query.blocks)) {
      const lstBlock = query.blocks.includes(',')
        ? query.blocks.split(',').map(id => id.trim())
        : [query.blocks.trim()];
      andQuery = andQuery.concat({
        "primaryTransaction.propertyUnit.block": { $in: lstBlock },
      });
    }
    if (!_.isEmpty(query.rooms)) {
      const lstRoom = query.rooms.includes(',')
        ? query.rooms.split(',').map(id => id.trim())
        : [query.rooms.trim()];
      andQuery = andQuery.concat({
        "primaryTransaction.propertyUnit.shortCode": { $in: lstRoom },
      });
    }

    // lấy tất cả hợp đồng đủ điều kiện bàn giao của project
    let _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      $and: andQuery,
      sort: "handoverStatus,-modifiedDate",
      _fields: `
        id,
        primaryTransaction.propertyUnit.code,
        primaryTransaction.customer.personalInfo,
        primaryTransaction.customer.info,
        primaryTransaction.customer.employee.name,
        deliveryResult,
        handoverStatus,
        cerHandoverStatus,
        deliveryDate,
        isSendDelivery,
        isCerSendDelivery,
        deliveryItems,
        eligibleItems,
        cerReadyHandoverItems,
        cerInProcessItems,
        cerHandoverSchedule,
        handoverSchedule
      `,
      paymentPercent: { $gte: minPercent },
    };

    if (!_.isEmpty(query.handoverStatus)) {
      const lstHandoverStatus = query.handoverStatus.includes(',')
        ? query.handoverStatus.split(',').map(id => id.trim())
        : [query.handoverStatus.trim()];
      andQuery = andQuery.concat({
        handoverStatus: { $in: lstHandoverStatus },
      });
    }
    if (!_.isEmpty(query.cerHandoverStatus)) {
      const lstCerHandoverStatus = query.cerHandoverStatus.includes(',')
        ? query.cerHandoverStatus.split(',').map(id => id.trim())
        : [query.cerHandoverStatus.trim()];
      andQuery = andQuery.concat({
        cerHandoverStatus: { $in: lstCerHandoverStatus },
      });
    }

    let models: any;
    const page: number = parseInt(query["page"]) || 1;
    const pageSize: number = parseInt(query["pageSize"]) || 10;
    models = await this.repository
      .findPrimaryContractForCustomerNew(_query, true, page, pageSize)
      .then((result: any[]) => {
        const total = result[0].totalCount[0]
          ? result[0].totalCount[0].count
          : 0;
        return {
          rows: result[0].rows.map((r) => {
            return this.transformHandoverItemNew(r, project, handOverSetting);
          }),
          page,
          pageSize,
          total: total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
        };
      });

    //renderTemplate mail
    if (models.rows.length > 0) {
      // lấy thông tin bàn giao
      const ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
        isActive: StatusEnum.ACTIVE,
        'project.id': handOverSetting.project.id
      });
      if (ownershipCertificate) {
        models.rows.forEach(row => {
          const data = {
            customer: {
              name: row.primaryTransaction?.customer?.personalInfo?.name
            },
            property: {
              code: row.primaryTransaction?.propertyUnit?.code
            },
            project: {
              name: row.project?.name
            },
            hotline: ownershipCertificate.hotline
          }
          row.emailForCerReadyHandover = this.renderTemplate(ownershipCertificate.emailForCerReadyHandover?.emailTemplate || "", data);
          row.emailCerHandedOver = this.renderTemplate(ownershipCertificate.emailCerHandedOver?.emailTemplate || "", data);
          row.emailForEligible = this.renderTemplate(ownershipCertificate.emailForEligible?.emailTemplate || "", data);
        });
      }

    }
    return models;
  }
  async getHandoverByProjectNew(handoverId, query) {
    // Lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: StatusEnum.ACTIVE,
    });
    if (!handOverSetting) return this.getResponse("PRIMARYCONTRACT0001");

    // Lấy project
    const project = await this.getProjectById(handOverSetting.project.id);
    if (!project) return this.getResponse("PRIMARYCONTRACT0002");

    // Build query filter
    const andQuery = this.buildAndQuery(query);
    const minPercent = handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    const _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      $and: andQuery,
      sort: "handoverStatus,-modifiedDate",
      _fields: `
      id,
      primaryTransaction.propertyUnit.code,
      primaryTransaction.customer.personalInfo,
      primaryTransaction.customer.info,
      primaryTransaction.customer.employee.name,
      deliveryResult,
      handoverStatus,
      cerHandoverStatus,
      deliveryDate,
      isSendDelivery,
      isCerSendDelivery,
      deliveryItems,
      eligibleItems,
      cerReadyHandoverItems,
      cerInProcessItems,
      cerHandoverSchedule,
      handoverSchedule
    `,
      paymentPercent: { $gte: minPercent },
    };

    // Lấy data
    const page = parseInt(query.page) || 1;
    const pageSize = parseInt(query.pageSize) || 10;

    const models = await this.repository
      .findPrimaryContractForCustomerNew(_query, true, page, pageSize)
      .then((result: any[]) => {
        const total = result[0].totalCount[0]
          ? result[0].totalCount[0].count
          : 0;
        return {
          rows: result[0].rows.map((r) => {
            return this.transformHandoverItemNew(r, project, handOverSetting);
          }),
          page,
          pageSize,
          total: total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
        };
      });

    // Gắn template email
    await this.attachEmailTemplates(models.rows, project, handOverSetting);

    return models;
  }

  private escapeRegex(text) {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  }

  async getHandoverListPropertyUnit(query: any) {
    const listPropertyUnitCode = await this.transactionClient.sendDataPromise({}, CmdPatternConst.LISTENER.GET_ALL_TRANSACTION_HAS_EAPP_NUMBER);

    const page = parseInt(query?.page) || 1;
    const pageSize = parseInt(query?.pageSize) || 10;
    const searchText = query?.search?.trim();
    const hasSearch = !!searchText?.length;

    const matchCondition: any = {
      status: StatusContractEnum.APPROVED,
      handoverStatus: HandoverStatusEnum.handed,
      'primaryTransaction.propertyUnit.code': { $nin: listPropertyUnitCode || [] },
    };

    if (hasSearch) {
      const search = this.escapeRegex(searchText);
      const regex = new RegExp('.*' + search + '.*', 'i');
      matchCondition.$and = [
        { 'primaryTransaction.propertyUnit.code': { $nin: listPropertyUnitCode || [] } },
        { $or: [{ 'primaryTransaction.propertyUnit.code': { $regex: regex } }] },
      ];
    }

    const basePipeline: any[] = [{ $match: matchCondition },
    {
      $lookup: {
        from: "handovers",
        let: { contractProjectId: "$primaryTransaction.project.id" },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ["$project.id", "$$contractProjectId"] },
              status: 1
            }
          }
        ],
        as: "handoverInfo"
      }
    },
    { $unwind: { path: "$handoverInfo", preserveNullAndEmptyArrays: true } }
    ];

    if (hasSearch) {
      const escaped = this.escapeRegex(searchText);
      basePipeline.push({
        $addFields: {
          matchScore: {
            $max: [
              {
                $switch: {
                  branches: [
                    { case: { $eq: ['$primaryTransaction.propertyUnit.code', searchText] }, then: 2 },
                    { case: { $regexMatch: { input: '$primaryTransaction.propertyUnit.code', regex: `^${escaped}$`, options: 'i' } }, then: 1.5 },
                    { case: { $regexMatch: { input: '$primaryTransaction.propertyUnit.code', regex: escaped, options: 'i' } }, then: 1 },
                  ],
                  default: 0,
                },
              }
            ]
          }
        }
      });
      basePipeline.push({ $sort: { matchScore: -1, createdDate: -1 } });
    } else {
      basePipeline.push({ $sort: { createdDate: -1 } });
    }

    const paginatedPipeline = [
      ...basePipeline,
      {
        $project: {
          _id: 0,
          id: 1,
          code: 1,
          money: "$feeAdvanceAmount",
          propertyCode: "$primaryTransaction.propertyUnit.code",
          customer: "$primaryTransaction.customer",
          project: "$primaryTransaction.project",
          customerName: {
            $cond: {
              if: { $eq: ['$primaryTransaction.customer.type', 'individual'] },
              then: '$primaryTransaction.customer.personalInfo.name',
              else: '$primaryTransaction.customer.company.name',
            },
          },
        }
      },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
    ];

    const countPipeline = [...basePipeline, { $count: "total" }];

    try {
      const [rows, countResult] = await Promise.all([
        this.repository.aggregate(paginatedPipeline),
        this.repository.aggregate(countPipeline),
      ]);
      const total = countResult[0]?.total || 0;
      return {
        rows,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      throw new BusinessException(BusinessErrorCode.COME0001, [
        { message: error?.message },
      ]);
    }
  }

  private parseListParam(param: string): string[] {
    if (_.isEmpty(param)) return [];
    return param.includes(',') ? param.split(',').map(id => id.trim()) : [param.trim()];
  }
  private buildAndQuery(query: any) {
    const andQuery: any[] = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.search)) {
      const regexSearch = { $regex: query.search, $options: "i" };
      andQuery.push({
        $or: [
          { "primaryTransaction.customer.personalInfo.name": regexSearch },
          { "primaryTransaction.customer.personalInfo.email": regexSearch },
          { "primaryTransaction.customer.personalInfo.phone": regexSearch },
        ],
      });
    }

    const filterMap = {
      floors: "primaryTransaction.propertyUnit.floor",
      blocks: "primaryTransaction.propertyUnit.block",
      rooms: "primaryTransaction.propertyUnit.shortCode",
      handoverStatus: "handoverStatus",
      cerHandoverStatus: "cerHandoverStatus",
    };

    for (const key in filterMap) {
      const values = this.parseListParam(query[key]);
      if (values.length) andQuery.push({ [filterMap[key]]: { $in: values } });
    }

    return andQuery;
  }
  private async attachEmailTemplates(rows: any[], project: any, handOverSetting: any) {
    if (!rows.length) return;
    const ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
      isActive: StatusEnum.ACTIVE,
      'project.id': handOverSetting.project.id
    });
    if (!ownershipCertificate) return;

    rows.forEach(row => {
      const data = {
        customer: { name: row.primaryTransaction?.customer?.personalInfo?.name },
        property: { code: row.primaryTransaction?.propertyUnit?.code },
        project: { name: row.project?.name },
        hotline: ownershipCertificate.hotline
      };
      row.emailForCerReadyHandover = this.renderTemplate(ownershipCertificate.emailForCerReadyHandover?.emailTemplate || "", data);
      row.emailCerHandedOver = this.renderTemplate(ownershipCertificate.emailCerHandedOver?.emailTemplate || "", data);
      row.emailForEligible = this.renderTemplate(ownershipCertificate.emailForEligible?.emailTemplate || "", data);
    });
  }
  async reportHandoverByProject(handoverId, query) {
    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: StatusEnum.ACTIVE,
    });

    if (!handOverSetting) {
      return this.getResponse("PRIMARYCONTRACT0001");
    }

    const project = await this.getProjectById(handOverSetting.project.id);

    if (!project) {
      return this.getResponse("PRIMARYCONTRACT0002");
    }

    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);

    // let sessions = (handOverSetting.sessions || []).filter(ses => ses.status);
    // if(!_.isEmpty(query["session"])) {
    //   sessions = sessions.filter(ses => ses.name === query["session"])
    // }

    // if(sessions.length === 0) {
    //     return [];
    // }

    // lấy ra các mã sản phẩm thuộc các đợt bàn giao đang active
    // let productCodes = [];
    // sessions.map(ses => {
    //   productCodes = productCodes.concat(ses.productCodes);
    //   if (ses.exception.priority) {
    //     productCodes = productCodes.concat(ses.exception.productCodes);
    //   }
    // });
    // productCodes = [...new Set(productCodes)];

    // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
    const minPercent =
      handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    let andQuery: any = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.search)) {
      andQuery = andQuery.concat({
        $or: [
          {
            "primaryTransaction.propertyUnit.code": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.name": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.email": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.phone": {
              $regex: query.search,
              $options: "i",
            },
          },
        ],
      });
    }

    // lấy tất cả hợp đồng đủ điều kiện bàn giao của project
    let _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      // 'primaryTransaction.propertyUnit.code': { $in: productCodes },
      $and: andQuery,
      sort: "handoverStatus,-modifiedDate",
      _fields: `
        id,
        primaryTransaction.propertyUnit.code,
        primaryTransaction.customer.personalInfo,
        primaryTransaction.customer.info,
        primaryTransaction.customer.employee.name,
        deliveryResult,
        handoverStatus,
        deliveryDate,
        isSendDelivery,
        deliveryItems
      `,
      paymentPercent: { $gte: minPercent },
    };

    let models: any;
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      models = await this.repository
        .findPrimaryContractForCustomer(_query, true, page, pageSize)
        .then((result: any[]) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          return result[0].rows.map((r) => {
            return this.transformHandoverItem(r, project, handOverSetting);
          });
        });
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
      models = models.map((r) => {
        return this.transformHandoverItem(r, project, handOverSetting);
      });
    }

    return models;
  }
  async getHandoverByStatus(handoverId, query) {
    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: StatusEnum.ACTIVE,
    });
    const handoverSchedules: IHandoverScheduleDocument[] =
      await this.handoverScheduleQueryService.findAllByDeliveryId(handoverId);
    if (!handOverSetting) {
      return this.getResponse("PRIMARYCONTRACT0001");
    }
    let handoverScheduleIds: string[] = [];
    if (handoverSchedules)
      handoverScheduleIds = handoverSchedules.map((element) => {
        return element.id;
      });

    const project = await this.getProjectById(handOverSetting.project.id);

    if (!project) {
      return this.getResponse("PRIMARYCONTRACT0002");
    }
    let lstPropertyUnitId: any[] = [];

    // get list id sản phẩm xd03

    let q = {
      $or: [
        { "syncErpData.anal_ppt9": "XD03A" },
        { "syncErpData.anal_ppt9": "XD08" },
        { "syncErpData.anal_ppt9": "XD09" }
      ],
      "project.id": handOverSetting.project.id,
    };
    const lstPropertyUnit = await this.propertyClient.sendDataPromise(
      q,
      CmdPatternConst.PROPERTY.GET_PROPERTY_UNIT_BY_PROJECT_ID
    );
    if (lstPropertyUnit && lstPropertyUnit.length > 0) {
      lstPropertyUnitId = lstPropertyUnit.map((item) => item.id);
    }

    // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
    const minPercent =
      handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    let andQuery: any = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.search)) {
      andQuery = andQuery.concat({
        $or: [
          {
            "primaryTransaction.propertyUnit.code": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.name": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.email": {
              $regex: query.search,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.phone": {
              $regex: query.search,
              $options: "i",
            },
          },
        ],
      });
    }

    // lấy tất cả hợp đồng đủ điều kiện bàn giao của project
    let _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      "primaryTransaction.propertyUnit.id": { $in: lstPropertyUnitId },
      id: { $nin: handoverScheduleIds },
      $and: andQuery,
      $or: [
        { handoverStatus: HandoverStatusEnum.later },
        { handoverStatus: HandoverStatusEnum.init },
        { cerHandoverStatus: HandoverStatusEnum.later },
      ],
      sort: "handoverStatus,-modifiedDate",
      _fields: `
        id,
        primaryTransaction.propertyUnit.id,
        primaryTransaction.propertyUnit.code,
        primaryTransaction.propertyUnit.syncErpData,
        primaryTransaction.customer.personalInfo,
        primaryTransaction.customer.info,
        primaryTransaction.customer.employee.name,
        deliveryResult,
        handoverStatus,
        deliveryDate,
        isSendDelivery,
        deliveryItems
      `,
      paymentPercent: { $gte: minPercent },
    };

    let models: any;
    const page: number = parseInt(query["page"]) || 1;
    const pageSize: number = parseInt(query["pageSize"]) || 10;
    models = await this.repository
      .findPrimaryContractForCustomer(_query, true, page, pageSize)
      .then((result: any[]) => {
        const total = result[0].totalCount[0]
          ? result[0].totalCount[0].count
          : 0;
        return {
          rows: result[0].rows.map((r) => {
            return this.transformHandoverItem(r, project, handOverSetting);
          }),
          page,
          pageSize,
          total: total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
        };
      });

    if (models.rows.length > 0) {
      const unitSyncMap = new Map(
        lstPropertyUnit.map(unit => [unit.id, unit.syncErpData])
      );

      models.rows.forEach(row => {
        const unitId = row?.primaryTransaction?.propertyUnit?.id;
        if (unitId && unitSyncMap.has(unitId)) {
          row.primaryTransaction.propertyUnit.syncErpData = unitSyncMap.get(unitId);
        }
      });

      const hasMatch = models.rows.some(row =>
        row?.primaryTransaction?.propertyUnit?.syncErpData?.anal_ppt9 === 'XD10' ||
        row?.primaryTransaction?.propertyUnit?.syncErpData?.anal_ppt9 === 'XD08'
      );

      if (hasMatch) {
        const ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
          isActive: StatusEnum.ACTIVE,
          'project.id': handOverSetting.project.id
        });

        if (ownershipCertificate) {
          // Gán ownershipCertificate vào từng row nếu cần
          models.rows.forEach(row => {
            const value = row?.primaryTransaction?.propertyUnit?.syncErpData?.anal_ppt9;
            if (value === 'XD10' || value === 'XD08') {
              row.ownershipCertificateId = ownershipCertificate.id;
              row.ownershipCertificate = ownershipCertificate;
            }
          });

        }
      }
    }

    return models;
  }
  async getHandoverByStatusNew(handoverId, query) {
    // Lấy dữ liệu bàn giao và danh sách schedule song song
    const [handOverSetting, handoverSchedules] = await Promise.all([
      this.handoverQueryService.findOneByQuery({
        id: handoverId,
        status: StatusEnum.ACTIVE,
      }),
      this.handoverScheduleQueryService.findAllByDeliveryId(handoverId),
    ]);

    if (!handOverSetting) return this.getResponse("PRIMARYCONTRACT0001");

    const handoverScheduleIds = handoverSchedules?.map(s => s.id) || [];

    // Lấy project
    const project = await this.getProjectById(handOverSetting.project.id);
    if (!project) return this.getResponse("PRIMARYCONTRACT0002");

    // Lấy danh sách sản phẩm theo điều kiện
    const propertyFilter = {
      $or: [
        { "syncErpData.anal_ppt9": "XD03A" },
        { "syncErpData.anal_ppt9": "XD08" },
        { "syncErpData.anal_ppt9": "XD09" },
      ],
      "project.id": handOverSetting.project.id,
    };

    const lstPropertyUnit = await this.propertyClient.sendDataPromise(
      propertyFilter,
      CmdPatternConst.PROPERTY.GET_PROPERTY_UNIT_BY_PROJECT_ID
    );

    const lstPropertyUnitId = lstPropertyUnit?.map(p => p.id) || [];

    // Tính phần trăm thanh toán tối thiểu
    const minPercent = handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    // Hàm tạo điều kiện tìm kiếm
    const buildSearchCondition = (search) => {
      if (_.isEmpty(search)) return [];
      const regex = { $regex: search, $options: "i" };
      return [{
        $or: [
          { "primaryTransaction.propertyUnit.code": regex },
          { "primaryTransaction.customer.personalInfo.name": regex },
          { "primaryTransaction.customer.personalInfo.email": regex },
          { "primaryTransaction.customer.personalInfo.phone": regex },
        ],
      }];
    };

    const andQuery = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
      ...buildSearchCondition(query.search)
    ];

    // Tạo query cuối cùng
    const _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      "primaryTransaction.propertyUnit.id": { $in: lstPropertyUnitId },
      id: { $nin: handoverScheduleIds },
      $and: andQuery,
      $or: [
        { handoverStatus: HandoverStatusEnum.later },
        { handoverStatus: HandoverStatusEnum.init },
        { cerHandoverStatus: HandoverStatusEnum.later },
        { handoverStatus: { $exists: false } },
        { handoverStatus: null },
        { cerHandoverStatus: { $exists: false } },
        { cerHandoverStatus: null }
      ],

      sort: "handoverStatus,-modifiedDate",
      _fields: `
    id,
    primaryTransaction.propertyUnit.id,
    primaryTransaction.propertyUnit.code,
    primaryTransaction.propertyUnit.syncErpData,
    primaryTransaction.customer.personalInfo,
    primaryTransaction.customer.info,
    primaryTransaction.customer.employee.name,
    deliveryResult,
    handoverStatus,
    deliveryDate,
    isSendDelivery,
    deliveryItems
  `,
      paymentPercent: { $gte: minPercent },
    };


    let models: any;
    const page: number = parseInt(query["page"]) || 1;
    const pageSize: number = parseInt(query["pageSize"]) || 10;
    models = await this.repository
      .findPrimaryContractForCustomerNew(_query, true, page, pageSize)
      .then((result: any[]) => {
        const total = result[0].totalCount[0]
          ? result[0].totalCount[0].count
          : 0;
        return {
          rows: result[0].rows.map((r) => {
            return this.transformHandoverItemNew(r, project, handOverSetting);
          }),
          page,
          pageSize,
          total: total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
        };
      });

    if (models.rows.length > 0) {
      const unitSyncMap = new Map(lstPropertyUnit.map(u => [u.id, u.syncErpData]));
      let ownershipCertificate: any;

      for (const row of models.rows) {
        const unitId = row?.primaryTransaction?.propertyUnit?.id;

        // Merge syncErpData
        if (unitId && unitSyncMap.has(unitId)) {
          row.primaryTransaction.propertyUnit.syncErpData = unitSyncMap.get(unitId);
        }

        // Check XD10 / XD08
        const code = row?.primaryTransaction?.propertyUnit?.syncErpData?.anal_ppt9;
        if (code === 'XD10' || code === 'XD08') {
          if (!ownershipCertificate) {
            ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
              isActive: StatusEnum.ACTIVE,
              'project.id': handOverSetting.project.id
            });
            if (!ownershipCertificate) break; // Không có thì thoát luôn
          }
          row.ownershipCertificateId = ownershipCertificate.id;
          row.ownershipCertificate = ownershipCertificate;
        }
      }
    }

    return models;
  }
  private transformHandoverItem(r, project, handOverSetting) {
    r.projectType = project.type;
    r.projectName = project.name;
    r.projectId = project.id;

    if (r.deliveryItems) {
      const notPassItem = r.deliveryItems.findIndex((group) => {
        return group.list ? group.list.findIndex((item) => item.isPass) : -1;
      });
      r.checklist = notPassItem === -1;
      delete r.deliveryItems;
    }
    r.session = "";
    r.smsTemplate = handOverSetting?.smsTemplate || "";
    r.emailTemplate = handOverSetting?.emailTemplate || "";
    r.careInvestor = project.careInvestor;
    return r;
  }
  private transformHandoverItemNew(r, project, handOverSetting) {
    return {
      ...r,
      projectType: project.type,
      projectName: project.name,
      projectId: project.id,
      checklist: r.deliveryItems
        ? !r.deliveryItems.some(group => group.list?.some(item => item.isPass))
        : undefined,
      session: "",
      smsTemplate: handOverSetting?.smsTemplate || "",
      emailTemplate: handOverSetting?.emailTemplate || "",
      careInvestor: project.careInvestor,
      deliveryItems: undefined // remove field
    };
  }
  renderTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/{{(.*?)}}/g, (_, key) => {
      const path = key.trim().split('.');
      let value = data;

      for (const segment of path) {
        if (value === null || value === undefined) return '';
        value = value[segment];
      }

      return (value !== null && value !== undefined) ? String(value) : '';
    });
  }
  async getContractByCode(code: string) {
    const contract = await this.repository.findOne(
      { code },
      { "policyPayment.schedule": 1 }
    );
    return contract;
  }

  async getContractsWithStatusForCareCustomer(identitiesArr: any[]) {
    // lấy ra hợp đồng của khách hàng với trang thái approve, waiting
    let _query = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.customer.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer.identities.value": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identities.value": {
                $in: identitiesArr,
              },
            },
          ],
        },
      ],
      _fields: "id",
    };

    return this.repository.findPrimaryContractForCustomer(_query, false);
  }
  syncProject(project: any) {
    return this.repository.updateMany(
      {
        "primaryTransaction.project.id": project.id,
      },
      {
        $set: {
          "primaryTransaction.project": project,
        },
      }
    );
  }

  syncPrimaryTransaction(primaryTransaction: any) {
    return this.repository.updateMany(
      {
        "primaryTransaction.id": primaryTransaction.id,
      },
      {
        $set: {
          primaryTransaction: primaryTransaction,
        },
      }
    );
  }

  async getPrimaryContractsByPropertyUnitCodeAndProjectId(query: {
    propertyUnitCode: string;
    projectId: string;
  }) {
    const aggregate = [
      {
        $match: {
          $and: [
            { "primaryTransaction.project.id": query.projectId },
            { "primaryTransaction.propertyUnit.code": query.propertyUnitCode },
            {
              status: {
                $in: [
                  StatusContractEnum.APPROVED,
                  StatusContractEnum.ACCOUNTANT_WAITING,
                  StatusContractEnum.LIQUIDATED,
                ],
              },
            },
            {
              $or: [
                { type: ContractEnum.DEPOSIT, purchase: null },
                { type: { $ne: ContractEnum.DEPOSIT } },
              ],
            },
          ],
        },
      },
      {
        $project: {
          primaryTransaction: 1,
        },
      },
    ];

    return this.repository.findPrimaryContractsAggregate(aggregate);
  }

  async getPrimaryContractsByPropertyUnitCodesAndProjectIds(query: {
    propertyUnitCodes: string[];
    projectIds: string[];
  }) {
    const aggregate = [
      {
        $match: {
          $and: [
            { "primaryTransaction.project.id": { $in: query.projectIds } },
            {
              "primaryTransaction.propertyUnit.code": {
                $in: query.propertyUnitCodes,
              },
            },
            {
              status: {
                $in: [
                  StatusContractEnum.APPROVED,
                  StatusContractEnum.ACCOUNTANT_WAITING,
                  StatusContractEnum.LIQUIDATED,
                ],
              },
            },
            {
              $or: [
                { type: ContractEnum.DEPOSIT, purchase: null },
                { type: { $ne: ContractEnum.DEPOSIT } },
              ],
            },
          ],
        },
      },
      {
        $project: {
          primaryTransaction: 1,
        },
      },
    ];

    return this.repository.findPrimaryContractsAggregate(aggregate);
  }

  async getDebtReportContract(user: any, query: any) {
    let _query: any = {};
    if (query.q) {
      _query.q = query.q;
    }
    if (query.projectId) {
      _query.projectId = query.projectId;
    }
    if (query.createdFrom) {
      _query.createdFrom = query.createdFrom;
    }
    if (query.createdTo) {
      _query.createdTo = query.createdTo;
    }

    const projects = await this.propertyClient.sendDataPromise(
      user,
      CmdPatternConst.LISTENER.GET_ALL_PROJECT_BY_USER
    );

    _query["projectIds"] = (projects || []).map((e) => e._id) || [];

    if (query.page || query.pageSize) {
      let page: number = parseInt(query["page"]) || 1;
      let pageSize: number = parseInt(query["pageSize"]) || 10;
      _query.page = page;
      _query.pageSize = pageSize;
    }
    return this.repository.findDebtReportContract(_query);
  }

  async getFileByPosIdUserLogin(user: any) {
    // lấy thông tin user login
    const e = await this.employeeClient.sendDataPromise(
      { id: user.id },
      CmdPatternConst.EMPLOYEE.EMPLOYEE_GET_BY_ID
    );
    if (e && e.pos && e.pos.id) {
      const file = await this.orgchartClient.sendDataPromise(
        e.pos.id,
        CmdPatternConst.ORGCHART.GET_TEMPLATE_DEPT_REPORT_FILE_BY_POS_ID
      );
      if (file && file.length) {
        // get data
        return file[0];
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  countDayBetweenCurrentDate(paymentDueDate) {
    let difference = new Date().getTime() - new Date(paymentDueDate).getTime();
    let days = Math.ceil(difference / (1000 * 3600 * 24));
    return days;
  }
  async handedContractByProjectId(projectId) {
    const query = {
      handoverStatus: HandoverStatusEnum.handed,
      "primaryTransaction.project.id": projectId,
    };
    return await this.repository.find(query, { id: 1 });
  }
  async generateFileByQuery(user, query, fileName: string, isSeeAll = false) {
    const entities = await this.getContract(user, query, isSeeAll);

    return await this.fileGenerationService.generateAndSaveTicketFile(
      query["url"],
      entities,
      fileName,
      user
    );
  }

  async printTicketByQuery(user, query, res, isSeeAll = false) {
    var fileName = "templateFile" + new Date().getTime();
    this.generateFileByQuery(user, query, fileName, isSeeAll).then(() => {
      fileName = fileName + ".pdf";
      const filePath = join(
        this.staticAssetService.getUploadFolderPath(),
        fileName
      );

      const isFileExisted = existsSync(filePath);
      if (isFileExisted === false) {
        throw new NotFoundException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "field"),
        });
      }
      res.sendFile(fileName, {
        root: this.staticAssetService.getUploadFolderPath(),
      });
      setTimeout(() => {
        unlinkSync(filePath);
      }, 10000);
    });
  }
}