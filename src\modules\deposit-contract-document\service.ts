import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { BaseServiceRepository } from '../../../shared-modules';
import { v4 } from 'uuid';
import _ = require('lodash');
import { IDepositContractDocumentFolder } from './interfaces/deposit-contract-document.interface';
import { DepositContractDocumentRepository } from './repositories/deposit-contract-document.repository';
import { DepositContractDocumentType } from '../shared/enum/deposit-contract-document.enum';
import { AddItemDepositContractDocumentDto, CreateDepositContractDocumentDto, UpdateDepositContractDocumentDto, UpdateItemDepositContractDocumentDto } from './dto/deposit-contract-document.dto';

@Injectable()
export class DepositContractDocumentService extends BaseServiceRepository<IDepositContractDocumentFolder> {
  constructor(private readonly depositContractDocumentRepository: DepositContractDocumentRepository) {
    super(depositContractDocumentRepository);
  }

  async getItemDepositContractDocument(id: string): Promise<IDepositContractDocumentFolder> {
    const data = await super.findOne({ id, type: DepositContractDocumentType.FOLDER, softDelete: false });
    data.items.sort((a, b) => a.name.localeCompare(b.name));
    if (!data) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    return data;
  }

  async getDepositContractDocument(depositContractId: string): Promise<IDepositContractDocumentFolder[]> {
    const data = await super.findAll({ depositContractId: depositContractId, softDelete: false }, { type: -1, name: 1, createdDate: -1 });
    data.forEach(folder => {
      folder.items.sort((a, b) => a.name.localeCompare(b.name));
    });
    return data
  }

  private getFileNameParts(filename: string): { baseName: string; extension: string } {
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return { baseName: filename, extension: '' };
    }
    return {
      baseName: filename.substring(0, lastDotIndex),
      extension: filename.substring(lastDotIndex), // bao gồm dấu chấm
    };
  }

  async createDepositContractDocument(dto: CreateDepositContractDocumentDto, user: any) {
    if (dto.type === DepositContractDocumentType.FILE) {
      const { baseName, extension } = this.getFileNameParts(dto.name);
      // Tìm tất cả các tài liệu có tên trùng khớp
      console.log('dto.depositContractId', dto.depositContractId);
      const existingDocuments = await this.depositContractDocumentRepository.existingDocuments(dto.depositContractId, DepositContractDocumentType.FILE, baseName, extension);
      let newName = dto.name;
      let counter = 1;
      const nameSet = new Set(existingDocuments.map(doc => doc.name));

      while (nameSet.has(newName)) {
        newName = `${baseName} (${counter})${extension}`;
        counter++;
      }
      dto.name = newName;
    }
    await super.isExistThrowError({ name: dto.name, depositContractId: dto.depositContractId, softDelete: false, type: dto.type }, 'COME0008');
    return await super.create({ ...dto, depositContractId: dto.depositContractId, createdBy: user.id });
  }

  async updateDepositContractDocument(depositContractId: string, id: string, dto: UpdateDepositContractDocumentDto, user: any) {
    const data = await super.findOne({ id, softDelete: false, type: DepositContractDocumentType.FOLDER });
    if (!data) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    if (data.name !== dto.name) {
      await super.isExistThrowError({ name: dto.name, depositContractId: depositContractId, type: DepositContractDocumentType.FOLDER, softDelete: false }, 'COME0008');
    }
    return await super.update(id, { ...dto, modifiedDate: new Date(), modifiedBy: user.id });
  }

  async deleteDepositContractDocument(id: string, user: any) {
    await super.isNotExistThrowError({ id, softDelete: false }, 'COME0002');
    return await super.update(id, { softDelete: true, modifiedDate: new Date(), modifiedBy: user.id });
  }

  async addItemDepositContractDocument(id: string, dto: AddItemDepositContractDocumentDto, user: any) {
    const data = await super.findOne({ id, softDelete: false, type: DepositContractDocumentType.FOLDER });
    if (!data) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    const { items } = data;
    const { baseName, extension } = this.getFileNameParts(dto.name);
    let newName = dto.name;

    let counter = 1;

    const existingNames = new Set(items.map(item => item.name));

    // Nếu tên đã tồn tại, thêm số `(1)`, `(2)`, ...
    while (existingNames.has(newName)) {
      newName = `${baseName} (${counter})${extension}`;
      counter++;
    }
    dto.name = newName;
    items.unshift({
      ...dto,
      id: v4(),
    });
    return await super.update(id, { items, modifiedDate: new Date(), createdBy: user.id });
  }

  async updateItemDepositContractDocument(documentId: string, itemId: string, dto: UpdateItemDepositContractDocumentDto, user: any) {
    const data = await super.findOne({ id: documentId, softDelete: false, type: DepositContractDocumentType.FOLDER });
    if (!data) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    const { items } = data;
    const item = _.find(items, { id: itemId });
    if (!item) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    const index = _.findIndex(items, { id: itemId });
    items[index] = {
      ...item,
      ...dto
    };
    return await super.update(documentId, { items, modifiedDate: new Date(), modifiedBy: user.id });
  }

  async deleteItemDepositContractDocument(documentId: string, itemId: string, user: any) {
    const data = await super.findOne({ id: documentId, softDelete: false, type: DepositContractDocumentType.FOLDER });
    if (!data) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    const { items } = data;
    const item = _.find(items, { id: itemId });
    if (!item) {
      throw new HttpException({ errorCode: 'COME0002' }, HttpStatus.OK);
    }
    const index = _.findIndex(items, { id: itemId });
    items.splice(index, 1);
    return await super.update(documentId, { items, modifiedDate: new Date(), modifiedBy: user.id });
  }
  async demandDepositContractDocumentSync(depositContractDocuments: any[]) {
    console.log('demandDepositContractDocumentSync update service =>');
    for (const doc of depositContractDocuments) {
      await super.create(doc);
    }
  }

}