import { Connection } from 'mongoose';
import { DebtCommissionListSchema } from '../schemas/debt-commission-list.schema';
import { CommonConst } from '../../shared/constant';

export const DebtCommissionListProviders = [
  {
      provide: CommonConst.DEBT_COMMISSION_LIST,
      useFactory: (connection: Connection) => connection.model(CommonConst.AGGREGATES.DEBT_COMMISSION_LIST.NAME, DebtCommissionListSchema),
      inject: [CommonConst.QUERY_CONNECTION_TOKEN],
    },
    {
      provide: CommonConst.TEMP_DEBT_COMMISSION_LIST,
      useFactory: (connection: Connection) => connection.model(CommonConst.AGGREGATES.DEBT_COMMISSION_LIST.TEMP, DebtCommissionListSchema),
      inject: [CommonConst.QUERY_CONNECTION_TOKEN],
    },
];
