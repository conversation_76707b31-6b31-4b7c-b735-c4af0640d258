import { isEmpty, toNumber as toNumber<PERSON>odash } from 'lodash';
import { CmdPatternConst } from '../../../../shared-modules';
import * as FormData from 'form-data';
import axios from "axios";
import _ = require('lodash');
import * as Bluebird from "bluebird";
import { HttpException, HttpStatus } from "@nestjs/common";
import <PERSON>zZip from 'pizzip';
import Docxtemplater from 'docxtemplater';
import sizeOf from 'image-size';

const ImageModule = require('docxtemplater-image-module-free');
const fs = Bluebird.promisifyAll(require("fs"));
interface ToNumberOptions {
  default?: number;
  min?: number;
  max?: number;
}

export function toBoolean(value: string): boolean {
  value = value.toLowerCase();

  return value === 'true' || value === '1' ? true : false;
}

export function toNumber(value: unknown, opts: ToNumberOptions = {}) {
  value = value === undefined ? opts.default : value;
  let newValue: number = toNumberLodash(value);
  
  if (isNaN(newValue)) {
    newValue = opts.default;
  }

  if (opts.min && newValue < opts.min) {
    newValue = opts.min;
  }

  if (opts.max && newValue > opts.max) {
    newValue = opts.max;
  }

  return newValue;
}

export async function GenPrefix(functionPrefix, userId, stsClient){
  const accInfo = await stsClient.sendDataPromise(
    { id: userId },
    CmdPatternConst.STS.ACCOUNT.GET_INFO
  );
  const accOrgCode = accInfo.result[0]?.orgCode;
  if (!accOrgCode) {
    return '';
  }

  return accOrgCode.substring(0, 4) + functionPrefix;
}

export async function pushFileS3(file: any, isCovertedToBuffer: boolean = false, newFileName: any = null) {
  if (!file) return;
  const formData = new FormData();
  if (isCovertedToBuffer)
    formData.append('file', file, {
      filename: newFileName
    });
  else
    formData.append('file', file?.buffer, {
      filename: file.originalname
    });
  formData.append('path', 'primary-contract');
  formData.append('useOriginalName', 'true');

  const response = await axios.post(
    process.env.MTD_UPLOAD,
    formData,
    {
      headers: { ...formData.getHeaders() }
    });
  return response?.data;
}

export async function ReAssignAccountInforForMultiple(rows: any, stsClient) {
  const uniqueAccountIds: Set<string> = new Set(
    _.flatten(rows.map((row) => [row.createdBy, row.modifiedBy]))
  );
  const accInfo = await stsClient.sendDataPromise(
    { id: { $in: Array.from(uniqueAccountIds) } },
    CmdPatternConst.STS.ACCOUNT.GET_INFO
  );
  // Replace ID with object
  const clones = JSON.parse(JSON.stringify(rows));
  clones.forEach((row) => {
    row.createdBy = {
      email: accInfo.listEmailById[row.createdBy],
      userName: accInfo?.result.find((x) => x.id == row.createdBy)?.username,
      fullName: accInfo?.result.find((x) => x.id == row.createdBy)?.fullName,
    };
    row.modifiedBy = {
      email: accInfo.listEmailById[row.modifiedBy],
      userName: accInfo?.result.find((x) => x.id == row.modifiedBy)?.username,
      fullName: accInfo?.result.find((x) => x.id == row.modifiedBy)?.fullName,
    };
    row.updatedBy = {
      email: accInfo.listEmailById[row.updatedBy],
      userName: accInfo?.result.find((x) => x.id == row.updatedBy)?.username,
      fullName: accInfo?.result.find((x) => x.id == row.updatedBy)?.fullName,
    };
  });
  return clones;
}

export async function ReAssignAccountInforForSingle(object: any, stsClient): Promise<any> {
  const { createdBy, modifiedBy, updatedBy } = object;
  const uniqueAccountIds = Array.from(new Set([createdBy, modifiedBy, updatedBy]));

  const accInfo = await stsClient.sendDataPromise(
    { id: { $in: uniqueAccountIds } },
    CmdPatternConst.STS.ACCOUNT.GET_INFO
  );

  // Tạo một map để dễ truy cập thông tin theo id
  const accountMap = new Map(
    accInfo.result.map((user: any) => [user.id, user])
  );

  // Hàm tạo thông tin user
  const getUserInfo = (id: string | number) => {
    const user: any = accountMap.get(id) || {};
    return {
      email: accInfo.listEmailById?.[id],
      userName: user.username,
      fullName: user.fullName,
    };
  };

  const clone = JSON.parse(JSON.stringify(object));
  clone.createdBy = getUserInfo(createdBy);
  clone.modifiedBy = getUserInfo(modifiedBy);
  clone.updatedBy = getUserInfo(updatedBy);
  return clone;
}

export function countWeekendDays(startDate, endDate) {
  let count = 0;

  // Đảm bảo startDate luôn nhỏ hơn endDate
  let current = new Date(Math.min(startDate, endDate));
  const stop = new Date(Math.max(startDate, endDate));

  while (current <= stop) {
    const day = current.getDay(); // 0: Chủ Nhật, 6: Thứ Bảy
    if (day === 0 || day === 6) {
      count++;
    }
    current.setDate(current.getDate() + 1); // tăng từng ngày
  }

  return count;
}

export async function getValueFileBuffer(fileUrl: string): Promise<Buffer> {
  try {
    const response = await axios.get(fileUrl, {
      responseType: 'arraybuffer',
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      }
    });
    return Buffer.from(response.data);
  } catch (error) {
    console.log(error)
  }
}

export async function fillDocxTemplateFromBuffer(buffer: Buffer, data: any): Promise<Buffer> {
  try {
    const content = buffer.toString('binary');
    const zip = new PizZip(content);
    const imageModule = new ImageModule({
      centered: true,
      async getImage(tagValue, tagName) {
        // Fetch image từ URL
        const response = await axios.get(tagValue, {
          responseType: 'arraybuffer'
        });
        return response.data;
      },
      getSize(imgBuffer, tagValue, tagName) {
        const dimensions = sizeOf(imgBuffer);
        const maxWidth = 150;

        // Tính chiều cao dựa trên tỉ lệ gốc
        const ratio = dimensions.height / dimensions.width;
        const height = maxWidth * ratio;

        return [maxWidth, height];
      }
    });
    const doc = new Docxtemplater(zip, {
      modules: [imageModule],
      paragraphLoop: true,
      linebreaks: true,
      nullGetter() { return ''; }
    });
    await doc.renderAsync(data);
    const bufferOutput = doc.getZip().generate({ type: 'nodebuffer' });
    return bufferOutput;
  } catch (error) {
    throw new HttpException({ errorCode: 'FILETEMPLATE0001', message: "Failed to fill template, please try again later" }, HttpStatus.OK);
  }
}

export async function convertToPdf(outputPath: string, outputFile: string, configService, s3, key, name?): Promise<any> {
  try {
    const formData = new FormData();
    const fileStream = fs.createReadStream(outputPath);
    formData.append('files', fileStream);
    formData.append('merge', 'true');
    let config: any = {
      method: 'post',
      url: `${configService.get("EAPP_LOCATION_URL")}/forms/libreoffice/convert`,
      headers: {
          ...formData.getHeaders()
      },
      responseType: 'arraybuffer',
      data: formData
    };

    const response = await axios.request(config);
    const base64 = Buffer.from(response.data).toString('base64');

    if (outputFile !== "file") {
      const s3Params = {
        Bucket: configService.get("AWS_BUCKET_NAME"),
        Key: `${key}/${name}-${Date.now()}.pdf`,
        Body: Buffer.from(response.data),
        ACL: 'public-read',
        ContentType: 'application/pdf',
      };
      const s3Response = await s3.upload(s3Params).promise();
      if (!s3Response.hasOwnProperty('key')) {
          s3Response['key'] = s3Response.Key;
      }

      s3Response['Location'] = configService.get('S3_URL') + '/' + s3Response['key'];
      return s3Response;
    }

    return `data:application/pdf;base64,${base64}`;
  } catch (error) {
    throw new HttpException({ errorCode: 'FILETEMPLATE0003', message: "Failed to convert to PDF" }, HttpStatus.OK);
  } finally {
    if (fs.existsSync(outputPath)) {
      fs.unlinkSync(outputPath);
    }
  }
}