import { Controller, Put, Post, Delete, Body, Headers, UseGuards, UseInterceptors, Param, UploadedFile, ForbiddenException, Get, Query } from '@nestjs/common';
import { ApiUseTags, ApiBearerAuth, ApiForbiddenResponse, ApiCreatedResponse } from '@nestjs/swagger';
import { LoggingInterceptor } from '../../common/interceptors/logging.interceptor';
import { DebtCommissionService } from './service';
import { Action } from '../shared/enum/action.enum';
import { PermissionEnum } from '../shared/enum/permission.enum';
import { CreateDebtCommissionDto, DeleteDebtCommissionDto, UpdateDebtCommissionDto, UpdateStatusDebtCommissionDto } from './dto/debt-commission.dto';
import { ValidationPipe } from '../../common/pipes/validation.pipe';
import { JwtAuthGuard, RoleGuard, Roles, User } from '../../../shared-modules';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { checkPermission } from '../shared/utils/checkPermission';
import { DebtCommissionStatus } from '../shared/enum/commission.enum';

interface IPagingQuery {
  search: string;
  periodName: string;
  posName: string;
  startDate: string;
  endDate: string;
  createdBy: string;
  page: number;
  pageSize: number;
}

@ApiBearerAuth()
@ApiUseTags('v1/debt/commission')
@Controller('v1/debt/commission')
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard, RoleGuard)
export class DebtCommissionController {

  private resSuccess = { success: true };
  private actionName: string = Action.NOTIFY;

  constructor(
    private readonly service: DebtCommissionService,
  ) { }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEBT_COMMISSION_GET_ALL)
  @Get()
  findAll(
    @User() user,
    @Query() query: IPagingQuery
  ): Promise<any> {
    return this.service.findAll(user, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.DEBT_COMMISSION_GET_BY_ID)
  @Get('/:id')
  findById(@User() user, @Param('id') id: string): Promise<any> {
    return this.service.findDebtCommissionById(user, id);
  }

  @Roles(PermissionEnum.DEBT_COMMISSION_CREATE)
  @Post()
  @ApiCreatedResponse({ description: 'The record has been successfully created.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async createCommissionDebt(@User() user, @Body() dto: CreateDebtCommissionDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.service.createDebtCommission(user, dto, this.actionName) || this.resSuccess;
  }

  @Roles(PermissionEnum.DEBT_COMMISSION_UPDATE)
  @Put()
  @ApiCreatedResponse({ description: 'The record has been successfully updated.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async updateCommissionDebt(@User() userLogged, @Body() dto: UpdateDebtCommissionDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.service.updateDebtCommission(userLogged, dto, this.actionName) || this.resSuccess;
  }

  @Roles(PermissionEnum.DEBT_COMMISSION_DELETE)
  @Delete(':id')
  @ApiCreatedResponse({ description: 'The record has been successfully updated.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async deleteCommissionDebt(@User() userLogged, @Param('id') id: string, @Body() dto: DeleteDebtCommissionDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.service.deleteDebtCommission(userLogged, id, dto, this.actionName);
  }

  @Roles(PermissionEnum.DEBT_COMMISSION_UPDATE)
  @Put('update-status')
  @ApiCreatedResponse({ description: 'The record has been successfully update status.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async updateStatusDebtCommission(
    @User() userLogged,
    @Body(new ValidationPipe()) dto: UpdateStatusDebtCommissionDto,
    @Headers('act') actionName?: string) {
    // this.actionName = (actionName || this.actionName);
    // const isAllow = checkPermission(userLogged.roles, PermissionEnum.DEBT_COMMISSION_PUBLISH)
    // if (!isAllow && (dto.status === DebtCommissionStatus.CONFIRMED)) {
    //   throw new ForbiddenException('Bạn không có quyền truy cập!');
    // }
    return await this.service.updateStatusAdjustmentVersion(userLogged, dto);
  }

  // @Roles(PermissionEnum.DEBT_COMMISSION_PUBLISH)
  // @Put('announced/:id')
  // @ApiCreatedResponse({ description: 'The record has been successfully publish.' })
  // @ApiForbiddenResponse({ description: 'Forbidden.' })
  // async publishDebtCommission(
  //   @User() userLogged,
  //   @Body(new ValidationPipe()) dto: UpdateStatusDebtCommissionDto,
  //   @Headers('act') actionName?: string
  // ) {
  //   this.actionName = (actionName || this.actionName);
  //   return await this.service.updateStatusAdjustmentVersion(userLogged, dto);
  // }

  @Roles(PermissionEnum.DEBT_COMMISSION_UPLOADFILE)
  @Post('upload-file')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: any
  ): Promise<any> {
    return await this.service.uploadFile(file);
  }

}
