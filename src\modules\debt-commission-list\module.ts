import { forwardRef, Module } from "@nestjs/common";
import { CodeGenerateModule } from "../code-generate/module";
import { StaticAssetModule } from "../config/static-asset.module";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { DebtCommissionModule } from "../debt-commission/module";
import { EmployeeQuerySideModule } from "../employee.query/module";
import { HistoryImportQuerySideModule } from "../import-history.queryside/module";
import { LoggerModule } from "../logger/logger.module";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { FileGenerationService } from "./file-generation.service";
import { DebtCommissionListController } from "./controller";
import { DebtCommissionListService } from "./service";
import { DebtCommissionListProviders } from "./providers/debt-commission-list.providers";
import { DebtCommissionListRepository } from "./repository/debt-commission-list.repository";
import { TempDebtCommissionListRepository } from "./repository/temp-debt-commission-list.repository";
import { PrimaryContractModule } from "../primary-contract/module";
import { DebtCommissionPolicyModule } from "../debt-commission-policy/module";

@Module({
  imports: [
    QueryDatabaseModule,
    DebtCommissionListModule,
    MgsSenderModule,
    EmployeeQuerySideModule,
    LoggerModule,
    HistoryImportQuerySideModule,
    // ProjectQuerySideModule,
    // CommissionPolicyQuerySideModule,
    CodeGenerateModule,
    StaticAssetModule,
    PrimaryContractModule,
    forwardRef(() => DebtCommissionModule),
    DebtCommissionPolicyModule,
  ],
  controllers: [
    DebtCommissionListController
  ],
  providers: [
    DebtCommissionListService,
    FileGenerationService,
    DebtCommissionListRepository,
    TempDebtCommissionListRepository,
    ...DebtCommissionListProviders,
  ],
  exports: [
    DebtCommissionListService,
    DebtCommissionListRepository,
    TempDebtCommissionListRepository,
    DebtCommissionListModule
  ]
})
export class DebtCommissionListModule { }