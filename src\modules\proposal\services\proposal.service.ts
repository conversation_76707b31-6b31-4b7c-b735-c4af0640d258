import {
  BaseServiceRepository,
  CmdPatternConst as cmd2,
  IListQueryInterface,
} from "../../../../shared-modules";
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
} from "@nestjs/common";
import { IProposalDocument } from "../../proposal.queryside/interfaces/document.interface";
import { ProposalRepository } from "../repositories/proposal.repository";
import { CodeGenerateService } from "../../code-generate/service";
import { CommonConst, CmdPatternConst } from "../../shared/constant";
import { PropertyClient } from "../../mgs-sender/property.client";
import { ProposalClient } from "../../mgs-sender/proposal.client";
import { ConfigService } from "@nestjs/config";
import moment = require("moment");
import { CustomHttpService } from "../../../shared-modules/http/http.service";
import { MsxLoggerService } from "../../logger/logger.service";
import { CreateProposalDto, CustomerTypeEnum, UpdateProposalDto } from "../dto/proposal.dto";
import { Action } from "../../shared/enum/action.enum";
import { CreateProposalCommand } from "../../proposal.domain/commands/impl/create-proposal.cmd";
import { UpdateProposalCommand } from "../../proposal.domain/commands/impl/update-proposal.cmd";
import { DeleteProposalCommand } from "../../proposal.domain/commands/impl/delete-proposal.cmd";
import { CommandBus } from "@nestjs/cqrs";
const clc = require("cli-color");
const uuid = require("uuid");
import { ContractTypeEnum } from "../../shared/enum/primary-contract.enum";
import { ProposalStatusEnum } from "../../shared/enum/proposal.enum";
import { PrimaryContractService } from "../../primary-contract/service";
import { ErrorConst } from "../../shared/constant/error.const";
import axios from "axios";
import { EmployeeClient } from "../../mgs-sender/employee.client";
import { PermissionEnum } from "../../shared/enum/permission.enum";
import { ProposalStatusDto } from "../../proposal.domain/dto/proposal.dto";
import { CareClient } from "../../mgs-sender/care.client";
import { isNullOrUndefined } from "util";
import { CustomerClient } from "../../mgs-sender/customer.client";
import { DemandClient } from "../../mgs-sender/demand.client";

@Injectable()
export class ProposalService extends BaseServiceRepository<IProposalDocument> {
  private readonly context = ProposalService.name;
  private commandId: string;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly propertyClient: PropertyClient,
    private readonly proposalRepository: ProposalRepository,
    private readonly proposalClient: ProposalClient,
    private readonly careClient: CareClient,
    private readonly configService: ConfigService,
    private readonly loggerService: MsxLoggerService,
    private readonly primaryContractService: PrimaryContractService,
    private readonly employeeClient: EmployeeClient,
    private readonly customerClient: CustomerClient,
    private readonly demandClient: DemandClient,
  ) {
    super(proposalRepository);
  }

  private escapeRegex(text) {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  }

  private mappingCreateDemandCustomer(customer: any): any {
  return {
    type: customer?.type ?? '',
    name: customer?.personalInfo?.name ?? '',
    phone: customer?.personalInfo?.phone ?? '',
    gender: customer?.info?.gender ?? null,
    continueCreate: true,
    bankInfo: [],
    companyName: customer?.company?.name ?? '',
    company: customer?.company ?? null,
    taxCode: customer?.taxCode ?? '',
    info: {
      birthday: customer?.info?.birthday ?? '',
      onlyYear: customer?.info?.onlyYear ?? '',
      gender: customer?.info?.gender ?? null,
      birthdayYear: customer?.info?.birthdayYear ?? '',
      cloneAddress: customer?.info?.useResidentialAddress ?? false,
      rootAddress: {
        province: customer?.info?.rootAddress?.province ?? null,
        district: customer?.info?.rootAddress?.district ?? null,
        ward: customer?.info?.rootAddress?.ward ?? null,
        address: customer?.info?.rootAddress?.address ?? null
      },
      address: {
        province: customer?.info?.address?.province ?? null,
        district: customer?.info?.address?.district ?? null,
        ward: customer?.info?.address?.ward ?? null,
        address: customer?.info?.address?.address ?? null
      }
    },

    personalInfo: {
      name: customer?.personalInfo?.name ?? '',
      phone: customer?.personalInfo?.phone ?? '',
      email: customer?.personalInfo?.email ?? '',
      identities: customer.type === "business" ? [] :(customer?.personalInfo?.identities ?? []).map((id: any) => ({
        type: id?.type ?? '',
        value: id?.value ?? '',
        date: id?.date ? new Date(id.date).toLocaleDateString('en-GB') : '', // Format thành "dd/mm/yyyy"
        place: id?.place ?? ''
      }))
    }
  };
}

  private axiosPost (url: string, data: any, authorization: string) {
    return axios.post(url, data, {
      headers: { 'Content-Type': 'application/json', Authorization: authorization },
      timeout: parseInt(process.env.TIMEOUT_API) || 120000
    });
  }

  private axiosGet (url: string, authorization: string) {
    return axios.get(url, {
      headers: { 'Content-Type': 'application/json', Authorization: authorization },
      timeout: parseInt(process.env.TIMEOUT_API) || 120000
    });
  }

  private async checkCumstomerOrCumstomerDemand(dto, headers: any) {
    const data = await this.axiosGet(
      process.env.GET_CUSTOMER_BY_QUERY + `page=1&pageSize=10&search=${dto.code}&type=${dto.type}`,
      headers
    );
    return data.data.data.rows[0];
  }

  private async checkCustomerToUpdate(model: any, headers: any) {
    const oldCustomer = await this.checkCumstomerOrCumstomerDemand(
      model, headers
    );
    if (oldCustomer) {
      if (oldCustomer.customerType === 'customer') {
        oldCustomer.info.gender = model.info.gender;
        await this.customerClient.sendDataPromise(
          oldCustomer,
          cmd2.CUSTOMER.UPDATE_AND_SYNC_SAP
        );
      } else if (oldCustomer.customerType === 'demand') {
        oldCustomer.personalInfo.name = model.personalInfo.name;
        oldCustomer.info.birthday = model.info.birthday;
        oldCustomer.info.birthdayYear = model.info.birthdayYear;
        oldCustomer.personalInfo.phone = model.personalInfo.phone;
        oldCustomer.info.cloneAddress = model.info.useResidentialAddress;
        oldCustomer.info.address = model.info.address;
        oldCustomer.info.rootAddress = model.info.rootAddress;
        await this.demandClient.sendDataPromise(
          oldCustomer,
          cmd2.DEMAND.DEMAND_CUSTOMER.UPDATE
        );
      }
      // model = oldCustomer;
      return oldCustomer;
    }
  }

  async createdAndUpdatedCustomer(customer: any, headers: any) {
    if (!customer.code){
      const requestBody = this.mappingCreateDemandCustomer(customer);
      const data = await this.axiosPost(
          process.env.CREATE_DEMAND,
          requestBody,
          headers
        );
        return data;
    }else{
      return await this.checkCustomerToUpdate(customer, headers);
    }
  }

  async createProposal(
    user: any,
    dto: CreateProposalDto,
    actionName: string,
    authorization: string
  ) {
    this.loggerService.log(this.context, clc.green("create service"));
    const model: any = { ...dto };
    if (model.id) {
      delete model.id;
    }
    const getPrimaryContract =
      await this.primaryContractService.getPrimaryContractById(dto.contractId);
    if (!getPrimaryContract) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0004" },
        HttpStatus.OK
      );
    }

    const escrowTicketDto: any = await this.proposalRepository.findOne({
      "escrowTicket.id": getPrimaryContract.primaryTransaction.id,
    });
    if (escrowTicketDto) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0020" },
        HttpStatus.OK
      );
      // throw new BadRequestException({
      //   errors: ErrorConst.Error(ErrorConst.EXISTED, 'Phiếu cọc', 'id', getPrimaryContract.primaryTransaction.id)
      // });
    }

    if (dto.proposalDate && !moment(dto.proposalDate).toDate()) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INTERNAL_DATE,
          "Ngày tạo đơn",
          dto.proposalDate.toString()
        ),
      });
    }

    const response = await axios.get(
      process.env.MSX_PROPERTY_PRIMARY_TRANSACTION_URI +
        `/${getPrimaryContract.primaryTransaction.id}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: authorization,
        },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000,
      }
    );
    const { statusCode, data, message } = response.data;
    if (statusCode !== "0") {
      throw new HttpException({ errorCode: statusCode }, HttpStatus.OK);
    }

    if (getPrimaryContract.primaryTransaction) model.escrowTicket = data;

    model.status = ProposalStatusEnum.INIT;

    // thông tin vay
    if (dto.loanType && dto.loanType == "yes") {
      if (isNullOrUndefined(dto.loanBankInfo)) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0031" }, HttpStatus.OK);
      }

      if (!dto.loanAmount) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0032" }, HttpStatus.OK);
      } else {
        if (dto.loanAmount.toString().length > 15) {
          throw new HttpException({ errorCode: "PRIMARYCONTRACT0035" }, HttpStatus.OK);
        }
      }
      
      if (!dto.loanTermYear) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0033" }, HttpStatus.OK);
      } else {
        const value = dto.loanTermYear.toString();
        const parts = value.split('.');
        const decimalPart = parts[1];
        if(decimalPart) {
          if (decimalPart.length !== 1) {
            throw new HttpException({ errorCode: "PRIMARYCONTRACT0036" }, HttpStatus.OK);
          }
        }
        if (value.length > 4) {
          throw new HttpException({ errorCode: "PRIMARYCONTRACT0034" }, HttpStatus.OK);
        }
      }
    }

    const customer = await this.createdAndUpdatedCustomer(model.customerTransferFormat, authorization);
    if (customer && customer.data && customer.data.statusCode !== '0') {
      throw new HttpException(customer.data, customer.data.statusCode);
    }

    if (model.customerTransferFormat.code){
      model.customerTransferFormat.customerType = customer.customerType;
    }else{
      model.customerTransferFormat.customerType = "demand";
      model.customerTransferFormat.id = customer.data.data.id;
      model.customerTransferFormat.code = customer.data.data.code;
    }

    this.commandId = uuid.v4();
    if (!model.code) {
      const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_PROPOSAL}`;
      model.code = await this.codeGenerateService.generateCode(
        CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
        prefix
      );
    }
    model.name = `${model.code}/${getPrimaryContract.primaryTransaction.escrowTicketCode}/${getPrimaryContract.primaryTransaction.customer.personalInfo.name}`;
    model.modifiedBy = user.id;
    model.createdBy = user.id;
    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);

    // Lưu vào lịch sử sản phẩm
    let histories = {
      propertyUnitId: getPrimaryContract.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason:
        dto.reasonLiquidation === "Đổi tên chuyển nhượng"
          ? "Thanh lý chuyển nhượng" + " (" + dto.customerTransfer.name + ")"
          : "Thanh lý chấm dứt",
      contractStatus: "Hợp đồng: " + ContractTypeEnum.APPROVED,
      actionName: "Tạo đơn đề nghị thanh lý",
    };
    await this.propertyClient.sendDataPromise(
      histories,
      CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT
    );
    return { id: this.commandId, code: model.code };
  }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreateProposalCommand(actionName, commandId, item);
        break;
      case Action.UPDATE:
        commandObject = new UpdateProposalCommand(actionName, commandId, item);
        break;
      case Action.DELETE:
        commandObject = new DeleteProposalCommand(actionName, commandId, item);
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async getAllByQuery(query, user) {
    const _query: any = {};
    const employee = await this.employeeClient.sendDataPromise(
      { where: { "account.id": user.id, active: true } },
      cmd2.EMPLOYEE.LISTENER.GET_BY_QUERY
    );
    if (employee && employee.length === 0) {
      throw new HttpException({ errorCode: "PROPRIMTX0001" }, HttpStatus.OK);
    }

    if (user.roles.includes(PermissionEnum.PROPOSAL_GET_ALL)) {
      const projectIds = await this.propertyClient.sendDataPromise(
        { id: user.id },
        CmdPatternConst.LISTENER.GET_PROJECT_BY_CUSTOMER_SERVICE
      );

      _query["$or"] = [
        { "escrowTicket.project.id": { $in: projectIds } },
        { "escrowTicket.project.f0.id": { $in: projectIds } },
      ];
    } else if (user.roles.includes(PermissionEnum.PROPOSAL_GET_ID)) {
      _query["escrowTicket.pos.id"] = employee.workingAt;
    }

    if (query.projectIds) {
      _query["escrowTicket.project.id"] = { $in: query.projectIds.split(",") };
    }

    // Trang thái
    if (query.status) {
      _query["status"] = { $in: query.status.split(",") };
    }
    if (query._fields) {
      _query._fields = query._fields;
    }
    if (query.search) {
      const search = this.escapeRegex(query.search);
      const regexSearch = new RegExp('.*' + search + '.*', 'i');
      _query.$or = [
        { name: { $regex: regexSearch } },
        { code: { $regex: regexSearch } },
      ];
    }
    if (query["page"] && query["pageSize"]) {
      const page = parseInt(query["page"]) || 1;
      const pageSize = parseInt(query["pageSize"]) || 10;
      _query.page = page;
      _query.pageSize = pageSize;
      _query.isPaging = true;
      return Promise.all([
        await this.proposalRepository.findAll(_query),
        await this.proposalRepository.countAll(_query),
      ]).then((res) => ({
        rows: res[0],
        total: res[1],
        page,
        pageSize,
        totalPages: Math.floor((res[1] + pageSize - 1) / pageSize),
      }));
    } else {
      return await this.proposalRepository.findAll(_query);
    }
  }

  async requestApproveProposal(user: any, dto: ProposalStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'request approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.proposalRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0027" }, HttpStatus.OK);
    }

    if (oldDto.status !== ProposalStatusEnum.INIT && oldDto.status !== ProposalStatusEnum.REJECTED) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0022" }, HttpStatus.OK);
    }
    if (dto.status !== ProposalStatusEnum.WAITING) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0021" }, HttpStatus.OK);
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;
    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto);
    return { id: dto.id };
  }

  async updateProposal(user: any, dto: UpdateProposalDto, actionName: string, authorization: any) {
    this.loggerService.log(this.context, clc.green("update service"));

    const oldDto: any = await this.proposalRepository.findOne({id: dto.id});
    const contract: any = await this.primaryContractService.getContractById(user, dto.contractId);

    if(!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Đơn đề nghị', 'id', 'null')
      });
    }
    if(dto.proposalDate && !moment(dto.proposalDate).toDate()){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, "Ngày tạo đơn", dto.proposalDate.toString())});
    }
    if(contract.primaryTransaction.id !== oldDto.escrowTicket.id){
      const escrowTicketDto: any = await this.proposalRepository.findOne({'escrowTicket.id': contract.primaryTransaction.id});
      if(escrowTicketDto){
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.EXISTED, 'Phiếu cọc', 'id', dto.escrowTicket.id)
        });
      }
      
      oldDto.contract = contract;
      oldDto.name = `${oldDto.code}-${contract.primaryTransaction.escrowTicketCode}-${contract.primaryTransaction.customer.personalInfo.name}`;
    }
   const customer = await this.createdAndUpdatedCustomer(dto.customerTransferFormat, authorization);
    if (customer && customer.data && customer.data.statusCode !== '0') {
      throw new HttpException(customer.data, customer.data.statusCode);
    }

    if(dto.reasonLiquidation){
      oldDto.reasonLiquidation = dto.reasonLiquidation;
    }

    const clonedTransaction = {
      ...contract.primaryTransaction,
      contract: {
        id: contract.id,
        name: contract.name,
        code: contract.code
      },
    };
    
    // thông tin vay
    if (dto.loanType && dto.loanType == "yes") {
      if (isNullOrUndefined(dto.loanBankInfo)) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0031" }, HttpStatus.OK);
      } else { 
        oldDto.loanBankInfo = dto.loanBankInfo;
      }

      if (!dto.loanAmount) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0032" }, HttpStatus.OK);
      } else {
        if (dto.loanAmount.toString().length > 15) {
          throw new HttpException({ errorCode: "PRIMARYCONTRACT0035" }, HttpStatus.OK);
        }
        oldDto.loanAmount = dto.loanAmount;
      }
      
      if (!dto.loanTermYear) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0033" }, HttpStatus.OK);
      } else {
        const value = dto.loanTermYear.toString();
        const parts = value.split('.');
        const decimalPart = parts[1];
        if(decimalPart) {
          if (decimalPart.length !== 1) {
            throw new HttpException({ errorCode: "PRIMARYCONTRACT0036" }, HttpStatus.OK);
          }
        }
        if (value.length > 4) {
          throw new HttpException({ errorCode: "PRIMARYCONTRACT0034" }, HttpStatus.OK);
        }
        oldDto.loanTermYear = dto.loanTermYear;
      }
    }
    oldDto.loanType = dto.loanType;
    
    oldDto.escrowTicket = clonedTransaction;
    oldDto.proposalDate = dto.proposalDate;
    oldDto.note = dto.note;
    if(dto.customerTransfer) oldDto.customerTransfer = dto.customerTransfer;
    if(dto.customerTransferFormat) oldDto.customerTransferFormat = dto.customerTransferFormat;
    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto);
    return { id: dto.id };
  }
  
  async approveProposal(user: any, dto: ProposalStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.proposalRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0005" },
        HttpStatus.OK
      );
    }

    if (oldDto.status !== ProposalStatusEnum.WAITING) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0022" },
        HttpStatus.OK
      );
    }
    if (dto.status !== ProposalStatusEnum.APPROVED && dto.status !== ProposalStatusEnum.REJECTED) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0022" },
        HttpStatus.OK
      );
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;
    oldDto.reasonReject = dto.reasonReject;
    await this.propertyClient.sendDataPromise({
      id: oldDto.escrowTicket?.propertyUnit?.id
    }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.LIQUIDATION_PROPOSAL_APPROVED);

    if(dto.status === ProposalStatusEnum.APPROVED) { 
      const { personalInfo } = oldDto.customerTransferFormat;
      const { escrowTicket } = oldDto;

      /* kiểm tra user được chuyển nhượng đã tồn tại hoặc bị inactive trong care customer ? 
        * note: k sử dung async/await để tránh làm ảnh hưởng performance của api 
        */
      this.careClient.sendDataPromise({ email: personalInfo.email, phone: personalInfo.phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((existentCustomer)=> { 
        // nếu chưa cố, tạo mới customer 
        if(!existentCustomer) { 
          const payload = { personalInfo, accessSystem: user.notiSystem ? [user.notiSystem] : null };
          this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

        }else if(!existentCustomer.isActive){ 
          // nếu bị inactive  => active customer
          this.careClient.sendDataPromise({ id: existentCustomer.id, isActive: true }, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
        }
      });
      
      // kiêm tra user chuyển nhượng còn hợp đồng không phải là trạng thái thanh lý
      const { identities} = personalInfo; 
      if(identities && identities.length){  
        const identitiesArr = identities.map(identity => {
          return identity.value
        });

        // note: k sử dung async/await để tránh làm ảnh hưởng performance của api
        this.primaryContractService.getContractsWithStatusForCareCustomer(identitiesArr).then((contracts)=> { 
          
          /* nếu congtracts length == 1, compare id với contract id trong proposal
            * disable account của user chuyển nhượng nếu tồn tại
            */ 
          if(!contracts || contracts.length === 1) {  
            const { id: contractId  } = contracts[0]; 
            if(contractId === escrowTicket.contract.id) { 
              this.careClient.sendDataPromise({ email: personalInfo.email, phone: personalInfo.phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((existentCustomer)=> {  
                if(existentCustomer) { 
                  this.careClient.sendDataPromise({ id: existentCustomer.id, isActive: false }, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
                }
              });  

            }
            
            
          }
        });
      }
    }

    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto);
    
    // Lưu vào lịch sử sản phẩm
    let reason = '';
    let contractStatus = '';
    let actionNameUnit = '';
    if (dto.status === ProposalStatusEnum.APPROVED) {
      reason = oldDto.reasonLiquidation === 'Đổi tên chuyển nhượng' ? 'Thanh lý chuyển nhượng' + ' (' + oldDto.customerTransfer.name + ')' : 'Thanh lý chấm dứt';
      if (oldDto.reasonLiquidation === 'Đổi tên chuyển nhượng') {
        reason = 'Thanh lý chuyển nhượng' + ' (' + oldDto.customerTransfer.name + ')';
        contractStatus = 'Hợp đồng: ' + ContractTypeEnum.APPROVED;
      } else {
        reason = 'Thanh lý chấm dứt';
        contractStatus = 'Hợp đồng: ' + ContractTypeEnum.APPROVED;
      }
      actionNameUnit = 'Duyệt đơn đề nghị thanh lý';
    } else if (dto.status === ProposalStatusEnum.REJECTED) {
      reason = dto.reasonReject;
      contractStatus = 'Hợp đồng: ' + ContractTypeEnum.APPROVED;
      actionNameUnit = 'Từ chối đơn đề nghị thanh lý';
    }
    let histories = {
      propertyUnitId: oldDto.escrowTicket.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: reason,
      contractStatus: contractStatus,
      actionName: actionNameUnit,
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return true;
  }

  async findByContract(contractId) {
    const proposal = await this.proposalRepository.findOne({
      "escrowTicket.contract.id": contractId,
      "status": ProposalStatusEnum.APPROVED
   });
   if (!proposal) {
    throw new HttpException({ errorCode: "PRIMARYCONTRACT0027" }, HttpStatus.OK);
   }
   return proposal;
  }

  async deleteProposal(user: any, id: string, actionName: string) {
    this.loggerService.log(this.context, clc.green("delete service"));
    const model: any = { id };

    const oldDto: any = await super.findOne({id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, "Đơn đề nghị", "ID", id)});
    }
    if (oldDto.status !== ProposalStatusEnum.INIT) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "proposalStatus")});
    }

    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    await this.executeCommand(Action.DELETE, actionName, this.commandId, model);
    return { id: id };
  }
}
