import * as mongoose from 'mongoose';
import { DepositContractFormEnum, DepositContractTypeEnum, DistributionChannelEnum, DivisionEnum, OrgchartEnum } from '../../shared/enum/deposit-contract.enum';
import uuid = require('uuid');

const Bank = new mongoose.Schema({
    code: { type: String },
    name: { type: String },
    accountNumber: { type: String },
    beneficiary: { type: String },
}, { _id: false });

const Installments = new mongoose.Schema({
    id: { type: String, default: uuid.v4, index: true },
    name: { type: String },
    paymentType: { type: String },
    type: { type: String },
    value: { type: Number },
    expiredDateType: { type: String },
    exactDays: { type: Date },
    expiredDays: { type: Number },
    description: { type: String },
    convertedAmount: { type: Number },
    transactionSuccessful: { type: Boolean, default: false },
}, { _id: false });


export const DepositContractSchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  // Tab thông tin chung
  code: { type: String, index: true },  //	Mã hợp đồng 
  projectId: { type: String, index: true },          // id dự án ký quỹ
  orgchart:{                            // Đơn vị ký quỹ
    id: { type: String },
    code: { type: String },
    name: { type: String },
    taxCode: { type: String },
    bpID: { type: String },
    email: { type: String },
    type: { type: OrgchartEnum, index: true },
  },
  orgchartPartnerId: { type: String, index: true, default: '' }, // Đơn vị hợp tác
  bank: { type: Bank },                             // Thông tin ngân hàng
  depositSignedDate: { type: Date, default: () => new Date() }, // Ngày ký kết
  depositForm: { type: DepositContractFormEnum, index: true }, // Hình thức quỹ
  depositTime: { type: Number, default: 0 }, // Thời gian quỹ (tháng)
  depositAmount: { type: Number, default: 0 }, // Số tiền quỹ
  // startDate: { type: Date, default: () => new Date() }, // Ngày hiệu lực hợp đồng 
  // expiredDate: { type: Date, default: () => new Date() }, // Ngày hiệu lực hợp đồng 
  // type: { type: DepositContractTypeEnum, default: DepositContractTypeEnum.ZH05 }, //Loại hợp đồng (ZH05 - SO phí môi giới BĐS, ZL03 - Leasing PMG)
  // POnumber: { type: String, index: true }, // Mã hợp đồng bản cứng
  // distributionChannel: { type: Number }, //Kênh phân phối
  // division: { type: Number }, //Ngành hàng
  salePolicyId: { type: String, index: true }, // Chính sách phí môi giới

  //Tab sản phẩm ký quỹ
  propertyUnitIds: { type: Array, default: []},
  propertyQuantity: { type: Number, default: 0 }, // Số lượng sản phẩm ký quỹ

  //Tab đợt thanh toán ký quỹ
  installments: { type: [Installments], default: [] },

  files: [
    {
      name: { type: String },
      url: { type: String },
      absoluteUrl: { type: String },
      uploadName: { type: String }
    }
  ],

  status: { type: Number, default: 1 },
  softDelete: { type: Boolean, default: () => false },
  createdDate: { type: Date, default: () => new Date() },
  modifiedDate: { type: Date, default: () => new Date() },
  createdBy: { type: String },
  modifiedBy: { type: String }
});

DepositContractSchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});