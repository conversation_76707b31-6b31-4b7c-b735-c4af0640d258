import { forwardRef, Module } from '@nestjs/common';
import { DebtCommissionController } from './controller';
import { DebtCommissionService } from './service';
import { DebtCommissionProviders } from './providers/debt-commission.providers';
import { EmployeeQuerySideModule } from '../employee.query/module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LoggerModule } from '../logger/logger.module';
import { CodeGenerateModule } from '../code-generate/module';
import { SharedModule } from '../../../shared-modules';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { DebtCommissionRepository } from './repository/repository';
import { DebtCommissionListRepository } from '../debt-commission-list/repository/debt-commission-list.repository';
import { DebtCommissionListModule } from '../debt-commission-list/module';
import { DebtCommissionPolicyModule } from '../debt-commission-policy/module';
// import { DebtCommissionListModule } from '../debt-commission-list/module';

@Module({
  imports: [
    QueryDatabaseModule,
    MgsSenderModule,
    EmployeeQuerySideModule,
    LoggerModule,
    CodeGenerateModule,
    SharedModule,
    forwardRef(() => DebtCommissionListModule),
    DebtCommissionPolicyModule
  ],
  controllers: [
    DebtCommissionController,
  ],
  providers: [
    DebtCommissionService,
    DebtCommissionRepository,
    // DebtCommissionListRepository,
    ...DebtCommissionProviders,
  ],
  exports: [
    DebtCommissionService,
    DebtCommissionRepository,
    DebtCommissionModule
  ]
})
export class DebtCommissionModule { }