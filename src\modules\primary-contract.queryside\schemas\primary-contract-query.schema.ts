import * as mongoose from "mongoose";
import { TransactionSchema } from "../../shared/schemas/common.schema";
import uuid = require("uuid");
import { AssignStatusEnum, DebtHistoryStatusEnum, StatusContractEnum } from "../../shared/enum/primary-contract.enum";
import {
  HandoverQualityEnum,
  HandoverStatusEnum,
  HandoverDeliveryItemType,
} from "../../shared/enum/status.enum";

const deliveryItem = new mongoose.Schema(
  {
    title: { type: String },
    description: { type: String },
    type: { type: HandoverDeliveryItemType },
    isPass: { type: Boolean },
    indexValue: { type: String },
    reason: { type: String },
    image: { type: String },
    files: { type: [Object], default: [] },
  },
  { _id: false }
);

const deliveryGroup = new mongoose.Schema(
  {
    name: { type: String },
    list: { type: [deliveryItem] },
  },
  { _id: false }
);

const deliveryHistory = new mongoose.Schema(
  {
    deliveryDate: { type: Date },
    type: { type: HandoverDeliveryItemType },
    isPass: { type: Boolean },
    indexValue: { type: String },
    items: { type: [deliveryGroup] },
    files: { type: [Object], default: [] },
  },
  { _id: false }
);

const deliveryResult = new mongoose.Schema(
  {
    deliveryDate: { type: Date },
    quality: { type: HandoverQualityEnum },
    employeeQuality: { type: HandoverQualityEnum },
    comment: { type: String },
    rate: { type: Number, default: 0 },
  },
  { _id: false }
);

const handoverScheduleSchema = new mongoose.Schema(
  {
    id: { type: String },
    handoverStartTime: { type: Date }, // khung thời gian bàn giao
    handoverEndTime: { type: Date }, // khung thời gian bàn giao
    supportEmployee: { type: Object },
  },
  { _id: false }
);

const RecordSchema = new mongoose.Schema(
  {
    title: { type: String },
    detail: { type: String },
  },
  { _id: false }
);

const CustomerSchema = new mongoose.Schema(
  {
    name: { type: String },
    gender: { type: String },
    address: { type: String },
    phone: { type: String },
  },
  { _id: false }
);

const HistorySchema = new mongoose.Schema(
  {
    status: { type: DebtHistoryStatusEnum },
    type: { type: String },
    data: { type: Object },
    createdBy: { type: Object, default: {} },
    createdDate: { type: Date }
  },
  { _id: false }
);

const DebtHistorySchema = new mongoose.Schema(
  {
    customer: { type: CustomerSchema },
    status: { type: DebtHistoryStatusEnum },
    histories: { type: [HistorySchema], default: [] },
  },
  { _id: false }
);

const AssignHistorySchema = new mongoose.Schema(
  {
    debtCollector: { type: Object },
    assignStatus: { type: String },
    assigner: { type: String },
  },
  { _id: false }
);

export const PrimaryContractQuerySchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },

  name: { type: String, index: true },
  code: { type: String, index: true },
  status: { type: String, index: true, default: StatusContractEnum.INIT },
  startDate: { type: Date, index: true },
  expiredDate: { type: Date },
  signedDate: { type: Date, default: () => Date.now() },
  primaryTransaction: { type: TransactionSchema },
  policyPayment: { type: Object },
  policyDiscount: { type: Object },
  policyDiscounts: { type: Array, default: [] },

  calcCurrencyFirst: { type: Boolean, default: false },
  calcPriceVat: { type: Boolean, default: false },
  calcContractPrice: { type: Boolean, default: false },
  maintenanceFee: {
    type: { type: String },
    value: { type: Number, default: 0 },
    stagePayment: { type: String },
  },

  oldContract: { type: Object },
  isTransferred: { type: Boolean, default: false },
  currency: { type: String, default: "VND" },
  type: { type: String, default: "deposit" },
  transferType: { type: String },
  reason: { type: String },
  isDebtRemind: { type: Boolean, default: true },
  isShowedInstallment: { type: Boolean, default: true },
  hasInterest: { type: Boolean, default: false },
  interest: { type: Object },
  depositConfirmFromCustomer: { type: Boolean, default: false },
  transferConfirmFromCustomer: { type: Boolean, default: false },
  liquidation: { type: Object },

  interestCalculations: { type: [Object], default: [] },
  description: { type: String, default: "" },
  active: { type: Boolean, default: true },
  createdBy: { type: String, default: "", index: true },
  createdDate: { type: Date },
  modifiedBy: { type: String, default: null },
  modifiedDate: { type: Date, default: () => Date.now() },
  deposit: { type: Object },
  purchase: { type: Object },
  files: { type: [Object], default: [] },
  filesDelivery: { type: [Object], default: [] },
  paymentPercent: { type: Number, default: 0 },
  deliveryItems: { type: [deliveryGroup] },
  eligibleFiles: { type: [Object], default: [] },
  cerInProcessFiles: { type: [Object], default: [] },
  cerReadyHandoverFiles: { type: [Object], default: [] },
  eligibleItems: { type: [deliveryGroup] },
  cerInProcessItems: { type: [deliveryGroup] },
  cerReadyHandoverItems: { type: [deliveryGroup] },
  deliveryDate: { type: Date },
  isSendDelivery: { type: Boolean, default: false },
  isCerSendDelivery: { type: Boolean, default: false },
  deliveryHistories: { type: [deliveryHistory], default: [] },
  deliveryResult: { type: deliveryResult },
  handoverStatus: {
    type: HandoverStatusEnum,
    default: HandoverStatusEnum.init,
  },
  cerHandoverStatus: { type: String },
  cerHandoverSchedule: { type: handoverScheduleSchema, default: null },
  handoverSchedule: { type: handoverScheduleSchema, default: null },
  companyInformation: { type: Object },
  tradeHistory: { type: [Object] },
  pathDtt: { type: [Array] },
  syncErpData: { type: Object },
  changeInstallment: { type: Boolean, default: false },
  releaseStartDate: { type: Date },
  releaseEndDate: { type: Date },
  liquidate: { type: Date },

  // add new
  orgCode: { type: String, default: "" },
  transactionSuccess: { type: Boolean, default: false },
  transactionState: { type: String, default: "" },
  salesPolicy: { type: Object, default: {} },
  employeeRole: { type: String, default: "" },
  employeeRevenueRate: { type: Number, default: 0 },
  staffsInvolved: { type: [Object], default: [] },
  isInterestExemption: { type: Boolean, default: false },
  proposal: { type: Object, default: {} },//loại trừ hợp đồng khỏi danh mục công nợ //phục hồi hợp đồng vào danh sách công nợ

  // debt collector
  debtCollector: { type: Object, default: {} }, // nv công nợ
  assignHistories: { type: [AssignHistorySchema], default: [] }, // lịch sử phân bổ nv công nợ
  assignStatus: { type: String, default: AssignStatusEnum.PENDING }, // trạng thái phân bổ hđ
  callRecords: { type: [RecordSchema], default: [] },
  notes: { type: [RecordSchema], default: [] },
  debtHistory: { type: DebtHistorySchema, default: {} }, // lịch sử công nợ

  eapStatus: { type: String },
  feeAdvanceAmount: { type: Number },//Tiền tạm ứng lệ phí trước bạ	
  interestExemptionReason: { type: String },
  restoreinterestReason: { type: String },

  isAddCoOwnerShipInfo: { type: Boolean, default: false },
  isAddCompanyInfo: { type: Boolean, default: false },

  //variable for SAP
  poNumber: { type: String, default: "" },
  sapCode: { type: String, default: "" },
  businessArea: { type: Object, default: {}},
  distributionChannel: { type: Object, default: {}},
  productCategory: { type: Object, default: {}},
  discountValue: { type: Number, default: 0 },
  issuedPrice: { type: Number, default: 0 },
  productPrice: { type: Number, default: 0 },

  //Thêm mới phần cho vay
  loanType: { type: String, default: "" },
  loanBankInfo: { type: Object, default: {}},
  loanAmount: { type: Number, default: 0 },
  loanTermYear: { type: Number, default: 0 },
});

PrimaryContractQuerySchema.pre("save", function (next) {
  this._id = this.get("id");
  next();
});

// =====
