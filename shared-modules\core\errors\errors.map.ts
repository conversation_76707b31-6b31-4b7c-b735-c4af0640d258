// error code for common system


// Định nghĩa kiểu dữ liệu cho mỗi mục trong ErrorsMap
type ErrorInfo = {
  statusCode: string;
  message: string;
};

export const ErrorsMap: Record<string, ErrorInfo> = {
  '0': { statusCode: '0', message: 'Success' },

  //Common
  COME0001: { statusCode: 'COME0001', message: 'We\'re busy at the moment. Please try again later' },
  COME0002: { statusCode: 'COME0002', message: 'No data' },
  COME0003: { statusCode: 'COME0003', message: 'Forbidden' },
  COME0004: { statusCode: 'COME0004', message: 'Unauthorized' },
  COME0006: { statusCode: 'COME0006', message: 'User access denied' },
  COME0005: { statusCode: 'COME0005', message: 'Data is duplicated' },
  COME0007: { statusCode: 'COME0007', message: 'Code is duplicated' },
  COME0008: { statusCode: 'COME0008', message: 'Name is duplicated' },
  COME0009: { statusCode: 'COME0009', message: 'Date From cannot be greater than Date To' },
  COME0010: { statusCode: 'COME0010', message: 'Phone is duplicated' },
  COME0011: { statusCode: 'COME0011', message: 'Generate code failed. No POS code found' },
  COME0012: { statusCode: 'COME0012', message: 'Code is duplicated' },
  COME0013: { statusCode: 'COME0013', message: 'Integration error' },
  COME0014: { statusCode: 'COME0014', message: 'Exceeded the number of supported items' },
  //Orgchart
  ORGE0001: { statusCode: 'ORGE0001', message: 'The name of the business cooperation unit already exists' },
  ORGE0002: { statusCode: 'ORGE0002', message: 'The code of the business cooperation unit already exists' },
  ORGE0003: { statusCode: 'ORGE0003', message: 'The tax code of the business cooperation unit already exists' },
  ORGE0004: { statusCode: 'ORGE0004', message: 'Orgchart not found' },
  ORGE0005: { statusCode: 'ORGE0005', message: 'Please remove the list of affiliated employees before disabling the unit' },
  ORGE0006: { statusCode: "ORGE0006", message: "Please remove all affiliated employees before deleting the unit" },
  ORGE0007: { statusCode: "ORGE0007", message: "{name} is missing" },
  ORGE0008: { statusCode: "ORGE0008", message: "Invalid Email" },

  //Sts
  STSE0001: { statusCode: 'STSE0001', message: 'Role name is duplicated' },
  STSE0002: { statusCode: 'STSE0002', message: 'Role code is duplicated' },
  STSE0003: { statusCode: 'STSE0003', message: 'Role not found' },
  STSE0004: { statusCode: 'STSE0004', message: 'Role ID format is invalid' },
  STSE0005: { statusCode: 'STSE0005', message: 'User hasn\'t account yet' },
  STSE0006: { statusCode: 'STSE0006', message: "Account haven't any roles" },
  STSE0007: { statusCode: 'STSE0007', message: "Account haven't any permissions to access other account info" },
  STSE0008: { statusCode: 'STSE0008', message: 'Role group name is duplicated' },

  //Project
  PRJE0001: { statusCode: 'PRJE0001', message: 'Project code is duplicated' },
  PRJE0002: { statusCode: 'PRJE0002', message: 'Project ID does not exist' },
  PRJE0003: { statusCode: 'PRJE0003', message: 'Block code is duplicated' },
  PRJE0004: { statusCode: 'PRJE0004', message: "Project code is not editable due to project type 'investment'" },
  PRJE0005: { statusCode: 'PRJE0005', message: "Missing value Project code" },
  PRJE0006: { statusCode: 'PRJE0006', message: "Missing value Block code" },
  PRJE0007: { statusCode: 'PRJE0007', message: "Duplicate floor display name" },
  PRJE0008: { statusCode: 'PRJE0008', message: "Block ID does not exist" },
  PRJE0009: { statusCode: 'PRJE0009', message: 'Project name is duplicated' },
  PRJE0010: { statusCode: 'PRJE0010', message: 'Investor not found' },
  PRJE0011: { statusCode: 'PRJE0011', message: 'Project not found' },
  PRJE0012: { statusCode: 'PRJE0012', message: 'Field projectId cannot be empty' },
  PRJE0013: { statusCode: 'PRJE0013', message: 'Project has not selected a unit that can be revoked, please check!' },
  PRJE0014: { statusCode: 'PRJE0014', message: 'The unit has already been selected as a revocable unit, please check!' },
  PRJE0015: { statusCode: 'PRJE0015', message: 'The unit does not exist in the project, please check!' },
  PRJE0016: { statusCode: 'PRJE0016', message: 'rootPosId does not exist' },
  PRJE0017: { statusCode: 'PRJE0017', message: 'Field projectId cannot be empty' },
  PRJE0018: { statusCode: 'PRJE0018', message: 'Project email template not found' },

  PRJEDOCFOL001: { statusCode: 'PRJEDOCFOL001', message: 'Project document folder not found' },
  PRJEDOCFOL002: { statusCode: 'PRJEDOCFOL002', message: 'Project document folder is duplicated' },

  //Masterdata
  MTDE0001: { statusCode: 'MTDE0001', message: 'Folder not found' },
  MTDE0002: { statusCode: 'MTDE0002', message: 'File name exist' },
  MTDE0003: { statusCode: 'MTDE0003', message: 'Investor not found' },
  MTDE0004: { statusCode: 'MTDE0004', message: 'Investor code is duplicated' },
  MTDE0005: { statusCode: 'MTDE0005', message: 'The default folder is not deleted' },

  PRDCT0001: { statusCode: 'PRDCT0001', message: 'Product code is duplicated' },
  PRDCT0002: { statusCode: 'PRDCT0002', message: 'Product ID does not exist' },
  PRDCT0003: { statusCode: 'PRDCT0003', message: 'Product location not found on row table' },
  PRDCT0004: { statusCode: 'PRDCT0004', message: 'No file uploaded' },
  PRDCT0005: { statusCode: 'PRDCT0005', message: 'Uploaded file is not an Excel file' },

  //Employee
  EMPE0001: { statusCode: 'EMPE0001', message: 'Employee code is duplicated' },
  EMPE0002: { statusCode: 'EMPE0002', message: 'Employee not found' },


  //Demand-customer
  DCUSE0001: { statusCode: 'DCUSE0001', message: 'Phone number is duplicated' },
  DCUSE0002: { statusCode: 'DCUSE0002', message: 'No customer found' },
  DCUSE0003: { statusCode: 'DCUSE0003', message: 'Customer status is not NORMAL' },
  DCUSE0004: { statusCode: 'DCUSE0004', message: 'Cannot modified information of this demand customer (CUST_DONE)' },
  DCUSE0005: { statusCode: 'DCUSE0005', message: 'The maximum number of banks must not exceed 10' },
  DCUSE0006: { statusCode: 'DCUSE0006', message: '{name} không được để trống' },
  DCUSE0007: { statusCode: 'DCUSE0007', message: '{name} vượt quá ký tự tối đa' },
  DCUSE0008: { statusCode: 'DCUSE0008', message: '{name} sai định dạng' },
  DCUSE0009: { statusCode: 'DCUSE0009', message: '{name} không tồn tại' },
  DCUSE0010: { statusCode: 'DCUSE0009', message: 'File của bạn không đúng với template yêu cầu' },

  //Lead repo
  LREPOE0001: { statusCode: 'LREPOE0001', message: 'Lead Config is being used' },
  LREPOE0002: { statusCode: 'LREPOE0002', message: 'Lead Repository Name exists' },
  LREPOE0003: { statusCode: 'LREPOE0003', message: 'Lead Repository not found' },
  LREPOE0004: { statusCode: 'LREPOE0004', message: 'Lead Repository config not found' },
  LREPOE0005: { statusCode: 'LREPOE0005', message: 'Invalid exploit time' },
  LREPOE0006: { statusCode: 'LREPOE0006', message: 'Invalid config or configHot' },

  //Investor
  INVTOR0001: { statusCode: 'INVTOR0001', message: 'Investor is attached to the project, this investor cannot be deleted' },

  //Sales program
  SPROGRAM0001: { statusCode: 'SPROGRAM0001', message: 'Sales Program code is duplicated' },
  SPROGRAM0002: { statusCode: 'SPROGRAM0002', message: 'Sales Program name is duplicated' },
  SPROGRAM0003: { statusCode: 'SPROGRAM0003', message: 'Sales Program ID does not exist' },
  SPROGRAM0004: { statusCode: 'SPROGRAM0004', message: 'InternalOrgchart does not exist' },
  SPROGRAM0005: { statusCode: 'SPROGRAM0005', message: 'Only one sales program can be selected.' },
  SPROGRAM0006: { statusCode: 'SPROGRAM0006', message: 'Cannot delete the saleProgram because it is being used in the sales program.' },
  SPROGRAM0007: { statusCode: 'SPROGRAM0007', message: 'Field salesProgramIds cannot be empty' },
  SPROGRAM0008: { statusCode: 'SPROGRAM0008', message: 'Sales program does not exist in the project' },
  SPROGRAM0009: { statusCode: 'SPROGRAM0009', message: 'The sales program does not meet the requirements' },
  SPROGRAM0010: { statusCode: 'SPROGRAM0010', message: 'This sales program is currently locked' },
  SPROGRAM0011: { statusCode: 'SPROGRAM0011', message: 'Field saleProgramId cannot be empty' },
  SPROGRAM0012: { statusCode: 'SPROGRAM0012', message: 'Sales Program not found' },

  //Lead
  LEADE0001: { statusCode: 'LEADE0001', message: 'Lead Config is being used' },
  LEADE0002: { statusCode: 'LEADE0002', message: 'Lead ID not found' },
  LEADE0003: { statusCode: 'LEADE0003', message: 'Invalid assignee. User does not have permission to update this lead' },
  LEADE0004: { statusCode: 'LEADE0004', message: 'Lead is not assigned' },
  LEADE0005: { statusCode: 'LEADE0005', message: 'Invalid assignee code' },
  LEADE0006: { statusCode: 'LEADE0006', message: 'Current user does not have permission to transfer lead' },

  //Lead source
  LEADS0001: { statusCode: 'LEADS0001', message: 'Lead source already exists' },
  LEADS0002: { statusCode: 'LEADS0002', message: 'Lead source not found' },
  LEADS0003: { statusCode: 'LEADS0003', message: 'Lead source in use' },

  //Customer
  CUS0001: { statusCode: 'CUS0001', message: 'Customer does not exist' },
  CUS0002: { statusCode: 'CUS0002', message: 'BankInfo over length' },
  CUS0003: { statusCode: 'CUS0003', message: 'Customer invalid' },
  CUS0004: { statusCode: 'CUS0004', message: 'PersonalInfo phone is required' },
  CUS0005: { statusCode: 'CUS0005', message: 'Company name is required' },
  CUS0006: { statusCode: 'CUS0006', message: 'PersonalInfo name is required' },
  CUS0007: { statusCode: 'CUS0007', message: 'Phone number is duplicated' },
  CUS0008: { statusCode: 'CUS0008', message: 'Identification document number exists in other official customers' },
  CUS0009: { statusCode: 'CUS0009', message: 'Tax code number exists in other official customers' },
  CUS0010: { statusCode: 'CUS0010', message: 'identities is not empty' },
  CUS0011: { statusCode: 'CUS0011', message: 'PersonalInfo.identities is not empty' },
  CUS0012: { statusCode: 'CUS0012', message: 'tax code is not empty' },
  CUS0013: { statusCode: 'CUS0013', message: 'bankInfo invalid' },
  //Property-unit
  PROUNIT0001: { statusCode: 'PROUNIT0001', message: 'Property unit code is duplicated' },
  PROUNIT0002: { statusCode: 'PROUNIT0002', message: 'Property unit name is duplicated' },
  PROUNIT0003: { statusCode: 'PROUNIT0003', message: 'Property unit ID does not exist' },
  PROUNIT0004: { statusCode: 'PROUNIT0004', message: 'Failed to import property unit' },
  PROUNIT0005: { statusCode: 'PROUNIT0005', message: 'Partial error in importing property unit' },
  PROUNIT0006: { statusCode: 'PROUNIT0006', message: 'Waiting to import property unit' },
  PROUNIT0007: { statusCode: 'PROUNIT0007', message: 'Property unit is not in COMING status' },
  PROUNIT0008: { statusCode: 'PROUNIT0008', message: 'Property unit does not belong to the sales program' },
  PROUNIT0009: { statusCode: 'PROUNIT0009', message: 'Project does not exist' },
  PROUNIT0010: { statusCode: 'PROUNIT0010', message: 'Property unit status is not valid' },
  PROUNIT0011: { statusCode: 'PROUNIT0011', message: 'Property unit has been registered' },
  PROUNIT0012: { statusCode: 'PROUNIT0012', message: 'Not enough time to re-register this Property unit' },
  PROUNIT0013: { statusCode: 'PROUNIT0013', message: 'Exceeded number of permissions allowed' },
  PROUNIT0014: { statusCode: 'PROUNIT0014', message: 'Property unit already has a primary transaction' },
  PROUNIT0015: { statusCode: 'PROUNIT0015', message: 'different pos invalid' },
  PROUNIT0016: { statusCode: 'PROUNIT0016', message: 'primary status invalid' },
  PROUNIT0017: { statusCode: 'PROUNIT0017', message: 'Unsuccessful' },
  PROUNIT0018: { statusCode: 'PROUNIT0018', message: 'User do not have permission to assign priority' },
  PROUNIT0019: { statusCode: 'PROUNIT0019', message: 'Property unit does not exist' },
  PROUNIT0020: { statusCode: 'PROUNIT0020', message: 'User do not have permission to remove priority' },
  PROUNIT0021: { statusCode: 'PROUNIT0021', message: 'This ticket has already been assigned a priority in another unit.' },
  PROUNIT0022: { statusCode: 'PROUNIT0022', message: 'This ticket not found.' },
  PROUNIT0023: { statusCode: 'PROUNIT0023', message: 'A ticket cannot be assigned more than one priority.' },
  PROUNIT0024: { statusCode: 'PROUNIT0024', message: 'The ticket has not fulfilled the required payment amount.' },
  PROUNIT0025: { statusCode: 'PROUNIT0025', message: 'The transaction slip is invalid or has been rejected' },

  //Block
  BLOCK0001: { statusCode: 'BLOCK0001', message: 'Block code is duplicated' },
  BLOCK0002: { statusCode: 'BLOCK0002', message: 'Block name is duplicated' },
  BLOCK0003: { statusCode: 'BLOCK0003', message: 'Block ID does not exist' },

  //Sale-policy
  SALEPOLICY0001: { statusCode: 'SALEPOLICY0001', message: 'Sale Policy does not exist' },
  SALEPOLICY0002: { statusCode: 'SALEPOLICY0002', message: 'Sale Policy code existed' },
  SALEPOLICY0003: { statusCode: 'SALEPOLICY0003', message: 'Sale Policy does not change status or delete' },
  SALEPOLICY0004: { statusCode: 'SALEPOLICY0004', message: 'Sale Policy code is not null' },
  SALEPOLICY0005: { statusCode: 'SALEPOLICY0005', message: 'ListProjectId is not null' },
  SALEPOLICY0006: { statusCode: 'SALEPOLICY0006', message: 'StartDate is not null' },
  SALEPOLICY0007: { statusCode: 'SALEPOLICY0007', message: 'period invalid' },

  //Form
  FORMFILE0001: { statusCode: 'FORMFILE0001', message: 'File name already exists' },
  FORMFILE0002: { statusCode: 'FORMFILE0002', message: 'File does not exists' },
  FORMFILE0003: { statusCode: 'FORMFILE0003', message: 'Unit already exists in Form' },

  //teamplate
  FILETEMPLATE0001: { statusCode: 'FILETEMPLATE0001', message: 'Failed to fill template, please try again later' },
  FILETEMPLATE0002: { statusCode: 'FILETEMPLATE0002', message: 'Failed to fetch file, please try again later' },
  FILETEMPLATE0003: { statusCode: 'FILETEMPLATE0003', message: 'Failed to convert to PDF' },

  //Policy
  POLI0001: { statusCode: 'POLI0001', message: 'Policy ID does not exist' },
  POLI0002: { statusCode: 'POLI0002', message: 'Policy name is duplicated' },
  POLI0003: { statusCode: 'POLI0003', message: 'exactDays must not be earlier than the current date' },
  POLI0004: { statusCode: 'POLI0004', message: 'Cannot retrieve submission number' },
  POLI0005: { statusCode: 'POLI0005', message: 'The policy already has a submission number' },
  POLI0006: { statusCode: 'POLI0006', message: 'Invalid account and department information' },
  POLI0007: { statusCode: 'POLI0007', message: 'Failures in policy submission preparation' },
  POLI0008: { statusCode: 'POLI0008', message: 'Unable to draft a proposal. This policy has a draft proposal' },
  POLI0009: { statusCode: 'POLI0009', message: 'The policy has not been assigned a number yet' },
  POLI0010: { statusCode: 'POLI0010', message: 'The proposal type does not match the policy type' },
  POLI0011: { statusCode: 'POLI0011', message: 'Name of installment is duplicate' },
  POLI0012: { statusCode: 'POLI0012', message: `Name of installment can not be 'khác'` },
  POLI0013: { statusCode: 'POLI0013', message: `The expiration date must be greater than or equal to the current date.` },
  POLI0014: { statusCode: 'POLI0014', message: `Policy is already linked to a contract.` },

  //Commission-policy
  COMMISSIONPOLICY0001: { statusCode: 'COMMISSIONPOLICY0001', message: 'Invalid date' },
  COMMISSIONPOLICY0002: { statusCode: 'COMMISSIONPOLICY0002', message: 'List rate can not empty' },
  COMMISSIONPOLICY0003: { statusCode: 'COMMISSIONPOLICY0003', message: 'List rate contains errors' },
  COMMISSIONPOLICY0004: { statusCode: 'COMMISSIONPOLICY0004', message: 'commissionPolicy does not exist' },
  COMMISSIONPOLICY0005: { statusCode: 'COMMISSIONPOLICY0005', message: 'end date is less than current date' },
  COMMISSIONPOLICY0006: { statusCode: 'COMMISSIONPOLICY0006', message: 'end date is less than start date' },
  COMMISSIONPOLICY0007: { statusCode: 'COMMISSIONPOLICY0007', message: 'bottomPrice can not empty' },
  COMMISSIONPOLICY0008: { statusCode: 'COMMISSIONPOLICY0008', message: 'bottomPrice is over length' },
  COMMISSIONPOLICY0009: { statusCode: 'COMMISSIONPOLICY0009', message: 'calculateType can not empty' },
  COMMISSIONPOLICY0010: { statusCode: 'COMMISSIONPOLICY0010', message: 'calculateType invalid' },
  COMMISSIONPOLICY0011: { statusCode: 'COMMISSIONPOLICY0011', message: 'end date is less than start date' },
  COMMISSIONPOLICY0012: { statusCode: 'COMMISSIONPOLICY0012', message: 'topPrice can not empty' },
  COMMISSIONPOLICY0013: { statusCode: 'COMMISSIONPOLICY0013', message: 'topPrice is over length' },
  COMMISSIONPOLICY0014: { statusCode: 'COMMISSIONPOLICY0014', message: 'bottomPrice is less than bottomPrice' },
  COMMISSIONPOLICY0015: { statusCode: 'COMMISSIONPOLICY0015', message: 'listUser invalid' },
  COMMISSIONPOLICY0017: { statusCode: 'COMMISSIONPOLICY0017', message: 'period invalid' },
  COMMISSIONPOLICY0018: { statusCode: 'COMMISSIONPOLICY0018', message: 'Commission policy type is not selected' },
  COMMISSIONPOLICY0019: { statusCode: 'COMMISSIONPOLICY0019', message: 'Can not deactive this comm policy' },

  //
  PRJORGCHARTUSING0001: { statusCode: 'PRJORGCHARTUSING0001', message: 'Organization chart is not valid' },

  //Indicator
  INDICATOR0001: { statusCode: 'INDICATOR0001', message: 'List Pos is required' },
  INDICATOR0002: { statusCode: 'INDICATOR0002', message: 'indicator id does not exist' },
  INDICATOR0003: { statusCode: 'INDICATOR0003', message: 'List Pos can only be selected once' },
  INDICATOR0004: { statusCode: 'INDICATOR0004', message: 'period invalid' },
  INDICATOR0005: { statusCode: 'INDICATOR0005', message: 'Indicator not found' },
  INDICATOR0006: { statusCode: 'INDICATOR0006', message: 'A commission is already in use' },

//IndicatorList
  INDICATORLIST0001: { statusCode: 'INDICATORLIST0001', message: 'employeeCode not found' },
  INDICATORLIST0002: { statusCode: 'INDICATORLIST0002', message: 'employeeCode is already in use' },
  INDICATORLIST0003: { statusCode: 'INDICATORLIST0003', message: 'managerCode not found' },
  INDICATORLIST0004: { statusCode: 'INDICATORLIST0004', message: 'employeeCode is duplicated with managerCode' },
  INDICATORLIST0005: { statusCode: 'INDICATORLIST0005', message: 'IndicatorList not found' },

  //IMPORT
  IMPORT_SUCCESS: { statusCode: 'SUCCESS', message: 'SUCCESS' },
  IMPORT_ERROR: { statusCode: 'ENTIRE_ERROR ', message: 'ENTIRE_ERROR' },
  IMPORT_ERROR_FORMAT: { statusCode: 'IMPORT_ERROR_FORMAT ', message: 'IMPORT_ERROR_FORMAT' },
  IMPORT_ERROR_SIZE: { statusCode: 'IMPORT_ERROR_SIZE ', message: 'IMPORT_ERROR_SIZE' },

  // commission
  COMME0001: { statusCode: 'COMME0001', message: 'Commission period not found' },
  COMME0002: { statusCode: 'COMME0002', message: 'Commission period id cannot be empty' },
  COMME0003: { statusCode: 'COMME0003', message: 'Cannot update status to ANNOUNCED' },
  COMME0004: { statusCode: 'COMME0004', message: 'Status is not valid' },
  COMME0005: { statusCode: 'COMME0005', message: 'Failed to calculate commission from DATA team' },
  COMME0006: { statusCode: 'COMME0006', message: 'Transaction exists in a commission period. Please provide another transaction to this period' },
  COMME0007: { statusCode: 'COMME0007', message: 'Either periodFrom or periodTo is not defined. Please define both fields' },
  COMME0008: { statusCode: 'COMME0008', message: 'Commission has been published. Failed to delete this period' },
  COMME0009: { statusCode: 'COMME0009', message: 'No employee found' },
  COMME0010: { statusCode: 'COMME0010', message: 'No sale policy found' },
  COMME0011: { statusCode: 'COMME0011', message: 'No project found' },
  COMME0012: { statusCode: 'COMME0012', message: 'This sale policy is not apply for current project' },
  COMME0013: { statusCode: 'COMME0013', message: 'No personal commission policy found' },
  COMME0014: { statusCode: 'COMME0014', message: 'No manager commission policy found' },
  COMME0015: { statusCode: 'COMME0015', message: 'Upload file does not have the same format as the template file' },
  COMME0016: { statusCode: 'COMME0016', message: 'Account does not have permission to view the published adjustment version' },
  COMME0017: { statusCode: 'COMME0017', message: 'Invalid commission period or POS code' },
  COMME0018: { statusCode: 'COMME0018', message: 'No expense transaction found in this period' },
  COMME0019: { statusCode: 'COMME0019', message: 'No version has been released yet' },
  COMME0020: { statusCode: 'COMME0020', message: 'Commission not found' },
  COMME0021: { statusCode: 'COMME0021', message: 'No valid employee found in indicator' },
  COMME0022: { statusCode: 'COMME0022', message: 'No commission for this user found in this period' },

  TRANSFORMPROPERTYUNIT0001: { statusCode: 'TRANSFORMPROPERTYUNIT0001', message: 'Pos Id does not exist in this program' },
  TRANSFORMPROPERTYUNIT0002: { statusCode: 'TRANSFORMPROPERTYUNIT0002', message: 'Product Id does not exist in this program' },

  //Fee
  FEE0001: { statusCode: 'FEE0001', message: 'Cannot delete transactions in progress approval' },
  FEE0002: { statusCode: 'FEE0002', message: 'Cannot delete this fee' },
  FEE0003: { statusCode: 'FEE0003', message: 'Cannot update with account does not create this fee' },
  FEE0004: { statusCode: 'FEE0004', message: 'Cannot update status when current status = WAITING' },
  FEE0005: { statusCode: 'FEE0005', message: 'Invalid file template' },

  //EVOUCHER
  EVOUCHER0001: { statusCode: 'EVOUCHER0001', message: 'Evoucher not found' },
  EVOUCHER0002: { statusCode: 'EVOUCHER0002', message: 'Evoucher is existed' },
  EVOUCHER0003: { statusCode: 'EVOUCHER0003', message: 'BusinessPartner over amount' },
  EVOUCHER0004: { statusCode: 'EVOUCHER0004', message: 'amountOfUsed over amount' },
  EVOUCHER0005: { statusCode: 'EVOUCHER0005', message: 'Can not update because applyStatus is not INIT' },
  EVOUCHER0006: { statusCode: 'EVOUCHER0006', message: 'maxAmountReduced is not empty' },
  EVOUCHER0007: { statusCode: 'EVOUCHER0007', message: 'applyStatus is not valid for applying' },
  EVOUCHER0008: { statusCode: 'EVOUCHER0008', message: 'The voucher is currently inactive' },
  EVOUCHER0009: { statusCode: 'EVOUCHER0009', message: 'promotionValue is not empty' },
  EVOUCHER0010: { statusCode: 'EVOUCHER0010', message: 'amountOfPosDistribution is not empty' },
  EVOUCHER0011: { statusCode: 'EVOUCHER0011', message: 'Please select a target object' },
  EVOUCHER0012: { statusCode: 'EVOUCHER0012', message: 'applyNvkd is not empty' },
  EVOUCHER0013: { statusCode: 'EVOUCHER0013', message: 'applyNvkd.amount is not empty' },
  EVOUCHER0014: { statusCode: 'EVOUCHER0014', message: 'employeeApply is not empty' },
  EVOUCHER0015: { statusCode: 'EVOUCHER0015', message: 'listSelfCreatedVoucher is not empty' },
  EVOUCHER0016: { statusCode: 'EVOUCHER0016', message: 'employeeApplyCommon is not empty' },
  EVOUCHER0017: { statusCode: 'EVOUCHER0017', message: 'providers do not exist' },

  //Period
  PERIOD0001: { statusCode: 'PERIOD0001', message: 'Period not found' },
  PERIOD0002: { statusCode: 'PERIOD0002', message: 'Cannot create when there is an active record.' },
  PERIOD0003: { statusCode: 'PERIOD0003', message: 'Cannot create when there is an active record for this org' },
  PERIOD0004: { statusCode: 'PERIOD0004', message: 'Cannot gen period' },
  PERIOD0005: { statusCode: 'PERIOD0005', message: 'Cannot delete when period is active' },
  PERIOD0006: { statusCode: 'PERIOD0006', message: 'Cannot create when there is an active record.' },
  PERIOD0007: { statusCode: 'PERIOD0007', message: 'Cannot create when there is an active record for this project' },
  PERIOD0008: { statusCode: 'PERIOD0008', message: 'Cannot gen period' },
  PERIOD0009: { statusCode: 'PERIOD0009', message: 'Cannot delete when period is active' },

  //Handover
  HANDOVER0001: { statusCode: 'HANDOVER0001', message: 'Handover is existed' },
  HANDOVER0002: { statusCode: 'HANDOVER0002', message: 'Please configure delivery time frame' },
  HANDOVER0003: { statusCode: 'HANDOVER0003', message: 'items name is not empty' },
  HANDOVER0004: { statusCode: 'HANDOVER0004', message: 'Handover is not exist' },
  HANDOVER0005: { statusCode: 'HANDOVER0005', message: 'expectedStartDate or expectedEndDate is invalid' },
  HANDOVER0006: { statusCode: 'HANDOVER0006', message: 'Hotline must contain only numbers' },
  HANDOVER0007: { statusCode: 'HANDOVER0007', message: 'project is not exist' },
  HANDOVER0008: { statusCode: 'HANDOVER0008', message: 'orgCharts name is not empty' },
  HANDOVER0009: { statusCode: 'HANDOVER0009', message: 'Delivery time frame invalid' },

  //Property/primary-transaction
  PROPRIMTX0001: { statusCode: 'PROPRIMTX0001', message: 'Employee not found' },
  PROPRIMTX0002: { statusCode: 'PROPRIMTX0002', message: 'Primary transaction ID does not exist' },
  PROPRIMTX0003: { statusCode: 'PROPRIMTX0003', message: 'Project or property unit for YCĐCO does not exist' },
  PROPRIMTX0004: { statusCode: 'PROPRIMTX0004', message: 'Can not create when stage 2 is end' },
  PROPRIMTX0005: { statusCode: 'PROPRIMTX0005', message: 'Can not create when YCĐCHO not valid' },
  PROPRIMTX0006: { statusCode: 'PROPRIMTX0006', message: 'Property unit status not valid or already have priorities' },
  PROPRIMTX0007: { statusCode: 'PROPRIMTX0007', message: 'Primary status of property unit status not valid' },
  PROPRIMTX0008: { statusCode: 'PROPRIMTX0008', message: 'saleUnitCode not valid' },
  PROPRIMTX0009: { statusCode: 'PROPRIMTX0009', message: 'Employee is not attached to any account, please check again!' },
  PROPRIMTX0010: { statusCode: 'PROPRIMTX0010', message: 'Primary transaction not found' },
  PROPRIMTX0011: { statusCode: 'PROPRIMTX0011', message: 'The project has not selected a rootPos' },
  PROPRIMTX0012: { statusCode: 'PROPRIMTX0012', message: 'ticket status is not valid' },
  PROPRIMTX0013: { statusCode: 'PROPRIMTX0013', message: 'status is not valid' },
  PROPRIMTX0014: { statusCode: 'PROPRIMTX0014', message: 'primary status is not valid' },

  //msx-transaction
  TRANSACTION0001: { statusCode: 'TRANSACTION0001', message: 'propertyTicket.id does not exist' },
  TRANSACTION0002: { statusCode: 'TRANSACTION0002', message: 'The amount exceeds the registered amount, please check!' },
  TRANSACTION0003: { statusCode: 'TRANSACTION0003', message: 'Transaction ID does not exist' },
  TRANSACTION0004: { statusCode: 'TRANSACTION0004', message: 'Transaction approved' },
  TRANSACTION0005: { statusCode: 'TRANSACTION0005', message: 'The bank bankNumber or bankName is incorrect.' },
  TRANSACTION0006: { statusCode: 'TRANSACTION0006', message: 'The online payment link already exists.' },
  TRANSACTION0007: { statusCode: 'TRANSACTION0007', message: 'The propertyTicket status is not valid' },
  TRANSACTION0008: { statusCode: 'TRANSACTION0008', message: 'The transaction code in valid' },
  TRANSACTION0009: { statusCode: 'TRANSACTION0009', message: 'The transaction propertyTicket in valid' },
  TRANSACTION0010: { statusCode: 'TRANSACTION0010', message: 'The transaction state is not valid' },
  TRANSACTION0011: { statusCode: 'TRANSACTION0011', message: 'The transaction status is invalid' },
  TRANSACTION0012: { statusCode: 'TRANSACTION0012', message: 'Unable to draft a proposal. This transaction has a draft proposal' },
  TRANSACTION0013: { statusCode: 'TRANSACTION0013', message: 'Failures in transaction submission preparation' },
  TRANSACTION0014: { statusCode: 'TRANSACTION0014', message: 'Cannot retrieve submission number' },
  TRANSACTION0015: { statusCode: 'TRANSACTION0015', message: 'File attachments are not allowed' },
  TRANSACTION0016: { statusCode: 'TRANSACTION0016', message: 'Customer information or address is invalid. Please verify and try again' },
  TRANSACTION0017: { statusCode: 'TRANSACTION0017', message: 'Invalid department or unit information' },
  TRANSACTION0018: { statusCode: 'TRANSACTION0018', message: 'Invalid commission information for the organization unit' },
  TRANSACTION0019: { statusCode: 'TRANSACTION0019', message: 'Project information is invalid. Please verify and try again.' },
  TRANSACTION0020: { statusCode: 'TRANSACTION0020', message: 'Unit with the selected code does not exist' },
  TRANSACTION0021: { statusCode: 'TRANSACTION0021', message: 'receiptNum already exists' },
  TRANSACTION0022: { statusCode: 'TRANSACTION0022', message: 'Creating online payment link failed' },

  //primary-contract
  PRIMARYCONTRACT0001: { statusCode: 'PRIMARYCONTRACT0001', message: 'handOver not found' },
  PRIMARYCONTRACT0002: { statusCode: 'PRIMARYCONTRACT0002', message: 'project not found' },
  PRIMARYCONTRACT0003: { statusCode: 'PRIMARYCONTRACT0003', message: 'handoverStatus invalid' },
  PRIMARYCONTRACT0004: { statusCode: 'PRIMARYCONTRACT0004', message: 'PrimaryContract not found' },
  PRIMARYCONTRACT0005: { statusCode: 'PRIMARYCONTRACT0005', message: 'PrimaryTrasaction not found' },
  PRIMARYCONTRACT0006: { statusCode: 'PRIMARYCONTRACT0006', message: 'PrimaryTrasaction already has contract' },
  PRIMARYCONTRACT0007: { statusCode: 'PRIMARYCONTRACT0007', message: 'policyDiscounts not found' },
  PRIMARYCONTRACT0008: { statusCode: 'PRIMARYCONTRACT0008', message: 'policyDiscounts.discount.typeRealEstate must be land or house' },
  PRIMARYCONTRACT0009: { statusCode: 'PRIMARYCONTRACT0009', message: 'policyDiscounts.discount.typeRealEstate must be default' },
  PRIMARYCONTRACT0010: { statusCode: 'PRIMARYCONTRACT0010', message: 'policyPayment not found' },
  PRIMARYCONTRACT0011: { statusCode: 'PRIMARYCONTRACT0011', message: 'Employee not found' },
  PRIMARYCONTRACT0012: { statusCode: 'PRIMARYCONTRACT0012', message: 'The contract liquidation already exists.' },
  PRIMARYCONTRACT0013: { statusCode: 'PRIMARYCONTRACT0013', message: 'The contract liquidation not found.' },
  PRIMARYCONTRACT0014: { statusCode: 'PRIMARYCONTRACT0014', message: 'HDC is used' },
  PRIMARYCONTRACT0015: { statusCode: 'PRIMARYCONTRACT0015', message: 'Invalid data type for liquidationStatus. Please try again.' },
  PRIMARYCONTRACT0016: { statusCode: 'PRIMARYCONTRACT0016', message: 'Current status is invalid. Please try again.' },
  PRIMARYCONTRACT0017: { statusCode: 'PRIMARYCONTRACT0017', message: 'Invalid data type for contractStatus. Please try again.' },
  PRIMARYCONTRACT0018: { statusCode: 'PRIMARYCONTRACT0018', message: 'The contract ID is incorrect.' },
  PRIMARYCONTRACT0019: { statusCode: 'PRIMARYCONTRACT0019', message: 'PrimaryTrasaction status invalid' },
  PRIMARYCONTRACT0020: { statusCode: 'PRIMARYCONTRACT0020', message: 'The deposit slip already exists. Please check again.' },
  PRIMARYCONTRACT0021: { statusCode: 'PRIMARYCONTRACT0021', message: 'Invalid data type for proposal. Please try again.' },
  PRIMARYCONTRACT0022: { statusCode: 'PRIMARYCONTRACT0022', message: 'Current proposal status is invalid. Please try again.' },
  PRIMARYCONTRACT0023: { statusCode: 'PRIMARYCONTRACT0023', message: 'InterestCalculation not found' },
  PRIMARYCONTRACT0024: { statusCode: 'PRIMARYCONTRACT0024', message: 'Current interestCalculation status is invalid' },
  PRIMARYCONTRACT0025: { statusCode: 'PRIMARYCONTRACT0025', message: 'cerHandoverStatus invalid' },
  PRIMARYCONTRACT0026: { statusCode: 'PRIMARYCONTRACT0026', message: 'ownershipCertificate not found' },
  PRIMARYCONTRACT0027: { statusCode: 'PRIMARYCONTRACT0027', message: 'The contract has not created a proposal yet.' },
  PRIMARYCONTRACT0028: { statusCode: 'PRIMARYCONTRACT0028', message: 'Only assign debt collector can interact with this contract' },
  PRIMARYCONTRACT0029: { statusCode: 'PRIMARYCONTRACT0029', message: 'There already had version with confirmed status. Cannot update another to confirmed' },
  PRIMARYCONTRACT0030: { statusCode: 'PRIMARYCONTRACT0030', message: 'This version has not been confirmed. Cannot cancel this version.' },
  PRIMARYCONTRACT0031: { statusCode: "PRIMARYCONTRACT0031", message: "bankInfo is missing" },
  PRIMARYCONTRACT0032: { statusCode: "PRIMARYCONTRACT0032", message: "loanAmount is missing" },
  PRIMARYCONTRACT0033: { statusCode: "PRIMARYCONTRACT0033", message: "loanTermYear is missing" },
  PRIMARYCONTRACT0034: { statusCode: "PRIMARYCONTRACT0034", message: "loanTermYear must be at most 4 characters (e.g., 12.3)" },
  PRIMARYCONTRACT0035: { statusCode: "PRIMARYCONTRACT0035", message: "loanAmount must be at most 15 characters" },
  PRIMARYCONTRACT0036: { statusCode: "PRIMARYCONTRACT0036", message: "loanTermYear must match format with exactly one decimal place (e.g., 1.5, 12.3)" },
  PRIMARYCONTRACT0037: { statusCode: "PRIMARYCONTRACT0037", message: "No template to print" },

  //OWNERSHIPCETIFICATE
  OWNERSHIPCETIFICATE0001: { statusCode: 'OWNERSHIPCETIFICATE0001', message: 'Hotline must contain only numbers' },
  OWNERSHIPCETIFICATE0002: { statusCode: 'OWNERSHIPCETIFICATE0002', message: 'Please configure delivery time frame' },
  OWNERSHIPCETIFICATE0003: { statusCode: 'OWNERSHIPCETIFICATE0003', message: 'Items name is not empty' },
  OWNERSHIPCETIFICATE0004: { statusCode: 'OWNERSHIPCETIFICATE0004', message: 'Project is not exist' },
  OWNERSHIPCETIFICATE0005: { statusCode: 'OWNERSHIPCETIFICATE0005', message: 'Ownership Certificate is not exist' },
  OWNERSHIPCETIFICATE0006: { statusCode: 'OWNERSHIPCETIFICATE0006', message: 'Ownership Certificate exist' },
  OWNERSHIPCETIFICATE0007: { statusCode: 'OWNERSHIPCETIFICATE0007', message: 'Delivery time frame invalid' },

  //Training
  TRAINING0001: { statusCode: 'TRAINING0001', message: 'Can not update this training with current account' },
  TRAINING0002: { statusCode: 'TRAINING0002', message: 'DVBH existed' },
  TRAINING0003: { statusCode: 'TRAINING0003', message: 'urlEvent existed' },
  TRAINING0004: { statusCode: 'TRAINING0004', message: 'sheet invalid (may wrong template sheet name)' },
  TRAINING0005: { statusCode: 'TRAINING0005', message: 'email existed' },
  TRAINING0006: { statusCode: 'TRAINING0006', message: 'email is empty' },
  TRAINING0007: { statusCode: 'TRAINING0007', message: 'Training not exist' },
  TRAINING0008: { statusCode: 'TRAINING0008', message: 'Training not exist or not allowRequestDemand' },
  TRAINING0009: { statusCode: 'TRAINING0009', message: 'user not found' },
  TRAINING0010: { statusCode: 'TRAINING0010', message: 'There are no more users eligible to receive the prize' },
  TRAINING0011: { statusCode: 'TRAINING0011', message: 'Wrong check-in code. Please try again.' },
  TRAINING0012: { statusCode: 'TRAINING0012', message: 'Invalid QR code or account does not have permission for this event' },
  TRAINING0013: { statusCode: 'TRAINING0013', message: 'Check-in code has been used. Please try another code' },
  TRAINING0014: { statusCode: 'TRAINING0014', message: 'Another prize draw is in progress. Please try again' },
  TRAINING0015: { statusCode: 'TRAINING0015', message: 'The prize has been cancelled' },
  TRAINING0016: { statusCode: 'TRAINING0016', message: 'The selected prize has been drawn out' },
  TRAINING0017: { statusCode: 'TRAINING0017', message: 'List prize not found' },
  TRAINING0018: { statusCode: 'TRAINING0018', message: 'The selected prize not found' },
  TRAINING0019: { statusCode: 'TRAINING0019', message: 'Invalid file' },
  TRAINING0020: { statusCode: 'TRAINING0020', message: 'Event is off' },


  //msx-primary-contract-proposal
  PCPROPOSAL001: { statusCode: 'PCPROPOSAL001', message: 'The proposal was not found' },
  PCPROPOSAL002: { statusCode: 'PCPROPOSAL002', message: 'The proposal already exists. Please check again.' },

  //DEBTCOMMISSIONPOLICY
  DEBTCOMMISSIONPOLICY0001: { statusCode: 'DEBTCOMMISSIONPOLICY0001', message: 'penalty does not empty' },
  DEBTCOMMISSIONPOLICY0002: { statusCode: 'DEBTCOMMISSIONPOLICY0002', message: 'penalty.amount is over length' },
  DEBTCOMMISSIONPOLICY0003: { statusCode: 'DEBTCOMMISSIONPOLICY0003', message: 'bottomPrice does not empty' },
  DEBTCOMMISSIONPOLICY0004: { statusCode: 'DEBTCOMMISSIONPOLICY0004', message: 'bottomPrice is over length' },
  DEBTCOMMISSIONPOLICY0005: { statusCode: 'DEBTCOMMISSIONPOLICY0005', message: 'topPrice does not empty' },
  DEBTCOMMISSIONPOLICY0006: { statusCode: 'DEBTCOMMISSIONPOLICY0006', message: 'topPrice is over length' },
  DEBTCOMMISSIONPOLICY0007: { statusCode: 'DEBTCOMMISSIONPOLICY0007', message: 'topPrice or bottomPrice is invalid' },
  DEBTCOMMISSIONPOLICY0008: { statusCode: 'DEBTCOMMISSIONPOLICY0008', message: 'recordedCommissionUnit is over length' },
  DEBTCOMMISSIONPOLICY0009: { statusCode: 'DEBTCOMMISSIONPOLICY0009', message: 'debtCommissionPolicy is not existed' },

  //HANDOVERSCHEDULE
  HANDOVERSCHEDULE0001: { statusCode: 'HANDOVERSCHEDULE0001', message: 'handoverApartment or handoverApartment.id do not empty' },
  HANDOVERSCHEDULE0002: { statusCode: 'HANDOVERSCHEDULE0002', message: 'HandoverSchedule is existed' },
  HANDOVERSCHEDULE0003: { statusCode: 'HANDOVERSCHEDULE0003', message: 'HandoverSchedule is not existed' },

  //HANDOVERCONFIG
  HANDOVERCONFIG0001: { statusCode: 'HANDOVERCONFIG0001', message: 'Contract not found' },
  HANDOVERCONFIG0002: { statusCode: 'HANDOVERCONFIG0002', message: 'Handover configuration not found' },
  HANDOVERCONFIG0003: { statusCode: 'HANDOVERCONFIG0003', message: 'Payment percentage does not meet the requirement' },
  HANDOVERCONFIG0004: { statusCode: 'HANDOVERCONFIG0004', message: 'Support employee information is missing' },
  HANDOVERCONFIG0005: { statusCode: 'HANDOVERCONFIG0005', message: 'Support employee not found' },
  HANDOVERCONFIG0006: { statusCode: 'HANDOVERCONFIG0006', message: 'Support employee position is not listed in handover configuration' },
  HANDOVERCONFIG0007: { statusCode: 'HANDOVERCONFIG0007', message: 'Missing handover time frame' },
  HANDOVERCONFIG0008: { statusCode: 'HANDOVERCONFIG0008', message: 'The selected time frame is not in the handover configuration' },
  HANDOVERCONFIG0009: { statusCode: 'HANDOVERCONFIG0009', message: 'The number of allowed handovers for this time frame has been reached' },
  HANDOVERCONFIG0010: { statusCode: 'HANDOVERCONFIG0010', message: 'Support employee has a conflicting schedule' },

  //msx-primary-contract > deposit-contract
  DEPCT0001: { statusCode: 'DEPCT0001', message: 'The deposit contract not found' },
  DEPCT0002: { statusCode: 'DEPCT0002', message: 'The deposit contract is existed' },
  DEPCT0003: { statusCode: 'DEPCT0003', message: 'Installment not found' },
  DEPCT0004: { statusCode: 'DEPCT0004', message: 'Orgchart partner not found' },
  DEPCT0005: { statusCode: 'DEPCT0005', message: 'Duplicate name in new installments.' },
  DEPCT0006: { statusCode: 'DEPCT0006', message: 'Name already exists in old installments.' },
  DEPCT0007: { statusCode: 'DEPCT0007', message: `Please enter a total payment amount less than the deposit amount.` },
  DEPCT0008: { statusCode: 'DEPCT0008', message: `Cannot delete installment because the transaction was successful.` },
  DEPCT0009: { statusCode: 'DEPCT0009', message: 'Failed to create deposit contract.' },

  // debt penalty
  DEBTPEN0001: { statusCode: 'DEBTPEN0001', message: 'An interest slip has been issued or an interest slip has not been fully transfered' },


  // Attribute

  ATTRIBUTE0001: { statusCode: 'ATTRIBUTE0001', message: 'Attribute not found' },
  ATTRIBUTE0002: { statusCode: 'ATTRIBUTE0002', message: 'Attribute name is duplicated' },
  ATTRIBUTE0003: { statusCode: 'ATTRIBUTE0003', message: 'Update attribute successfully' },
  ATTRIBUTE0004: { statusCode: 'ATTRIBUTE0004', message: 'Create attribute successfully' },


  // MARKETING
  MARKETING001: { statusCode: 'MARKETING001', message: 'Marketing not found' },
  MARKETING002: { statusCode: 'MARKETING002', message: 'Proposal status is not allowed to be update.' },
  MARKETING003: { statusCode: 'MARKETING003', message: 'attachments over length' },
  // cost item
  COSTITEM0001: { statusCode: '200', message: 'Success' },

  //pricing-table
  PRICINGTABLE001: { statusCode: 'PRICINGTABLE001', message: 'The data is currently being processed. Deletion is not allowed.' },
  PRICINGTABLE002: { statusCode: 'PRICINGTABLE002', message: 'The current version is awaiting results.' },
  PRICINGTABLE003: { statusCode: 'PRICINGTABLE003', message: 'There is a version of the sales program currently pending results.' },
  PRICINGTABLE004: { statusCode: 'PRICINGTABLE004', message: 'The system is processing' },

  //PRICE_LIST
  PRICELISTD0001: { statusCode: 'PRICELISTD0001', message: 'Status is not allowed to be modified' },
  PRICELISTD0002: { statusCode: 'PRICELISTD0002', message: 'Unable to draft a proposal. This policy price has a draft proposal' },
  PRICELISTD0003: { statusCode: 'PRICELISTD0003', message: 'Failures in policy price submission preparation' },
  PRICELISTD0004: { statusCode: 'PRICELISTD0004', message: 'Cannot retrieve submission number' },
  PRICELISTD0005: { statusCode: 'PRICELISTD0005', message: 'Dowload file price list failed' },

  // EVENT-PROJECT
  EVENTPROJECT0001: { statusCode: 'EVENTPROJECT0001', message: 'Event project not found' },
  EVENTPROJECT0002: { statusCode: 'EVENTPROJECT0002', message: 'Event project not found' },
};
