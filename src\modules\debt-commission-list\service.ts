import { Injectable } from '@nestjs/common';
import { EmployeeService } from '../employee.query/service';
import { HistoryImportQueryRepository } from '../import-history.queryside/repository/query.repository';
import { Decimal } from 'decimal.js';
import { CommissionDataDto, ContractDto, EmployeeDataDto, PropertyUnitDataDto } from './dto/employee-data.dto';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { AwesomeLogger, BaseService, ErrorService, CmdPatternConst as cmdv2 } from '../../../shared-modules';
import * as _ from 'lodash';
import { PropertyClient } from '../mgs-sender/property.client';
import { StaticAssetService } from '../config/static-asset.service';
import { existsSync, unlinkSync } from "fs";
import * as Bluebird from 'bluebird';
import * as path from 'path';
const fs = Bluebird.promisifyAll(require('fs'));
const ExcelJS = require('exceljs');
import * as axios from 'axios';
import * as FormData from 'form-data';
import { validateExcelHeaders, validateBody } from '../shared/utils/import';
import { DebtCalculationType, DebtCommissionStatus, DebtType, HistoryType } from '../shared/enum/commission.enum';
import { TempDebtCommissionListRepository } from './repository/temp-debt-commission-list.repository';
import { DebtCommissionListRepository } from './repository/debt-commission-list.repository';
import { DebtCommissionRepository } from '../debt-commission/repository/repository';
import { CalculateBaseDebtCommissionDto, DebtCommissionListDto } from './dto/commission-list.dto';
import { PrimaryContractRepository } from '../primary-contract/repository/primary-contract-query.repository';
import { FileGenerationService } from './file-generation.service';
import { IDebtCommission } from '../shared/services/debt-commission/interfaces/debt-commission.interface';
import { IDebtCommissionRate } from '../shared/services/debt-commission-policy/interfaces/commission-policy.interface';
import { IDebtPolicy } from './interfaces/document.interface';
import { DebtCommissionPolicyService } from '../debt-commission-policy/service';
import { DebtCommissionPolicyQueryRepository } from '../debt-commission-policy/repository/repository';

const uuid = require('uuid');
const clc = require('cli-color');

@Injectable()
export class DebtCommissionListService extends BaseService {
  private readonly context = DebtCommissionListService.name;
  private readonly logger = new AwesomeLogger(DebtCommissionListService.name);

  constructor(
    private readonly tempCommListRepo: TempDebtCommissionListRepository,
    private readonly commListRepo: DebtCommissionListRepository,
    private readonly commRepo: DebtCommissionRepository,
    private readonly commissionPolicyRepo: DebtCommissionPolicyQueryRepository,
    private readonly historyRepository: HistoryImportQueryRepository,
    private readonly debtCommPolicyService: DebtCommissionPolicyService,
    private readonly orgchartClient: OrgchartClient,
    private readonly propertyClient: PropertyClient,
    private readonly primaryContractRepo: PrimaryContractRepository,
    private readonly fileGenerationService: FileGenerationService,
    private readonly staticAssetService: StaticAssetService,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }

  async calculateBaseCommission(user, dto: CalculateBaseDebtCommissionDto) {
    this.logger.info('Calculate base debt commission');

    // sales - get all tx by period
    const tickets = await this.primaryContractRepo.getAllInterestTickets(user, {
      periodFrom: dto.periodFrom,
      periodTo: dto.periodTo,
      projectId: dto.projectId
    });

    // calculate debt commission tx
    const calculateResult = await this.calculateDebtCommission(user, dto, tickets);
    console.log('calculateResult', calculateResult);

    return this.getResponse(0, calculateResult);
  }

  async createTempCommissionList(user, model, action?: String) {
    try {
      this.logger.info('create temporarily commission list api');
      const { code } = model;

      this.logger.debug('code', model.code);

      // const commTx = await this.tempCommListRepo.findOne({ code });
      // if (!_.isEmpty(commTx)) {
      //   return this.getResponse('COMME0006');
      // }

      model.id = uuid.v4();
      model.createdBy = user.id;
      model.code = code;
      model.commission['type'] = HistoryType.DEBT_COMMISSION;

      // save to db
      await this.tempCommListRepo.create(model);
    } catch (error) {
      this.logger.error('error create comm list', error);
    }
  }

  async createCommissionList(model) {
    try {
      this.logger.info('create commission list api');
      const plainModel = model.toObject();
      delete plainModel.__v;
      delete plainModel._id;

      // save to db
      await this.commListRepo.create(plainModel);
    } catch (error) {
      this.logger.error('error create comm list', error);
    }
  }

  async updateCommissionList(user, dto: any, actionName: string) {
    this.logger.info('update service');
    const { id } = dto;

    if (dto.id === '') {
      return this.getResponse('COMME0002');
    }

    const commission = await this.commRepo.findOne({ id });
    if (!commission) {
      this.logger.error('no commission period found');
      return this.getResponse('COMME0001');
    }

    try {
      // Save all data from temp collection to main collection
      const tempModelList = await this.tempCommListRepo.find({ 'commission.id': id });
      if (tempModelList?.length > 0) {
        // calculate button confirm
        const existTxs = await this.commListRepo.find({ 'commission.id': id });
        if (existTxs) {
          // delete all old transaction in commission list
          await this.commListRepo.deleteManyByCommissionId(id);
        }

        // create com list base on temp list
        await Bluebird.map(tempModelList, async model => {
          await this.createCommissionList(model);
        });

        // export to commission list excel file then upload to s3
        const now = new Date();
        const fileName = 'BangKeHoaHongCongNoGoc_' + now.getTime();
        const uploadResult = await this.exportTxs(user, id, fileName, true);
        // console.log('uploadResult', uploadResult);
        if (uploadResult?.statusCode != '0') {
          console.error('uploadResult error');
          return this.getResponse('COMME0001');
        }

        await this.addVersion(user, id, fileName, uploadResult.data, true);
      }

      const commissionToUpdate = await this.commRepo.findOne({ id });

      // get commission policy
      if (dto.commissionPolicyCode) {
        const commissionPolicy = await this.commissionPolicyRepo.findOne({ code: dto.commissionPolicyCode });
        if (!commissionPolicy) {
          return this.getResponse('COMME0013');
        }
        dto.commissionPolicy = commissionPolicy;
      }

      commissionToUpdate.commissionPolicy = dto.commissionPolicy;
      if (dto.periodFrom && dto.periodTo) {
        const from = new Date(dto.periodFrom);
        const to = new Date(dto.periodTo);
        const formattedFromDate = `${from.getDate().toString().padStart(2, '0')}/${(from.getMonth() + 1).toString().padStart(2, '0')}/${from.getFullYear()}`;
        const formattedEndDate = `${to.getDate().toString().padStart(2, '0')}/${(to.getMonth() + 1).toString().padStart(2, '0')}/${to.getFullYear()}`;
        commissionToUpdate.period = formattedFromDate + " → " + formattedEndDate;
        commissionToUpdate.name = dto.periodName;
        commissionToUpdate.periodFrom = dto.periodFrom;
        commissionToUpdate.periodTo = dto.periodTo;
      }
      commissionToUpdate.modifiedBy = user.id;
      commissionToUpdate.modifiedDate = new Date();
      await this.commRepo.update(commissionToUpdate);

      // Delete all transactions in temp collection
      await this.tempCommListRepo.deleteManyByCommissionId(id);

      return this.getResponse(0, { id: dto.id });
    } catch (e) {
      this.logger.error('error', e);
      return this.getResponse('COMME0001');
    }
  }

  // async deleteCommissionList(user, id: string, actionName: string) {
  //   this.logger.info(this.context, clc.redBright('delete service'));
  //   if (id === '') {
  //     return this.getResponse('COMME0002');
  //   }
  //   const commissionList = new CreateCommissionListDto();
  //   commissionList.id = id;
  //   commissionList.softDelete = true;
  //   this.commandId = uuid.v4();
  //   await this.executeCommand(CommonConst.COMMANDS.DELETE, actionName, this.commandId, commissionList);
  //   return this.getResponse(0);
  // }

  async publishCommissionList(user, id: string, actionName: string) {
    this.logger.info(this.context, clc.blue('publish service'));
    if (id === '') {
      return this.getResponse('COMME0002');
    }
    const commission = await this.commListRepo.findOne({ id });
    if (!commission) {
      return this.getResponse('COMME0001');
    }
    commission.isPublish = true;
    commission.modifiedBy = user.id;
    await this.commListRepo.update(commission);
    return this.getResponse(0);
  }

  async unpublishCommissionList(user, id: string, actionName: string) {
    this.logger.info(this.context, clc.blue('unpublish service'));
    if (id === '') {
      return this.getResponse('COMME0002');
    }
    const commission = await this.commListRepo.findOne({ id });
    if (!commission) {
      return this.getResponse('COMME0001');
    }
    commission.isPublish = false;
    commission.modifiedBy = user.id;
    await this.commListRepo.update(commission);
    return this.getResponse(0);
  }

  // import phi dieu chinh
  async importCommissionList(user, id, files: any, actionName: string) {
    this.logger.info('import adjustment version');
    let history = [];

    // history.push({
    //   line: `${index + 1}`,
    //   error: `Không tìm thấy nhân viên: ${draftItem.employeeCode}`
    // });

    let pathTemp = '';

    // get all tx
    const unwindComList = await this.commListRepo.listDebtCommissionByUser(user, { 'commission.id': id });
    const listComm = await this.commListRepo.listAll({ 'commission.id': id });
    const mapTx = {};
    for (const tx of listComm) {
      mapTx[tx.code] = tx;
    }

    try {
      // Create a new workbook
      const workbook = new ExcelJS.Workbook();

      // Read the Excel file
      await workbook.xlsx.load(files[0].buffer);

      // Get the worksheet
      const worksheet = workbook.getWorksheet(1);

      if (!worksheet) {
        throw new Error('Worksheet not found');
      }

      // Print header row - for debugging purposes
      // const headers = printExcelHeaders(worksheet);
      // console.log('Found headers:', headers);

      // Validate file
      const validateResult = await this.validateExcel(unwindComList, worksheet);
      history = validateResult.history;

      if (history.length > 0) {
        const historyModel = {
          fileName: files[0].originalname,
          processBy: user.id,
          fail: history.length,
          success: 0,
          description: history,
          createdDate: new Date(),
          type: HistoryType.DEBT_COMMISSION,
          failedFileUrl: validateResult.failedFile ? validateResult.failedFile.Location : '',
          failedFileName: validateResult.failedFile ? validateResult.failedFile.name : '',
          failedFilePath: validateResult.failedFile ? validateResult.failedFile.Key : '',
          status: 'ENTIRE_ERROR'
        };
        await this.historyRepository.create(historyModel);

        return this.getResponse('COMME0015');
      }

      // Calculate final amount
      const columnCalculations = [
        { result: 'R', operand1: 'K', operand2: 'Q' }, // R = K + Q
        { result: 'T', operand1: 'P', operand2: 'S' }, // T = P + S
      ];

      // Iterate through each row
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 7) { // Skip header if needed

          columnCalculations.forEach(calc => {
            const cell1 = row.getCell(calc.operand1);

            const hasValue = cell1.value !== null && cell1.value !== undefined && cell1.value !== '';
            if (hasValue) {
              const cell2 = row.getCell(calc.operand2);
              const resultCell = row.getCell(calc.result);

              // Get numeric values, defaulting to 0 if undefined or non-numeric
              const value1 = (cell1.value !== null && cell1.value !== undefined && !isNaN(cell1.value)) ? Number(cell1.value) : 0;
              const value2 = (cell2.value !== null && cell2.value !== undefined && !isNaN(cell2.value)) ? Number(cell2.value) : 0;

              // Calculate sum
              const sum = value1 + value2;

              // Set the result
              resultCell.value = sum;

              // Copy number format from first operand cell if it exists
              if (cell1.numFmt) {
                resultCell.numFmt = cell1.numFmt;
              }
            }
          });
        }
      });

      // Generate unique filename with timestamp
      const timestamp = new Date().getTime();
      const originalFileName = 'BangKeHoaHongDieuChinh';
      const fileNameWithoutExt = originalFileName.replace('.xlsx', '');
      const newFileName = `${fileNameWithoutExt}_${timestamp}.xlsx`;

      // Create path to generated-files folder
      const nodebuffer = await workbook.xlsx.writeBuffer();
      pathTemp = path.resolve(this.staticAssetService.getUploadFolderPath(), newFileName);
      await fs.writeFileAsync(pathTemp, nodebuffer);

      // upload to s3
      const formData = new FormData();
      formData.append('file', nodebuffer, { filename: `${newFileName}.xlsx` });
      formData.append('path', 'commission');
      const response = await axios.default.post(
        process.env.MTD_UPLOAD,
        formData,
        { headers: { ...formData.getHeaders() } });

      if (response.data.statusCode != '0') {
        return this.getResponse('COMME0001');
      }

      // add version to commission
      const version = await this.addVersion(user, id, newFileName, response.data.data);

      // add adjustmentData to commission list
      let currentAdjustmentData = null;
      let codeValue = '';
      let lastIndex = 0;
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 7) { // Skip header rows, start from row 8

          const indexValue = row.getCell('A').value;
          codeValue = row.getCell('B').value;

          currentAdjustmentData = {
            id: indexValue ? Number(indexValue) : Number(lastIndex),
            version,
            debtRevenue: {
              adjust: row.getCell('Q').value || 0,   // Doanh thu thu hồi điều chỉnh
              final: row.getCell('R').value || 0     // Doanh thu thu hồi sau điều chỉnh
            },
            debtCommissionRevenue: {
              adjust: row.getCell('S').value || 0,   // Hoa hồng công nợ điều chỉnh
              final: row.getCell('T').value || 0     // Hoa hồng công nợ sau điều chỉnh
            }
          };

          mapTx[codeValue].adjustmentData = mapTx[codeValue].adjustmentData?.length > 0 ?
            [...mapTx[codeValue].adjustmentData, currentAdjustmentData] :
            [currentAdjustmentData];

          lastIndex = indexValue;
        }
      });

      console.log('mapTx', mapTx);

      await this.commListRepo.bulkUpdateAdjustmentData(listComm, mapTx);

      if (existsSync(pathTemp)) {
        unlinkSync(pathTemp);
      }

      const historyModel = {
        fileName: files[0].originalname,
        processBy: user.id,
        fail: history.length,
        success: history?.length > 0 ? 0 : worksheet.lastRow.number,
        description: history,
        createdDate: new Date(),
        type: HistoryType.DEBT_COMMISSION,
        filefailedFileUrl: '',
        failedFileName: '',
        failedFilePath: '',
        status: 'SUCCESS'
      };
      await this.historyRepository.create(historyModel);

      // Return your existing response
      return this.getResponse(0, { id });

    } catch (error) {
      console.error('Error in importDebtCommissionList:', error);

      if (existsSync(pathTemp)) {
        unlinkSync(pathTemp);
      }

      return this.getResponse('COMME0001');
    }
  }

  async addVersion(user, id, fileName, uploadData, isBase = false) {
    const commission = await this.commRepo.findDebtCommissionById(id);

    // console.log('commission', commission);

    let nextVersion = 1;
    let adjustmentVersions = commission.adjustmentVersions || [];
    if (!isBase && commission.adjustmentVersions && commission.adjustmentVersions.length > 0) {
      const lastVersion = commission.adjustmentVersions[commission.adjustmentVersions.length - 1].version;
      const lastVersionNum = parseInt(lastVersion.replace(/\D/g, ''));
      nextVersion = lastVersionNum + 1;
    } else {
      adjustmentVersions = [];
    }

    adjustmentVersions.push({
      id: uuid.v4(),
      version: `V.${nextVersion}`,
      fileName,
      fileUrl: uploadData.Location,
      filePath: uploadData.Key,
      status: DebtCommissionStatus.CREATED,
      uploadDate: new Date(),
      uploadBy: user.id
    });
    commission.adjustmentVersions = adjustmentVersions;
    commission.modifiedBy = user.id;
    commission.modifiedDate = new Date();
    await this.commRepo.update(commission);

    return `V.${nextVersion}`;
  }

  private rounding(number: number): number {
    return Math.round(number);
  }

  async calculateDebtCommission(user: any, dto: CalculateBaseDebtCommissionDto, tickets: any) {
    this.logger.info('calculate debt commission', tickets);
    const { commissionCode } = dto;

    // const history = [];

    if (!dto.commissionPolicyCode) {
      return this.getResponse('COMME0013');
    }

    // Lấy thông tin kỳ hạn
    const commission = await this.commRepo.findOne({ code: commissionCode });
    if (!commission) {
      return this.getResponse('COMME0001');
    }

    // Lấy thông tin dự án từ project code
    const setProjectCode = new Set(tickets.map((row) => row.projectCode));
    // console.log('setProjectCode', setProjectCode);
    const listProject = await this.propertyClient.sendDataPromise({ codes: Array.from(setProjectCode) as any }, cmdv2.PROJECT.GET_PROJECT_BY_CODES);
    // console.log('listProject', listProject);
    const mapProject = {};
    for (const project of listProject) {
      mapProject[project.code] = (({ id, code, name }) => ({ id, code, name }))(project);
    }
    // console.log('mapProject', mapProject);

    // Lấy thông tin kpi từ commissionPolicyCode
    const commissionPolicy = await this.debtCommPolicyService.findByCode(dto.commissionPolicyCode);
    if (!commissionPolicy) {
      return this.getResponse('COMME0013');
    }

    // Lấy thông tin pos theo posCode
    const listOrgCodes = tickets.map(row => row.orgCode);
    const listPos = await this.orgchartClient.sendDataPromise({ code: { $in: listOrgCodes } }, cmdv2.ORGCHART.LISTENER.GET_POS_BY_QUERY);
    const mapPos = {};
    for (const pos of listPos) {
      mapPos[pos.code] = (({ id, code, name }) => ({ id, code, name }))(pos);
    }
    console.log('mapPos', mapPos);
    // console.log('tickets.entries()', tickets.entries());

    // Loop lần 1: Gom nhóm giao dịch theo nhân viên, thực hiện tính toán doanh thu cho từng giao dịch
    const projectDebtMap: any = {};
    const badDebtMap: any = {};
    for (const [index, ticket] of tickets.entries()) {

      // console.log('ticket', ticket);

      const project = mapProject[ticket.projectCode];
      if (!project) {
        return this.getResponse('COMME0011');
      }

      let listData = [];
      let debtRate = 0;
      let debtRevenue = 0;
      let debtPolicy: any;
      if (ticket.debtType == DebtType.PROJECT_DEBT) {
        // Tỷ lệ thù lao nợ dự án
        debtRate = new Decimal(commissionPolicy?.projectDebt?.rate
          ? commissionPolicy.projectDebt.rate
          : 0).dividedBy(100).toNumber();

        debtPolicy = commissionPolicy.projectDebt;

        // Gom dữ liệu theo nhân viên
        listData = projectDebtMap[ticket.debtCollector.code] || [];
      } else {
        // Tỷ lệ thù lao nợ xấu
        debtRate = new Decimal(commissionPolicy?.badDebt?.rate
          ? commissionPolicy.badDebt.rate
          : 0).dividedBy(100).toNumber();

        debtPolicy = commissionPolicy.badDebt;

        // Gom dữ liệu theo nhân viên
        listData = badDebtMap[ticket.debtCollector.code] || [];
      }

      // lấy tổng doanh thu thu hồi trong kỳ
      switch (debtPolicy.type) {
        case DebtCalculationType.ALL:
          debtRevenue = +ticket.principalAmount + +ticket.interestAmount + +ticket.latePaymentFee || 0;
          break;
        case DebtCalculationType.BASE:
          debtRevenue = +ticket.principalAmount || 0;
          break;
        case DebtCalculationType.INTEREST:
          debtRevenue = +ticket.interestAmount + +ticket.latePaymentFee || 0;
          break;
        default:
          break;
      }

      // VAT
      const vatRate = (debtPolicy && debtPolicy.isVAT) ? 1.1 : 1;

      // gom all giao dịch của all nhân viên
      listData.push(new EmployeeDataDto({
        index: index + 1,
        debtType: ticket.debtType, // PROJECT_DEBT | BAD_DEBT
        code: ticket.contractCode,
        name: ticket.contractName,
        installmentName: ticket.installmentName,
        contract: new ContractDto({
          id: ticket.id,
          type: ticket.contractType,
          code: ticket.contractCode,
          name: ticket.contractName,
        }),
        project: mapProject[ticket.projectCode],
        propertyUnit: new PropertyUnitDataDto({
          id: ticket.propertyId,
          type: ticket.propertyType || '',
          view1: ticket.propertyView1,
        }),
        pos: {
          id: mapPos[ticket.orgCode].id,
          code: ticket.orgCode,
          name: mapPos[ticket.orgCode].name,
        },
        customer: {
          code: ticket.customerCode,
          name: ticket.customerName
        },
        debtCollector: {
          id: ticket.debtCollector.id,
          accountId: ticket.debtCollector.accountId,
          code: ticket.debtCollector.code,
          name: ticket.debtCollector.name,
        },
        commissionPolicy: {
          code: commissionPolicy.code,
          ...debtPolicy
        },
        debtage: {
          name: ticket.debtage.name
        },

        // calculated
        vatRate,
        debtRate,
        debtRevenue,

        // tính toán sau
        commissions: [],
      }));

      if (ticket.debtType == DebtType.PROJECT_DEBT) {
        projectDebtMap[ticket.debtCollector.code] = listData;
      } else {
        badDebtMap[ticket.debtCollector.code] = listData;
      }
    }

    // Tính hoa hồng
    const projectDebtModelList: DebtCommissionListDto[] = await this.calculateCommission(commission, projectDebtMap, dto);
    const badDebtModelList: DebtCommissionListDto[] = await this.calculateCommission(commission, badDebtMap, dto);

    // Xóa dữ liệu cũ trong temp
    await this.tempCommListRepo.deleteManyByCommissionId(commission.id);

    // Lưu dữ liệu vao temp
    let tempCommissionList: any;
    try {
      await Bluebird.map([...projectDebtModelList, ...badDebtModelList], async model => {
        await this.createTempCommissionList(user, model, 'createTempCommissionList');
      });

      tempCommissionList = await this.tempCommListRepo.listAll({ 'commission.id': commission.id });
    } catch (e) {
      console.log('error', e);
    }

    return { id: commission.id, transactions: tempCommissionList };
  }

  async calculateCommission(commission: IDebtCommission, employeeTransactionMap: any, dto: CalculateBaseDebtCommissionDto): Promise<DebtCommissionListDto[]> {
    const modelList: DebtCommissionListDto[] = [];

    // Loop lần 2: Với mỗi nhân viên, thực hiện tính hoa hồng
    for (const employeeCode of Object.keys(employeeTransactionMap)) {
      const listEmployeeData: EmployeeDataDto[] = employeeTransactionMap[employeeCode];

      // Lấy chính sách hoa hồng NVKD của mỗi nhân viên
      const commissionPolicy = listEmployeeData[0].commissionPolicy;

      // Tính tổng doanh thu của mỗi nhân viên trong kỳ
      const totalDebtRevenue = listEmployeeData.reduce((commissionAccu, employee) => commissionAccu + employee.debtRevenue, 0);

      let previousRevenue = 0;
      for (const employeeData of listEmployeeData) {

        // Tính hoa hồng cá nhân
        employeeData.commissions = await this.calculateEmployeeCommission(
          employeeData,
          commissionPolicy,
          totalDebtRevenue, // dùng cho công thức ko lũy tiến
          previousRevenue, // dùng cho công thức lũy tiến
        );

        // console.log('employeeData', employeeData);

        // Tăng tổng doanh thu của mỗi nhân viên trong kỳ - dùng cho công thức lũy tiến
        previousRevenue += employeeData.debtRevenue;
      }

      // Lưu lại kết quả tính hoa hồng
      employeeTransactionMap[employeeCode] = listEmployeeData;
    }

    // Loop lần 3: Tổng hợp dữ liệu từ map theo từng employee thành một list commission list để lưu lại khớp với định dạng DB hiện tại
    for (const employeeCode of Object.keys(employeeTransactionMap)) {
      const listEmployeeData: EmployeeDataDto[] = employeeTransactionMap[employeeCode];
      for (const employeeData of listEmployeeData) {
        modelList.push(new DebtCommissionListDto({
          index: employeeData.index,
          code: employeeData.code,
          debtType: employeeData.debtType,
          installmentName: employeeData.installmentName,
          commission: commission,
          contract: employeeData.contract,
          project: employeeData.project,
          propertyUnit: employeeData.propertyUnit,
          commissionPolicy: employeeData.commissionPolicy,
          pos: employeeData.pos,
          customer: employeeData.customer,
          debtCollector: employeeData.debtCollector,
          debtage: employeeData.debtage,
          vatRate: employeeData.vatRate,
          debtRevenue: employeeData.debtRevenue,
          employees: [
            {
              id: employeeData.debtCollector.id,
              code: employeeData.debtCollector.code,
              name: employeeData.debtCollector.name,
              debtRate: employeeData.debtRate,
              commissions: employeeData.commissions,
            }
          ],
        }));
      }
    }
    return modelList;
  }

  private async calculateEmployeeCommission(
    data: EmployeeDataDto,
    commissionPolicy: IDebtPolicy,
    totalDebtRevenue: number,
    previousRevenue: number): Promise<CommissionDataDto[]> {

    // Không có tỷ lệ hoa hồng => không tính được kết quả
    if (!commissionPolicy.listRate || !commissionPolicy.listRate.length) {
      return [];
    }

    // Nếu chính sách HH check ô VAT hoặc không
    const commissionVatRate = data.vatRate;
    // Trường thưởng thêm = [Số tiền] Trong chính sách bán hàng * [%Tỷ lệ chia] / [Tính từ giá VAT]
    // Nếu chính sách HH check ô Lũy tiến hoặc không
    if (commissionPolicy.isProgressive) {
      // Lũy tiến:
      let totalRevenueStart: number = previousRevenue;
      let totalRevenueEnd: number = previousRevenue + data.debtRevenue;
      const listCommission = [];

      // Xác định tỷ lệ hoa hồng cho giá trị trước khi giao dịch
      const commissionRateDataStart = commissionPolicy.listRate.find(rate =>
        (!rate.bottomPrice || (rate.bottomPrice && rate.bottomPrice <= totalRevenueStart))
        && (!rate.topPrice || (rate.topPrice > totalRevenueStart)));

      // Xác định tỷ lệ hoa hồng cho giá trị sau khi giao dịch
      const commissionRateDataEnd = commissionPolicy.listRate.find(rate =>
        (!rate.bottomPrice || (rate.bottomPrice && rate.bottomPrice <= totalRevenueEnd))
        && (!rate.topPrice || (rate.topPrice > totalRevenueEnd)));

      if (!commissionRateDataStart || !commissionRateDataEnd) {
        return [];
      }

      // Toàn bộ giao dịch nằm trong 1 mức hoa hồng => 1 mức commission
      if ((commissionRateDataStart.topPrice === commissionRateDataEnd.topPrice)
        && (commissionRateDataStart.bottomPrice === commissionRateDataEnd.bottomPrice)) {
        listCommission.push(this.calculateEmployeeCommissionData(
          data.debtRevenue,
          data.debtRate,
          commissionRateDataStart,
          commissionVatRate,
          totalDebtRevenue));
      } else {
        // Toàn bộ giao dịch nằm trong ít nhất 2 mức hoa hồng khác nhau => nhiều mức commisison
        // Tính hoa hồng cho tỷ lệ đầu tiên
        listCommission.push(this.calculateEmployeeCommissionData(
          commissionRateDataStart.topPrice - totalRevenueStart,
          data.debtRate,
          commissionRateDataStart,
          commissionVatRate,
          totalDebtRevenue));
        // Tính hoa hồng cho tỷ lệ cuối cùng
        listCommission.push(this.calculateEmployeeCommissionData(
          totalRevenueEnd - commissionRateDataEnd.bottomPrice,
          data.debtRate,
          commissionRateDataEnd,
          commissionVatRate,
          totalDebtRevenue));
        // Tính hoa hồng cho tỷ lệ ở giữa (nếu có)
        const listCommisisonDataBetween: IDebtCommissionRate[] = commissionPolicy.listRate
          .filter(rate => (rate.topPrice && rate.bottomPrice)
            && (rate.bottomPrice >= commissionRateDataStart.topPrice)
            && (rate.topPrice <= commissionRateDataEnd.bottomPrice))
          .sort((r1, r2) => r1.bottomPrice - r2.bottomPrice);
        for (const commissionRate of listCommisisonDataBetween) {
          listCommission.push(this.calculateEmployeeCommissionData(
            commissionRate.topPrice - commissionRate.bottomPrice,
            data.debtRate,
            commissionRate,
            commissionVatRate,
            totalDebtRevenue));
        }
      }
      // Gán bonus cho commission đầu tiên của nhân viên
      listCommission.sort((c1, c2) => c1.rate - c2.rate);

      return listCommission;
    } else {
      // Không lũy tiến: TỔNG DOANH THU THU HỒI nằm trong vùng level nào thì trả về [Tỷ lệ hoa hồng] cho giao dịch đó
      const commissionRateData = commissionPolicy.listRate.find(rate =>
        (!rate.bottomPrice || (rate.bottomPrice && rate.bottomPrice <= totalDebtRevenue))
        && (!rate.topPrice || (rate.topPrice > totalDebtRevenue)));
      if (!commissionRateData) {
        return [];
      }
      const commission = this.calculateEmployeeCommissionData(
        data.debtRevenue,
        data.debtRate,
        commissionRateData,
        commissionVatRate,
        totalDebtRevenue);

      return [commission];
    }
  }

  private calculateEmployeeCommissionData(
    debtRevenue: number,
    debtRate: number,
    commissionRateData: IDebtCommissionRate,
    commissionVatRate: number,
    totalDebtRevenue: number
  ): CommissionDataDto {
    let debtCommissionRevenue = 0;
    if (commissionRateData && commissionRateData.recordedCommissionUnit === 'VNĐ') { // VNĐ
      debtCommissionRevenue = this.rounding(new Decimal(commissionRateData.recordedCommission)
        .times(commissionVatRate)
        .times(debtRate)
        .toNumber());
    } else if (commissionRateData && commissionRateData.recordedCommissionUnit === '%') { // %
      debtCommissionRevenue = this.rounding(new Decimal(debtRevenue)
        .times(commissionRateData.recordedCommission)
        .times(commissionVatRate)
        .times(debtRate)
        .toNumber());
    }

    return new CommissionDataDto({
      debtRevenue,
      totalDebtRevenue,
      recordedCommission: commissionRateData.recordedCommission,
      unit: commissionRateData.recordedCommissionUnit,
      debtRate,
      commissionVatRate,
      debtCommissionRevenue
    });
  }

  async exportTxs(user, id, fileName, isSeeAll) {
    const listComm = await this.commListRepo.listDebtCommissionByUser(
      user,
      { "commission.id": id }
    );

    const comm = await this.commRepo.findDebtCommissionById(id);
    return await this.fileGenerationService.exportCommission(user, fileName, listComm, comm, false, true);
  }

  async validateExcel(unwindComList, worksheet) {
    const history = [];
    let failedFile = {};
    // Validate headers
    const headerValidation = validateExcelHeaders(worksheet);
    if (!headerValidation.isValid) {
      console.error('Header validation failed:', headerValidation.errors);
      history.push(
        {
          line: `5`,
          error: `File của bạn không đúng với template yêu cầu`
        },
        {
          line: `6`,
          error: `File của bạn không đúng với template yêu cầu`
        }
      );
    }

    const bodyValidation = validateBody(unwindComList, worksheet);
    if (!bodyValidation.isValid) {
      console.error('Body validation failed:', bodyValidation.errors);
      history.push(...bodyValidation.errors);
    }

    // convert to excel file
    if (history.length > 0) {
      // Add header for error column
      const headerRow = worksheet.getRow(5);
      let lastColumnIndex = 0;

      headerRow.eachCell({ includeEmpty: false }, (cell, colNumber) => {
        if (colNumber > lastColumnIndex) {
          lastColumnIndex = colNumber;
        }
      });

      // Get the next column letter after the last column
      const errorColumnIndex = lastColumnIndex + 1;
      const errorColumnLetter = this.getExcelColumnLetter(errorColumnIndex);

      // Get the last row number
      const lastRowNum = worksheet.lastRow ? worksheet.lastRow.number : 100;

      // Apply borders and background to all cells in error column FIRST
      for (let i = 5; i <= lastRowNum; i++) {
        const row = worksheet.getRow(i);
        const cell = row.getCell(errorColumnLetter);

        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }

      const headerRow4 = worksheet.getRow(4);
      headerRow4.getCell(errorColumnLetter).value = 'Lỗi';
      headerRow4.getCell(errorColumnLetter).font = { bold: true };
      headerRow4.getCell(errorColumnLetter).alignment = { vertical: 'middle', horizontal: 'center' };
      headerRow4.getCell(errorColumnLetter).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFF0000' } // Red background
      };

      // Add error messages to corresponding rows
      const errorMap = {};

      // Group errors by line number
      history.forEach(item => {
        // if (typeof item.line === 'number') {
        if (!errorMap[item.line]) {
          errorMap[item.line] = [];
        }
        errorMap[item.line].push(item.error);
        // }
      });

      // Write errors to the worksheet
      Object.keys(errorMap).forEach(lineNumber => {
        const row = worksheet.getRow(parseInt(lineNumber));
        row.getCell(errorColumnLetter).value = errorMap[lineNumber].join('\n');
        row.getCell(errorColumnLetter).alignment = { vertical: 'top', horizontal: 'left', wrapText: true };
      });

      try {
        // Generate unique filename with timestamp
        const timestamp = new Date().getTime();
        const originalFileName = 'BangKeHoaHongCongNoDieuChinh';
        const fileNameWithoutExt = originalFileName.replace('.xlsx', '');
        const newFileName = `${fileNameWithoutExt}_Error_${timestamp}.xlsx`;

        // Create path to generated-files folder
        const workbook = worksheet.workbook;
        const nodebuffer = await workbook.xlsx.writeBuffer();
        // const errorFilePath = path.resolve(this.staticAssetService.getUploadFolderPath(), newFileName);
        // await fs.writeFileAsync(errorFilePath, nodebuffer);

        // console.log('Error file saved successfully at:', errorFilePath);

        // upload to s3
        const formData = new FormData();
        formData.append('file', nodebuffer, { filename: `${newFileName}.xlsx` });
        formData.append('path', 'commission');
        const response = await axios.default.post(
          process.env.MTD_UPLOAD,
          formData,
          { headers: { ...formData.getHeaders() } });

        if (response.data.statusCode != '0') {
          return this.getResponse('COMME0001');
        }

        failedFile = { ...response.data.data, name: newFileName };

      } catch (err) {
        console.error('Error saving error file:', err);
      }
    }

    return { history, failedFile };
  }

  getExcelColumnLetter(columnIndex) {
    let columnLetter = '';
    let temp = columnIndex;

    while (temp > 0) {
      let remainder = (temp - 1) % 26;
      columnLetter = String.fromCharCode(65 + remainder) + columnLetter;
      temp = Math.floor((temp - remainder) / 26);
    }

    return columnLetter;
  }
}