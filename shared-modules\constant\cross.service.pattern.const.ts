
export class CmdPatternConst {
  static CMD_PATTERN = 'msx-adsg';
  static STS = {
    ACCOUNT: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.sts.account.sync',
      JWT_AUTH_GUARD:
        CmdPatternConst.CMD_PATTERN + '.sts.account.jwt-auth-guard',
      GET_INFO: CmdPatternConst.CMD_PATTERN + '.sts.account.get.info',
      GET_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.sts.account.get.by.query',
    },
  };
  static INTEGRATED_HISTORY = {
    CREATE: CmdPatternConst.CMD_PATTERN + '.integrated.history.create',
  }
  static MASTERDATA = {
    PROVINCE: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.province.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.province.sync',
    },
    DISTRICT: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.district.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.district.sync',
    },
    WARD: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.ward.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.ward.sync',
    },
    LOCATION: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.location.sync',
      COUNT: CmdPatternConst.CMD_PATTERN + '.masterdata.location.count',
    },
    POSTITION: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.position.sync',
    },
    JOB_TITLE: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.job-title.sync',
    },
    PLURALITY_JOB_TITLE: {
      SYNC:
        CmdPatternConst.CMD_PATTERN + '.masterdata.plurality-job-title.sync',
    },
    BANK: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.bank.sync',
      GET_BY_NAME: CmdPatternConst.CMD_PATTERN + '.masterdata.bank.get.by.name',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.bank.get.all',
      COUNT: CmdPatternConst.CMD_PATTERN + '.masterdata.bank.count',
    },
    MATERIAL_TYPE: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.material.type.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.material.type.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.material.type.get.one',
    },
    GLACCOUNT: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.glaccount.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.glaccount.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.glaccount.get.one',
    },
    MATERIAL_GROUP: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.material.group.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.material.group.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.material.group.get.one',
    },
    MATERIAL_GROUP_5: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.material.group.5.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.material.group.5.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.material.group.5.get.one',
    },
    BASE_UNIT: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.base.unit.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.base.unit.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.base.unit.get.one',
    },
    DIVISION: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.division.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.division.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.division.get.one',
    },
    PROD_HIERARCHY: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.prod.hierarchy.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.prod.hierarchy.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.prod.hierarchy.get.one',
    },
    ACCT_ASSMT_GRP_MAT: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.acct.assmt.grp.mat.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.acct.assmt.grp.mat.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.acct.assmt.grp.mat.get.one',
    },
    TAX_CLASSIFICATION_MATERIAL: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.tax.classification.material.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.tax.classification.material.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.tax.classification.material.get.one',
    },
    DISTRIBUTION_CHANEL: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.distribution.channel.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.distribution.channel.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.distribution.channel.get.one',
    },
    BUSINESS_AREA: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.business.area.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.business.area.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.business.area.get.one',
    },
    COST_CENTER: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.cost.center.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.cost.center.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.cost.center.get.one',
    },
    COST_ITEM: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.masterdata.cost.item.sync',
      GET_ALL: CmdPatternConst.CMD_PATTERN + '.masterdata.cost.item.get.all',
      GET_ONE: CmdPatternConst.CMD_PATTERN + '.masterdata.cost.item.get.one',
    },
    INVESTOR: {
      GET_BY_NAME:
        CmdPatternConst.CMD_PATTERN + '.masterdata.investor.get.by.name',
    },
  };
  static ORGCHART = {
    INTERNAL_ORGCHART: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.orgchart.internal-orgchart.sync',
    },
    EXTERNAL_ORGCHART: {
      SEND: CmdPatternConst.CMD_PATTERN + '.orgchart.external.orgchart.send.sap',
      SYNC: CmdPatternConst.CMD_PATTERN + '.orgchart.external.orgchart.sync',
    },
    LISTENER: {
      GET_DROPDOWN: CmdPatternConst.CMD_PATTERN + '.orgchart.dropdown',
      GET_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.orgchart.get.by.query',
      GET_POS_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.get.pos.by.query.listener',
    },
  };
  static EMPLOYEE = {
    SYNC: CmdPatternConst.CMD_PATTERN + '.employee.sync', // TODO xóa sau khi chuyển api đồng bộ internal employee
    INTERNAL_EMPLOYEE: {
      SYNC: CmdPatternConst.CMD_PATTERN + '.employee.internal-employee.sync',
      GET_BY_NAME:
        CmdPatternConst.CMD_PATTERN + '.employee.internal-employee.get.by.name',
      GET_BY_ID:
        CmdPatternConst.CMD_PATTERN + '.employee.internal-employee.get.by.id',
    },
    LOCATION: {
      SYNC: {
        PROVINCE: CmdPatternConst.CMD_PATTERN + '.employee.province.sync',
        DISTRICT: CmdPatternConst.CMD_PATTERN + '.employee.district.sync',
        WARD: CmdPatternConst.CMD_PATTERN + '.employee.ward.sync',
      },
    },
    GET_EMPLOYEE_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.employee.get.by.query',
    GET_ALL_STAFFS: CmdPatternConst.CMD_PATTERN + '.employee.get.all.staffs',
    DROPDOWN: CmdPatternConst.CMD_PATTERN + '.employee.listener.dropdown',
    LISTENER: {
      FIND_ONE_BY_QUERY:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.find.one.by.query',
      GET_BY_QUERY:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.get.by.query',
      CREATE:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.create',
      DELETE:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.delete',
      UPDATE:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.update',
      UPDATE_MANY:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.update.many',
      BULK_WRITE:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.bulk.write',
      FIND_AND_UPDATE:
        CmdPatternConst.CMD_PATTERN +
        '.employee.listener.lead.employee.find.update',
    }
  };
  static BANK = {
    UPDATE_BANK_EXTERNAL_ORGCHART:
      CmdPatternConst.CMD_PATTERN + '.update.bank.external_orgchart',
    UPDATE_BANK_INTERNAL_ORGCHART:
      CmdPatternConst.CMD_PATTERN + '.update.bank.internal_orgchart',
  };
  static ADMIN = {
    ACCOUNT: {
      GETROLES: CmdPatternConst.CMD_PATTERN + '.admin.account.role.getall',
      GETACCOUNTS: CmdPatternConst.CMD_PATTERN + '.admin.account.getall',
      CREATE: CmdPatternConst.CMD_PATTERN + '.admin.account.create',
      EXT_CREATE: CmdPatternConst.CMD_PATTERN + '.admin.account.external.create',
      UPDATE: CmdPatternConst.CMD_PATTERN + '.admin.account.update',
      SYNC: CmdPatternConst.CMD_PATTERN + '.admin.account.sync',
    },
  };
  static PROJECT = {
    SYNC: CmdPatternConst.CMD_PATTERN + '.project.sync',
    GETBLOCKBYCODE: CmdPatternConst.CMD_PATTERN + '.block.get.all.by.query',
    UPDATE_INVESTOR: CmdPatternConst.CMD_PATTERN + '.project.update.investor',
    GET_PROJECT_BY_ID: CmdPatternConst.CMD_PATTERN + '.get.project.id',
    GET_PROJECT_BY_IDS: CmdPatternConst.CMD_PATTERN + '.get.project.ids',
    GET_PROJECT_BY_CODES: CmdPatternConst.CMD_PATTERN + '.get.project.codes',
    GET_PROJECT_BY_ID_FPT: CmdPatternConst.CMD_PATTERN + '.property.get.project.by.id.fpt',
    GET_PROJECT_DROPDOWN_LIST: CmdPatternConst.CMD_PATTERN + '.property.get.project.dropdown.list',
  };
  static CUSTOMER = {
    UPDATE: CmdPatternConst.CMD_PATTERN + '.customer.update',
    UPDATE_AND_SYNC_SAP: CmdPatternConst.CMD_PATTERN + '.customer.update.and.sync.sap',
    SAP_CUSTOMER: CmdPatternConst.CMD_PATTERN + '.sap.customer.send',
    SAP_CUSTOMER_UPDATE: CmdPatternConst.CMD_PATTERN + '.sap.customer.update',
    SAP_CUSTOMER_UPDATE_BPID: CmdPatternConst.CMD_PATTERN + '.sap.customer.bpid.update',
    SAP_CUSTOMER_SYNC: CmdPatternConst.CMD_PATTERN + '.sap.customer.sync',
    GET_INFO_WITH_QUERY: CmdPatternConst.CMD_PATTERN + '.customer.get.with.query',
    UPDATE_TAKECARE: CmdPatternConst.CMD_PATTERN + '.customer.update.takecare',
  };
  static LEAD = {
    UPDATE_BY_CODE: CmdPatternConst.CMD_PATTERN + '.lead.update.by.code',
    LISTENER: {
      GET_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.lead.get.by.query',
      UPDATE_DEMAND: CmdPatternConst.CMD_PATTERN + '.lead.update.demand',
    }
  };
  static DEMAND = {
    DEMAND_CUSTOMER: {
      LISTENER: {
        IMPORT_DEMAND_CUSTOMER:
          CmdPatternConst.CMD_PATTERN + '.import.demand.customer',
        SAP_DEMAND_CUSTOMER:
          CmdPatternConst.CMD_PATTERN + '.sap.demand.customer.send',
        GET_DEMAND_CUSTOMER_BY_IDENTIFIES:
          CmdPatternConst.CMD_PATTERN + '.get.demand.customer.by.identifies',
        GET_DEMAND_CUSTOMER_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.get.demand.customer.by.query',
        GET_DEMAND_CUSTOMER: CmdPatternConst.CMD_PATTERN + '.get.demand.customer',
        GET_ALL_DEMAND_CUSTOMER_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.get.all.demand.customer.by.query',
        UPDATE_TAKECARE: CmdPatternConst.CMD_PATTERN + '.update.takecare.for.demand.customer',
        GEN_DEMAND_CUSTOMER_CODE: CmdPatternConst.CMD_PATTERN + '.gen.demand.customer.code',
      },
      LIST_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.demand.list.by.query',
      UPDATE: CmdPatternConst.CMD_PATTERN + '.demand.update',
      SAP_DEMAND_CUSTOMER_SYNC:
        CmdPatternConst.CMD_PATTERN + '.sap.demand.customer.sync',
      SAP_DEMAND_CUSTOMER_UPDATE: CmdPatternConst.CMD_PATTERN + '.sap.demand.customer.update',
      DEMAND_CUSTOMER_SYNC:
        CmdPatternConst.CMD_PATTERN + '.demand.customer.sync',
      DEMAND_CUSTOMER_DOCUMENT_SYNC:
        CmdPatternConst.CMD_PATTERN + '.demand.customer.document.sync',
      GET_CUSTOMER_BY_CUSTOMER_CODE: CmdPatternConst.CMD_PATTERN + '.demand.get.by.customer.code',
      FIND_CUSTOMER_BY_PARAM: CmdPatternConst.CMD_PATTERN + 'find.customer.by.param',
      FIND_BY_QUERY: CmdPatternConst.CMD_PATTERN + 'find.by.query',
    },
    UPDATE: CmdPatternConst.CMD_PATTERN + '.demand.update',
    LIST_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.demand.list.by.query',
    GET_BY_CUSTOMER_ID: CmdPatternConst.CMD_PATTERN + '.demand.get.customer.id',
    GET_BY_EMPLOYEE_ID: CmdPatternConst.CMD_PATTERN + '.demand.get.employee.id',
    GET_LIKE_EMAIL_NAME: CmdPatternConst.CMD_PATTERN + '.demand.get.like.email.name',
    SAP_DEMAND_CUSTOMER_UPDATE: CmdPatternConst.CMD_PATTERN + '.sap.demand.customer.update',
    GET_DEMAND_CUSTOMER_BY_IDENTIFIES: CmdPatternConst.CMD_PATTERN + '.get.demand.customer.by.identifies',
  };
  static EAPP = {
    COMMON_API: {
      GEN_PROPOSAL_CODE: CmdPatternConst.CMD_PATTERN + '.eap.common-api.gen.proposal.code',
      GET_PROC_DEF_ID: CmdPatternConst.CMD_PATTERN + '.eap.common-api.get.proc.def.id',
      UPDATE_STATUS: CmdPatternConst.CMD_PATTERN + '.eap.common-api.update.status',
      UPDATE_VERSION: CmdPatternConst.CMD_PATTERN + '.eap.common-api.update.version',
    },
    CUSTOMER_EXPLOITATION_PROPOSAL: {
      CREATE: CmdPatternConst.CMD_PATTERN + '.eap.customer-exploitaion-proposal.create',
      UPDATE: CmdPatternConst.CMD_PATTERN + '.eap.customer-exploitaion-proposal.update',
    },
    COMMISSION_ADVANCE_PROPOSAL: {
      CREATE: CmdPatternConst.CMD_PATTERN + '.eap.non-sample-proposal.create',
      UPDATE: CmdPatternConst.CMD_PATTERN + '.eap.non-sample-proposal.update',
    }
  };
  static PROPOSAL = {
    CREATE: CmdPatternConst.CMD_PATTERN + '.proposal.create',
    UPDATE: CmdPatternConst.CMD_PATTERN + '.proposal.update',
    DELETE: CmdPatternConst.CMD_PATTERN + '.proposal.delete',
    LIST: CmdPatternConst.CMD_PATTERN + '.proposal.list',
    GET: CmdPatternConst.CMD_PATTERN + '.proposal.get',
    LIST_STATUS: CmdPatternConst.CMD_PATTERN + '.proposal.list-status',
    LIST_TYPE: CmdPatternConst.CMD_PATTERN + '.proposal.list-type',
    UPLOAD_FILE: CmdPatternConst.CMD_PATTERN + '.proposal.upload-file',
  };
  static TRANSACTION = {
    LISTENER: {
      GET_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.property.primary.transaction.get.by.query',
    },
    SEND_SAP: CmdPatternConst.CMD_PATTERN + '.transaction.send.sap',
  };
  static PRIMARY_CONTRACT = {
    GET_TRANSACTION_HISTORY: CmdPatternConst.CMD_PATTERN + '.primary.contract.get.transaction.history',
    GET_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.primary.contract.transaction.get.by.query',
    GET_BY_QUERY_STAFF_INVOLVED: CmdPatternConst.CMD_PATTERN + '.primary.contract.transaction.get.by.query.staff.involved',
    GET_CONTRACT_BY_PROPERTY_UNIT_ID: CmdPatternConst.CMD_PATTERN + '.get.contract.by.property.unit.id',
    GET_ONE_WNERSHIP_CETIFICATE_SETTING: CmdPatternConst.CMD_PATTERN + '..get.one.ownership.certificate.setting',
    PROPOSAL_FOR_INTEREST_WAIVER_OR_REDUCTION: CmdPatternConst.CMD_PATTERN + '.primary.contract.update.status.for.interest.waiver.or.reduction',
    DEBT_COMMISSION_POLICY_PROPOSAL: CmdPatternConst.CMD_PATTERN + '.primary.contract.update.status.for.debt_commisison_policy',
    PROPOSAL_FOR_REINSTATING_CONTRACT_INTO_DEBT_LIST: CmdPatternConst.CMD_PATTERN + '.primary.contract.update.status.for.reinstating.contract.into.debt.list',
    PROPOSAL_FOR_EXCLUDING_CONTRACT_FROM_DEBT_LIST: CmdPatternConst.CMD_PATTERN + '.primary.contract.update.status.for.excluding.contract.into.debt.list',
    CHECK_CONDITION_TO_UPDATE_DEBT_PENALTY: CmdPatternConst.CMD_PATTERN + '.primary.contract.check.condition.to.update.debt.penalty',
    GET_CONTRACT_STATUS: CmdPatternConst.CMD_PATTERN + '.primary.contract.get.contract.status',
    HANDOVER_SEND_SAP: CmdPatternConst.CMD_PATTERN + '.primary.contract.handover.send.sap',
    SEND_SAP: CmdPatternConst.CMD_PATTERN + '.primary.contract.send.sap',
    SAP_DEPOSIT_CONTRACT_RECEIPT: CmdPatternConst.CMD_PATTERN + '.sap.deposit.contract.receipt',
    SAP_DEPOSIT_CONTRACT_PAYMENT: CmdPatternConst.CMD_PATTERN + '.sap.deposit.contract.payment',
  };
  static SALE_POLICY = {
    GET: CmdPatternConst.CMD_PATTERN + '.sale.policy.get',
    LIST: CmdPatternConst.CMD_PATTERN + '.sale.policy.list',
    UPDATE: CmdPatternConst.CMD_PATTERN + '.sale.policy.update',
  };
  static INDICATOR = {
    GET: CmdPatternConst.CMD_PATTERN + '.indicator.get',
    LIST: CmdPatternConst.CMD_PATTERN + '.indicator.list',
    UPDATE: CmdPatternConst.CMD_PATTERN + '.indicator.update',
  };
  static COMMISSION_POLICY = {
    GET: CmdPatternConst.CMD_PATTERN + '.commission.policy.get',
    LIST: CmdPatternConst.CMD_PATTERN + '.commission.policy.list',
    UPDATE: CmdPatternConst.CMD_PATTERN + '.commission.policy.update',
  };
  static COMMISSION = {
    SEND_SAP: CmdPatternConst.CMD_PATTERN + '.commission.send.sap',
  };
  static EXPENSE_LIST = {
    UPDATE_RECEIPT_AMOUNT: CmdPatternConst.CMD_PATTERN + '.expense.list.update.receipt.amount',
    UPDATE_PAYMENT_AMOUNT: CmdPatternConst.CMD_PATTERN + '.expense.list.update.payment.amount',
  };
  static PROPERTY = {
    GET_TRANSACTION_HISTORY: CmdPatternConst.CMD_PATTERN + '.property.get.transaction.history',
    CALCULATE_TOTAL_DEBT_DAYS: CmdPatternConst.CMD_PATTERN + '.calculate.total.debt.days',
    SYNC_SAP: CmdPatternConst.CMD_PATTERN + '.property.sync.sap',
    SYNC_CONS: CmdPatternConst.CMD_PATTERN + '.property.sync.cons',
  };
  static ZNS = {
    SEND: CmdPatternConst.CMD_PATTERN + '.zns.send',
    GET: CmdPatternConst.CMD_PATTERN + '.zns.get',
  };
  static MAILER = {
    SEND_DEBT_REMINDER: CmdPatternConst.CMD_PATTERN + '.mailer.send.debt.reminder'
  };
  static SCHEDULE = {
    CREATE_INTEREST: CmdPatternConst.CMD_PATTERN + '.create.interest'
  };
  static PROVIDER = {
    INSERT_MANY: CmdPatternConst.CMD_PATTERN + 'provider.insert.many',
    LIST: CmdPatternConst.CMD_PATTERN + 'provider.list'
  };
}
