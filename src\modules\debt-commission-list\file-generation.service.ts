
import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
const fs = Bluebird.promisifyAll(require('fs'));
import * as path from 'path';
import { StaticAssetService } from '../config/static-asset.service';
import { existsSync, unlinkSync } from 'fs';
const libre = require('libreoffice-convert');
const XlsxTemplate = require('xlsx-template');
const _ = require("lodash");
import * as moment from 'moment';
import { EmployeeService } from '../employee.query/service';
import * as axios from 'axios';
import * as FormData from 'form-data';
import * as ExcelJS from 'exceljs';
import { ExcelUtils } from '../shared/classes/excel-ultils';

@Injectable()
export class FileGenerationService {

  constructor(
    private readonly staticAssetService: StaticAssetService,
    private readonly employeeService: EmployeeService,
  ) { }

  async exportCommission(userLogged, fileName, data, comm, isConvertPdf = false, isUploadS3 = false) {
    // const listComm = _.groupBy(data, 'id');
    const groupData = _.groupBy(data, 'id');
    let dataTemplate = [];
    for (const [key, value] of Object.entries(groupData)) {
      // console.log('value', value);
      const _value: any = value;
      // _value.map((item: any, index) => {

      // });
      dataTemplate.push({
        ..._value[0]
      });
    }
    const tempFileName = `${new Date().getTime()}.xlsx`;
    await this.getTemplateFile(data, tempFileName);

    // Load an XLSX file into memory
    const pathTemp = path.resolve(this.staticAssetService.getUploadFolderPath(), `${tempFileName}`);
    const templateFile = await fs.readFileAsync(pathTemp, 'binary');

    // Create a template
    var template = new XlsxTemplate(templateFile);

    // Replacements take place on first sheet
    var sheetNumber = 1;

    // Set up some placeholder values matching the placeholders in the template
    var values = {
      commissionName: comm.name,
      commissionPeriod: comm.period,
      project: comm.project,
    };

    // Perform substitution
    template.substitute(sheetNumber, values);

    // Get binary data
    const nodebuffer = template.generate({
      type: 'nodebuffer'
    });

    if (isUploadS3) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(nodebuffer);

      // Process each worksheet
      const excelUtil = new ExcelUtils();
      const buffer = await excelUtil.rehighlightAsterisks(workbook);

      const formData = new FormData();
      formData.append('file', buffer, { filename: `${fileName}.xlsx` });
      formData.append('path', 'commission');
      // formData.append('useOriginalName', 'true');
      try {
        const response = await axios.default.post(
          process.env.MTD_UPLOAD,
          formData,
          { headers: { ...formData.getHeaders() } });

        // remove file 
        if (existsSync(pathTemp)) {
          unlinkSync(pathTemp);
        }

        return response.data;
      } catch (error) {
        console.error('error from mtd', error);
        if (existsSync(pathTemp)) {
          unlinkSync(pathTemp);
        }
        throw error;
      }
    }

    try {
      const pathXlsx = path.resolve(this.staticAssetService.getUploadFolderPath(), `${fileName}.xlsx`);
      const pathPdf = path.resolve(this.staticAssetService.getUploadFolderPath(), `${fileName}.pdf`);
      await fs.writeFileAsync(pathXlsx, nodebuffer);
      if (isConvertPdf) {
        return new Promise(async (resolve, reject) => {
          const extend = '.pdf';
          // Read file
          const enterPath = await fs.readFileSync(pathXlsx);
          // Convert it to pdf format with undefined filter (see Libreoffice doc about filter)
          return await libre.convert(enterPath, extend, undefined, async (err, done) => {
            if (err) {
              if (existsSync(pathXlsx)) {
                unlinkSync(pathXlsx);
              }
              if (existsSync(pathTemp)) {
                unlinkSync(pathTemp);
              }
              reject(err);
            }
            // Here in done you have pdf file which you can save or transfer in another stream
            await fs.writeFileSync(pathPdf, done);
            if (existsSync(pathXlsx)) {
              unlinkSync(pathXlsx);
            }
            if (existsSync(pathTemp)) {
              unlinkSync(pathTemp);
            }
            resolve(fileName);
          });
        });
      } else {
        if (existsSync(pathTemp)) {
          unlinkSync(pathTemp);
        }
        return fileName;
      }
    }
    catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties
      };
      throw e;
    }
  }

  async exportCommissionByUser(userLogged, file, data, isConvertPdf = false) {

    // Load an XLSX file into memory
    let emp = await this.employeeService.getEmployeeById(userLogged.id);
    const pathTemp = path.resolve(this.staticAssetService.getTemplateFolderPath(), `Template_DownloadByUser.xlsx`);
    const templateFile = await fs.readFileAsync(pathTemp, 'binary');
    // Create a template
    var template = new XlsxTemplate(templateFile);
    // Replacements take place on first sheet
    var sheetNumber = 1;
    // Set up some placeholder values matching the placeholders in the template
    data.items.map(item => {
      if (item.employees.commissions.commissionReceived) {
        item.commissionReceived = item.employees.commissions.commissionReceived;
      }
      item.period = this.getDatePeriod(item.commission.name)
    });
    const createdDate = new Date();
    var values = {
      rows: data.items,
      commissionName: data.items[0].period,
      createdDate: {
        day: ("0" + (createdDate.getDate())).slice(-2),
        month: ("0" + (createdDate.getMonth() + 1)).slice(-2),
        year: createdDate.getFullYear()
      },
      employee: emp.toJSON()
    };

    // Perform substitution
    template.substitute(sheetNumber, values);
    // var sheetNumber = 2;
    // template.substitute(sheetNumber, values);
    // Get binary data
    var nodebuffer = template.generate({ type: 'nodebuffer' });
    try {
      const pathXlsx = path.resolve(this.staticAssetService.getUploadFolderPath(), `${file}.xlsx`);
      const pathPdf = path.resolve(this.staticAssetService.getUploadFolderPath(), `${file}.pdf`);
      await fs.writeFileAsync(pathXlsx, nodebuffer);
      if (isConvertPdf) {
        return new Promise(async (resolve, reject) => {
          const extend = '.pdf';
          // Read file
          const enterPath = await fs.readFileSync(pathXlsx);
          // Convert it to pdf format with undefined filter (see Libreoffice doc about filter)
          return await libre.convert(enterPath, extend, undefined, async (err, done) => {
            if (err) {
              if (existsSync(pathXlsx)) {
                unlinkSync(pathXlsx);
              }
              reject(err);
            }
            // Here in done you have pdf file which you can save or transfer in another stream
            await fs.writeFileSync(pathPdf, done);
            if (existsSync(pathXlsx)) {
              unlinkSync(pathXlsx);
            }
            resolve(file);
          });
        });
      } else {
        return file;
      }
    }
    catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      throw e;
    }
  }
  private getDatePeriod(name) {
    return moment(new Date(name)).isValid() ? moment(name, 'YYYYMM').format('MM/YYYY') : '';
  }
  async download(url, dest) {
    const http = require('https');
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(dest, { flags: "wx" });

      const request = http.get(url, response => {
        if (response.statusCode === 200) {
          response.pipe(file);
        } else {
          file.close();
          fs.unlink(dest, () => { }); // Delete temp file
          reject(`Server responded with ${response.statusCode}: ${response.statusMessage}`);
        }
      });

      request.on("error", err => {
        file.close();
        fs.unlink(dest, () => { }); // Delete temp file
        reject(err.message);
      });

      file.on("finish", () => {
        resolve(true);
      });

      file.on("error", err => {
        file.close();

        if (err.code === "EEXIST") {
          reject("File already exists");
        } else {
          fs.unlink(dest, () => { }); // Delete temp file
          reject(err.message);
        }
      });
    });
  }

  async getTemplateFile(data, file) {
    const excelUtil = new ExcelUtils();
    return excelUtil.loadFile(this.staticAssetService.getTemplateFileDownload('Template_GDHHCN.xlsx')).then(async () => {
      let rowIndex = 8;
      data.forEach((item, index) => {
        if (rowIndex != 8) {
          //   // Thông tin chung
          excelUtil.insertRow([['A1']], rowIndex);
          const getUpperIndexValue = excelUtil.getCellValue(`A${rowIndex - 1}`);

          excelUtil.duplicateCell(`${_.get(item, 'index', '') == getUpperIndexValue ? '' : _.get(item, 'index', '')}`, `A${rowIndex - 1}`, `A${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'contract.code', '')}`, `B${rowIndex - 1}`, `B${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'contract.name', '')}`, `C${rowIndex - 1}`, `C${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'customer.code', '')}`, `D${rowIndex - 1}`, `D${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'customer.name', '')}`, `E${rowIndex - 1}`, `E${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'debtCollector.code', '')}`, `F${rowIndex - 1}`, `F${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'debtCollector.name', '')}`, `G${rowIndex - 1}`, `G${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'installmentName', '')}`, `H${rowIndex - 1}`, `H${rowIndex}`);

          // Thông tin hoa hồng công nợ
          excelUtil.duplicateCell(`${_.get(item, 'debtage.name', '')}`, `I${rowIndex - 1}`, `I${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'debtType', '')}`, `J${rowIndex - 1}`, `J${rowIndex}`);
          excelUtil.duplicateCell(+item.employees.commissions.debtRevenue, `K${rowIndex - 1}`, `K${rowIndex}`);
          excelUtil.duplicateCell(`${_.get(item, 'commissionPolicy.type', '')}`, `L${rowIndex - 1}`, `L${rowIndex}`);
          excelUtil.duplicateCell(+item.commissionPolicy.rate, `M${rowIndex - 1}`, `M${rowIndex}`);

          const commissionUnit = item.employees.commissions.unit;
          if (commissionUnit == '%') {
            excelUtil.duplicateCell(+item.employees.commissions.recordedCommission, `N${rowIndex - 1}`, `N${rowIndex}`);
            excelUtil.duplicateCell('', `O${rowIndex - 1}`, `O${rowIndex}`);
          } else {
            excelUtil.duplicateCell('', `N${rowIndex - 1}`, `N${rowIndex}`);
            excelUtil.duplicateCell(+item.employees.commissions.recordedCommission, `O${rowIndex - 1}`, `O${rowIndex}`);
          }
          excelUtil.duplicateCell(+item.employees.commissions.debtCommissionRevenue, `P${rowIndex - 1}`, `P${rowIndex}`);

          // Điều chỉnh
          excelUtil.duplicateCell(``, `Q${rowIndex - 1}`, `Q${rowIndex}`);
          excelUtil.duplicateCell(``, `R${rowIndex - 1}`, `R${rowIndex}`);
          excelUtil.duplicateCell(``, `S${rowIndex - 1}`, `S${rowIndex}`);
          excelUtil.duplicateCell(``, `T${rowIndex - 1}`, `T${rowIndex}`);

          rowIndex = rowIndex + 1;
        } else { // row 8
          // Thông tin chung
          excelUtil.setCellValue(`${_.get(item, 'index', '')}`, `A${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'contract.code', '')}`, `B${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'contract.name', '')}`, `C${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'customer.code', '')}`, `D${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'customer.name', '')}`, `E${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'debtCollector.code', '')}`, `F${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'debtCollector.name', '')}`, `G${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'installmentName', '')}`, `H${rowIndex}`);

          // Thông tin hoa hồng công nợ
          excelUtil.setCellValue(`${_.get(item, 'debtage.name', '')}`, `I${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'debtType', '')}`, `J${rowIndex}`);
          excelUtil.setCellValue(+item.employees.commissions.debtRevenue, `K${rowIndex}`);
          excelUtil.setCellValue(`${_.get(item, 'commissionPolicy.type', '')}`, `L${rowIndex}`);
          excelUtil.setCellValue(+item.commissionPolicy.rate, `M${rowIndex}`);

          const commissionUnit = item.employees.commissions.unit;
          if (commissionUnit == '%') {
            excelUtil.setCellValue(+item.employees.commissions.recordedCommission, `N${rowIndex}`);
            excelUtil.setCellValue('', `O${rowIndex}`);
          } else {
            excelUtil.setCellValue('', `N${rowIndex}`);
            excelUtil.setCellValue(+item.employees.commissions.recordedCommission, `O${rowIndex}`);
          }
          excelUtil.setCellValue(+item.employees.commissions.debtCommissionRevenue, `P${rowIndex}`);

          // Điều chỉnh
          excelUtil.setCellValue(``, `Q${rowIndex}`);
          excelUtil.setCellValue(``, `R${rowIndex}`);
          excelUtil.setCellValue(``, `S${rowIndex}`);
          excelUtil.setCellValue(``, `T${rowIndex}`);

          rowIndex = rowIndex + 1;
        }
      });
      const nodebuffer = await excelUtil.getbuffer();
      await fs.writeFileAsync(path.resolve(this.staticAssetService.getUploadFolderPath(), file), nodebuffer, async function (err) {
        if (err) {
          return console.log(err);
        }
        return;
      });
    });
  }
}
