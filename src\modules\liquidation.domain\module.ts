import { CqrsModule } from "@nestjs/cqrs";
import { Module } from "@nestjs/common";
import { LiquidationDomainCommandHandlers } from "./commands/handlers";
import { LiquidationDomainEventHandlers } from "./events";
import { LiquidationDomainSagas } from "./sagas/liquidation.domain-sagas";
import { LiquidationDomainController } from "./controller";
import { LiquidationDomainService } from "./service";
import { LiquidationEventRepository } from "./repository/liquidation.event.repository";
import { DomainDatabaseModule } from "../database/domain/domain.database.module";
import { CqrsProviders } from "./providers/cqrs.domain.providers";
import { LiquidationQuerySideModule } from "../liquidation.queryside/module";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { LoggerModule } from "../logger/logger.module";
import { CodeGenerateModule } from "../code-generate/module";
import { PrimaryContractQuerySideModule } from "../primary-contract.queryside/module";
import { PrimaryContractDomainModule } from "../primary-contract.domain/module";
import { ProposalQuerySideModule } from "../proposal.queryside/module";
import { PrimaryContractModule } from "../primary-contract/module";

@Module({
  imports: [
    CqrsModule,
    DomainDatabaseModule,
    LiquidationQuerySideModule,
    MgsSenderModule,
    CodeGenerateModule,
    LoggerModule,
    PrimaryContractModule,
    PrimaryContractQuerySideModule,
    PrimaryContractDomainModule,
    ProposalQuerySideModule,
  ],
  controllers: [LiquidationDomainController],
  providers: [
    LiquidationDomainService,
    LiquidationEventRepository,
    LiquidationDomainSagas,
    ...CqrsProviders,
    ...LiquidationDomainCommandHandlers,
    ...LiquidationDomainEventHandlers,
  ],
  exports:[
    LiquidationDomainService
  ]
})
export class LiquidationDomainModule {}
