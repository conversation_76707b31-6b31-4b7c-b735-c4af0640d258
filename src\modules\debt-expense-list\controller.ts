import {
  Controller,
  Get,
  UseGuards,
  Query,
  Param,
  BadRequestException,
  Res,
  Body,
  Post,
  Put,
  ValidationPipe,
} from '@nestjs/common';
import { PermissionEnum } from '../shared/enum/permission.enum';
import {
  ApiUseTags,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard, RoleGuard, Roles, User } from '../../../shared-modules';
import { DebtExpenseListService } from './service';
import {
  CalcDebtExpenseListDto,
  CreateDebtExpenseListDto,
  UpdateDebtExpenseListDto,
} from './dto/debt-expense-list.dto';

@ApiBearerAuth()
@ApiUseTags('v1/debt-expense-list')
@Controller('v1/debt-expense-list')
@UseGuards(JwtAuthGuard)
export class DebtExpenseListController {
  constructor(private readonly service: DebtExpenseListService) {}

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.GET_ALL_DEBT_EXPENSE_LIST)
  @Get()
  findAll(@Query() query: any): Promise<any> {
    if (!query.debtCommissionId) {
      throw new BadRequestException('debtCommissionId is required');
    }
    return this.service.listAll(query);
  }

  @Get('/downloadListTransaction')
  async downloadListTransaction(@Query() query: any, @Res() res) {
    const fileName = new Date().getTime();
    await this.service.downloadListTransaction(fileName, query, res);
    return { message: 'File download started successfully' };
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.CREATE_DEBT_EXPENSE_LIST)
  @Post()
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
  })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async createDebtExpenseList(
    @User() userLogged,
    @Body(new ValidationPipe()) dto: CreateDebtExpenseListDto
  ) {
    return (await this.service.createDebtExpenseList(userLogged, dto)) || true;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.UPDATE_DEBT_EXPENSE_LIST)
  @Put()
  @ApiCreatedResponse({
    description: 'The record has been successfully updated.',
  })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async updateDebtExpenseList(
    @User() userLogged,
    @Body(new ValidationPipe()) dto: UpdateDebtExpenseListDto
  ) {
    if (!dto.id || (dto && dto.id && dto.id.trim().length <= 0)) {
      return {
        msg: 'InputData_something_wrong',
        err: 'id null',
        data: null,
      };
    }
    return (await this.service.updateDebtExpenseList(userLogged, dto)) || true;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.IMPORT_DEBT_EXPENSE_LIST)
  @Post('calc-data')
  async calcData(@User() userLogged, @Body() dto: CalcDebtExpenseListDto) {
    return (await this.service.calcData(userLogged, dto)) || true;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.IMPORT_DEBT_EXPENSE_LIST)
  @Post('merge-data')
  async mergeData(@User() userLogged, @Body() dto: any) {
    return (await this.service.mergeData(userLogged, dto)) || true;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PUBLISH_DEBT_EXPENSE_LIST)
  @Post('publish/:id')
  @ApiCreatedResponse({
    description: 'The record has been successfully publish.',
  })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async pushlishDebtExpenseList(@User() userLogged, @Param('id') id: string) {
    return await this.service.publishDebtExpenseList(userLogged, id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PUBLISH_DEBT_EXPENSE_LIST)
  @Post('unPublish/:id')
  @ApiCreatedResponse({
    description: 'The record has been successfully publish.',
  })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async unPushlishDebtExpenseList(@User() userLogged, @Param('id') id: string) {
    return await this.service.unPublishDebtExpenseList(userLogged, id);
  }
}
