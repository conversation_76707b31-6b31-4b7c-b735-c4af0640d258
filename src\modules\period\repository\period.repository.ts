import { Model } from "mongoose";
import { Inject, Injectable } from "@nestjs/common";
import { CommonConst } from "../../shared/constant";
import { IPeriodDocument } from "../interfaces/document.interface";

@Injectable()
export class PeriodRepository {
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IPeriodDocument>
  ) {}

  async create(readmodel): Promise<IPeriodDocument> {
    return await this.readModel
      .create(readmodel)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async deleteMany(): Promise<IPeriodDocument> {
    return await this.readModel
      .deleteMany({})
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async findAll(
    page: number,
    pageSize: number,
    query: any = {}
  ): Promise<IPeriodDocument[]> {
    query.softDelete = false;
    const conditions: any[] = [
      {
        $match: query,
      },
      {
        $sort: { modifiedDate: -1 },
      },
      {
        $facet: {
          paginatedResults: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [{ $count: "count" }],
        },
      },
    ];

    return await this.readModel
      .aggregate(conditions)
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        const total = result[0].totalCount[0]
          ? result[0].totalCount[0].count
          : 0;
        return {
          rows: result[0].paginatedResults,
          page,
          pageSize,
          total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
        };
      });
  }

  async findOne(query): Promise<IPeriodDocument> {
    query.softDelete = false;
    return await this.readModel
      .findOne(query)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async countByName(name: string): Promise<number> {
    return await this.readModel
      .countDocuments({
        name: name,
        softDelete: false,
      })
      .exec();
  }

  async updateOne(model: any) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: model,
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async delete(id: string) {
    return await this.readModel
      .deleteOne({ id })
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
}
