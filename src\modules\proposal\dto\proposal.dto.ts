import { Transform, Type } from 'class-transformer';

import { trim, toNumber } from "lodash";
import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString, registerDecorator, ValidationOptions, ValidationArguments, MaxLength, IsEnum, ArrayMaxSize, IsArray, ValidateNested, Max, MinDate, IsInt, Min, ValidatorConstraint, ValidatorConstraintInterface, Validate, ValidateIf, IsUUID, Matches, IsPositive } from "class-validator";
import { DiscountTypeEnum, DiscountTypeRealEstateEnum } from '../../shared/enum/primary-contract.enum';
import { ProposalDto } from '../../proposal.domain/dto/proposal.dto';

export enum CustomerTypeEnum {
  BUSINESS = 'business',
  INDIVIDUAL = 'individual'
}

class FileDto {
  @IsOptional()
  name: string;

  @IsOptional()
  url: string;

  @IsOptional()
  absoluteUrl: string;

  @IsOptional()
  uploadName: string;
}

export class AddressDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  code: string;

  @Transform((value) => trim(value))
  @IsOptional()
  @IsString()
  name: string;
}

export class BankDto {
  @IsOptional()
  bankCode: string;

  @IsOptional()
  bankName: string;

  @IsOptional()
  branchName: string;

  @IsOptional()
  houseBank: string;

  @IsOptional()
  id: string;
}

export class AddressObjectDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  province: AddressDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  district: AddressDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  ward: AddressDto;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  @MaxLength(255)
  address: string;
}

export class CompanyDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty({ message: "company name should not be empty" })
  @MaxLength(120, { message: "company name maximum length is 120 characters" })
  name: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressObjectDto)
  address: AddressObjectDto;
}

class CustomerTransferDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  code: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  name: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  gender: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  birthday: string;

  @IsOptional()
  onlyYear: boolean;

  @Transform((value) => trim(value))
  @IsOptional()
  @Transform((value) => value ? value.toString() : null)
  @MaxLength(4)
  birthdayYear: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  phone: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  email: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  identityType: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  identityNumber: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  identityIssueDate: Date;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  identityIssueLocation: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressObjectDto)
  rootAddress: AddressObjectDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressObjectDto)
  address: AddressObjectDto;

  @ValidateIf(o => o.type === CustomerTypeEnum.BUSINESS)
  @ValidateNested()
  @Type(() => CompanyDto)
  @IsNotEmpty({ message: 'Company information is required for business type' })
  company: CompanyDto;

  @IsOptional()
  @IsBoolean()
  useResidentialAddress: boolean;
}

export class CreateProposalDto {
  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(255)
  contractId: string;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CustomerTransferDto)
  customerTransfer: CustomerTransferDto;

  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(255)
  reasonLiquidation: string;
  
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  proposalDate: Date;

  @IsOptional()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(255)
  note: string;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(10, { message: 'files cannot upload more than 10 files' })
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];

  @IsOptional()
  customerTransferFormat: any;

  @IsOptional()
  loanType: string = "no";

  @IsOptional()
  loanBankInfo: BankDto;

  @IsOptional()
  // @MaxLength(15)
  @IsInt({ message: 'loanAmount must be an integer' })
  @IsPositive({ message: 'loanAmount must be a positive number' })
  loanAmount: number;

  @IsOptional()
  @IsNumber({}, { message: 'loanTermYear must be a number' })
  @IsPositive({ message: 'loanTermYear must be a positive number' })
  // @Matches(/^\d{1,2}\.\d$/, {
  //   message: 'loanTermYear must be a number with exactly one decimal place (e.g., 1.5, 12.3)',
  // })
  loanTermYear: number;
}

export class UpdateProposalDto extends ProposalDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  contractId: string;

  @IsOptional()
  name: string;

  @IsOptional()
  reasonLiquidation: string;

  @IsOptional()
  proposalDate: any;

  @IsOptional()
  note: string;

  @IsOptional()
  customerTransfer: any;

  @IsOptional()
  customerTransferFormat: any;

  @IsOptional()
  loanType: string = "no";

  @IsOptional()
  loanBankInfo: BankDto;

  @IsOptional()
  // @MaxLength(15)
  @IsInt({ message: 'loanAmount must be an integer' })
  @IsPositive({ message: 'loanAmount must be a positive number' })
  loanAmount: number;

  @IsOptional()
  @IsNumber({}, { message: 'loanTermYear must be a number' })
  @IsPositive({ message: 'loanTermYear must be a positive number' })
  // @Matches(/^\d{1,2}\.\d$/, {
  //   message: 'loanTermYear must be a number with exactly one decimal place (e.g., 1.5, 12.3)',
  // })
  loanTermYear: number;
}
