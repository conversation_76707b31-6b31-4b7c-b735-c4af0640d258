import { IsNotEmpty, IsString } from 'class-validator';
import { ClassBased } from '../../shared/classes/class-based';
import { IDebtCommissionList, ICommissionListContract, ICommissionListCustomer, ICommissionListPropertyUnit } from '../../shared/services/debt-commission-list/interfaces/debt-commission-list.interface';
import * as _ from "lodash";

export class DebtCommissionListDto extends ClassBased implements IDebtCommissionList {
  id: string;
  index: number;
  name: string;
  code: string; // tự gen
  installmentName: string;
  commission: object;
  project: object;
  pos: object;
  propertyUnit: ICommissionListPropertyUnit;
  customer: ICommissionListCustomer;
  contract: ICommissionListContract;
  debtCollector: object;
  debtage: object;
  description: string;

  vatRate: number;
  debtType: string;
  debtRate: number;
  debtRevenue: number;

  employees: any[];

  commissionPolicy: any;
  adjustmentData?: any[];

  constructor(init?: Partial<DebtCommissionListDto>) {
    super();
    Object.assign(this, init);
  }
}

export class CalculateBaseDebtCommissionDto extends DebtCommissionListDto {
  @IsNotEmpty()
  @IsString()
  commissionCode: string;

  @IsNotEmpty()
  @IsString()
  projectId: string;

  @IsNotEmpty()
  @IsString()
  periodFrom: string;

  @IsNotEmpty()
  @IsString()
  periodTo: string;

  @IsNotEmpty()
  @IsString()
  commissionPolicyCode: string;

  createdBy: string;
}

export class CreateDebtCommissionListDto extends DebtCommissionListDto {
  name: string;
  code: string;
}

export class UpdateDebtCommissionListDto extends DebtCommissionListDto {
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  @IsString()
  commissionCode: string;

  @IsNotEmpty()
  @IsString()
  projectId: string;

  @IsNotEmpty()
  @IsString()
  periodName: string;

  @IsNotEmpty()
  @IsString()
  periodFrom: string;

  @IsNotEmpty()
  @IsString()
  periodTo: string;

  @IsNotEmpty()
  @IsString()
  year: string;

  @IsNotEmpty()
  @IsString()
  commissionPolicyCode: string;
}
