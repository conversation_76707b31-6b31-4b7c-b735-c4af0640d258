import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { BaseRepository } from '../../../../shared-modules';

import { IDepositContractDocumentFolder } from '../interfaces/deposit-contract-document.interface';
import { CommonConst } from '../../shared/constant/common.const';

@Injectable()
export class DepositContractDocumentRepository extends BaseRepository<IDepositContractDocumentFolder> {
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly cusDoc: Model<IDepositContractDocumentFolder>
  ) {
    super(cusDoc);
  }

  async existingDocuments(depositContractId: string, type: string, baseName: string, extension: string): Promise<IDepositContractDocumentFolder[]> {
    const escapedBaseName = this.escapeRegex(baseName);
    const escapedExtension = extension.replace('.', '\\.');
  
    const regex = new RegExp(`^${escapedBaseName}( \\(\\d+\\))?${escapedExtension}$`, 'i');
  
    return await this.cusDoc.find({
      depositContractId: depositContractId,
      type: type,
      name: { $regex: regex },
      softDelete: false,
    }).sort({ name: 1 });
  }
  
  escapeRegex(str) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}
