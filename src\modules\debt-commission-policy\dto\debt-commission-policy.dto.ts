import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>num, IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Length, Max, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, MinDate, ValidateNested } from 'class-validator';
import { ClassBased } from '../../shared/classes/class-based';
import { Type } from 'class-transformer';
import { ApiModelProperty } from '@nestjs/swagger';
import { ActiveEnum } from '../../shared/enum/policies.enum';
import { DebtCalculationType } from '../../shared/enum/commission.enum';
export class PenaltyDto extends ClassBased {
    @IsNotEmpty()
    @IsString()
    name: string;
    @IsNotEmpty()
    @IsString()
    unit: string;
    @IsNotEmpty()
    @IsString()
    type: string;
    @IsNotEmpty()
    @IsNumber()
    amount: number; 
}
export class ProjectDto extends ClassBased {
    @IsNotEmpty()
    @IsString()
    id: string;
    @IsNotEmpty()
    @IsString()
    name: string;
    @IsNotEmpty()
    @IsString()
    code: string;
}
export class CommissionRateDto extends ClassBased {
    @IsNotEmpty()
    @IsNumber()
    topPrice: number;
    @IsNotEmpty()
    @IsNumber()
    bottomPrice: number;
    @IsNotEmpty()
    @IsString()
    unit: string;
    @IsNotEmpty()
    @IsString()
    recordedCommissionUnit: string;
    @IsNotEmpty()
    @IsNumber()
    recordedCommission: number;
}
export class projectDebtDto extends ClassBased {
    @IsNotEmpty()
    @IsEnum(DebtCalculationType)
    @IsString()
    type: string;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(999.99)
    rate: number;
    @IsOptional()
    isProgressive: boolean;
    @IsOptional()
    isVAT: boolean;

    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => CommissionRateDto)
    listRate: CommissionRateDto[];
}

export class DebtCommissionPolicyDto {
    id: string;
    code: string;
    reasonDelete: string;

    @IsOptional()
    description: string;
    @IsOptional()
    period: string;
    modifiedBy: any;
    createdBy: any;
    @IsOptional()
    periodName: string;
    @IsOptional()
    isInterestSupport: boolean;
    @IsNotEmpty()
    @IsString()
    @MaxLength(255)
    name: string;

    @IsNotEmpty()
    @IsNumber()
    @Min(1900)
    @Max(3000)
    year: number;

    @IsNotEmpty()
    @Type(() => Date)
    @IsDate({ message: 'periodFrom must be a valid ISO date string or timestamp' })
    periodFrom: Date;

    @IsNotEmpty()
    @Type(() => Date)
    @IsDate({ message: 'periodTo must be a valid ISO date string or timestamp' })
    periodTo: Date;

    @IsNotEmpty()
    @IsNumber()
    @IsEnum(ActiveEnum)
    isActive: number;

    @IsNotEmpty()
    @Type(() => ProjectDto)
    project: ProjectDto;

    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => PenaltyDto)
    penalty: PenaltyDto[];

    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => projectDebtDto)
    badDebt: projectDebtDto;

    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => projectDebtDto)
    projectDebt: projectDebtDto;
}


export class CreateDebtCommissionPolicyDto extends DebtCommissionPolicyDto {

}

export class UpdateDebtCommissionPolicyDto extends DebtCommissionPolicyDto {
    @IsNotEmpty()
    @IsString()
    id: string;
}
export class DeleteDebtCommissionPolicyDto {
    @IsNotEmpty()
    @IsString()
    id: string;

    @IsNotEmpty()
    @IsString()
    @MaxLength(255)
    reasonDelete: string;
    modifiedBy:string
}

export class UpdateIsActiveDebtCommissionPolicyDto {
    @IsNotEmpty()
    @IsString()
    id: string;

    @IsNotEmpty()
    @IsNumber()
    @IsEnum(ActiveEnum)
    isActive: number;

    modifiedBy:string
}