import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ClassBased } from '../../shared/classes/class-based';
import { IDebtExpenseList } from '../../shared/services/debt-expense-list/interfaces/debt-expense-list.interface';

export class DebtExpenseListDto extends ClassBased implements IDebtExpenseList {
    @IsOptional()
    id: string;
    @IsOptional()
    debtCollector: object;   
    @IsOptional()                
    code: string;  
    @IsOptional()       
    name: string;   
    @IsOptional()               
    primaryTransaction: object;          
    @IsOptional()
    policyPayment: object;  
    @IsOptional()      
    interestCalculations: object;
    @IsOptional()
    penalty: object;
    @IsOptional()
    penaltyPerContract: number;
    @IsOptional()
    description: string;
    @IsOptional()
    createdBy: string;
    @IsOptional()
    modifiedBy: string;
    @IsOptional()
    createdAt: Date;
    @IsOptional()
    updatedAt: Date;
}

export class CreateDebtExpenseListDto extends DebtExpenseListDto {
}

export class CalcDebtExpenseListDto extends DebtExpenseListDto {
    @IsNotEmpty()
    @IsString()
    debtCommissionId: string;
}

export class UpdateDebtExpenseListDto extends DebtExpenseListDto {
    @IsNotEmpty()
    id: string;
    @IsNotEmpty()
    code: string;
}
