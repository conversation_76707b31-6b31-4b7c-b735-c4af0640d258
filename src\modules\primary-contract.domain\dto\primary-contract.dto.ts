
import { IsEnum, IsOptional, IsString } from "class-validator";
import { IsNotEmpty, IsNumber } from "class-validator";
import { InterestCalculationStatusEnum } from "src/modules/shared/enum/status.enum";
import { ClassBased } from "../../shared/classes/class-based";
import { IInterestCalculation, IPrimaryContract } from "../../shared/services/primary-contract/interfaces/interface";

export class PrimaryContractDto extends ClassBased implements IPrimaryContract {
  liquidation: any;
  liquidate: Date;
  id: string;
  name: string;
  code: string;
  status: string;
  startDate: Date;
  expiredDate: Date;
  signedDate: Date;
  primaryTransactionId: string;
  policyPaymentId: string;
  policyDiscountId: string;
  policyDiscountIds: string[];

  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: any;

  interestCalculations: IInterestCalculation[];
  currency: string;
  customer2: any;
  type: string;
  transferType: string;
  description: string;
  policyPayment: any;
  reason: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean
  deposit: any;
  files: any;
  companyInformation: any;
  releaseStartDate: any;
  releaseEndDate: any;

  debtCollector: any;
  assignHistories: any;
  assignStatus: string;
  debtHistory: any;
  callRecords: any;
  notes: any;
  proposal: any;
  feeAdvanceAmount: number;
  eapStatus: string;
  interestExemptionReason: string;
  restoreinterestReason: string;
  isInterestExemption: boolean;

}
export class InterestCalculationDto extends ClassBased implements IInterestCalculation {
  id: string;
  name: string;
  title: string;
  code: string;
  status: InterestCalculationStatusEnum;
  principalAmount: number = 0;
  interestRate: number = 0;
  interestAmount: number = 0;
  interestAmountTransferred: number = 0;
  interestReductionAmount: number = 0;
  remainingAmount: number = 0;
  createdDate: Date;
  startDate: Date;
  endDate: Date;
  dayOfLatePayment: number = 0;
  installmentName: string;
  receipts: any;
  description: string;
  eapStatus: string;
  eapCode: string;
  proposal: object;
  interestReductionAmountEap: number;
  interestReductionReason: string;
}
export class DownloadInterestCalculationDto extends InterestCalculationDto {
  customerName: string;
  productCode: string;
  projectName: string;
  address: string;
  contractName: string;
  signedDate: Date;
  companyName: string;
  banks: any;
}
export class CreatePrimaryContractDto extends PrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  primaryTransactionId: string;
}
export class UpdatePrimaryContractDto extends CreatePrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  changeInstallment: any;
}
export class UpdateInterestCalculationDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  interestCalculation: InterestCalculationDto;
}
export class PrimaryContractStatusDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  @IsString()
  @IsNotEmpty()
  status: string;
  reason: string;
}
export class UpdateDepositConfirmDto {
  @IsString()
  @IsNotEmpty()
  id: string;
}
export class UpdatePrimaryContractFileDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  files: any;
}
export class UpdatePrimaryContractDeliveryDateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  deliveryDate: any;

  filesDelivery: any;
}
export class SendPrimaryContractDeliveryNotifyDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  contracts: any;
}
export class HandoverPrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  deliveryResult: any;

  @IsNotEmpty()
  deliveryItems: any;

  @IsString()
  @IsNotEmpty()
  handoverStatus: any;
  @IsOptional()
  files: any;
  @IsOptional()
  deliveryDate: any;
}
export class UpdatefeeAdvanceAmountDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  @IsNumber()
  feeAdvanceAmount: number

  @IsOptional()
  files: any;
}
export class OwnershipCertificatePrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  @IsOptional()
  deliveryResult: any;
  @IsOptional()
  itemsForEligible: any;


  @IsOptional()
  itemsCerInProcess: any;

  @IsOptional()
  itemsForCerReadyHandover: any;

  @IsString()
  @IsNotEmpty()
  cerHandoverStatus: any;
  @IsOptional()
  files: any;
  @IsOptional()
  deliveryDate: any;
}
export class UpdateShowReceiptDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  receiptId: string;

  @IsNotEmpty()
  isShowedReceipt: boolean;
}

export class UpdateManyPrimaryContract {
  lstIdPrimaryContract: any[];
  startDate: any;
  endDate: any;
}
