import { ApiModelProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  MaxLength,
  Min,
  registerDecorator,
  Validate,
  ValidateIf,
  ValidateNested,
  ValidationArguments,
  ValidationOptions,
} from "class-validator";
import { trim, toNumber } from "lodash";
import {
  DepositContractFormEnum,
  DepositContractTypeEnum,
  DistributionChannelEnum,
  DivisionEnum,
  OrgchartEnum,
} from "../../shared/enum/deposit-contract.enum";

class FileDto {
  @IsOptional()
  name: string;

  @IsOptional()
  url: string;

  @IsOptional()
  absoluteUrl: string;

  @IsOptional()
  uploadName: string;
}


export function RoundPercentValue() {
  return Transform(({ value, obj }) => {
    if (obj?.type === InstallmentTypeEnum.PERCENT && typeof value === 'number') {
      return Math.round(value);
    }
    return value;
  });
}

export function IsCurrencyOrPercentage(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isCurrencyOrPercentage",
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;

          if (obj.type === InstallmentTypeEnum.PERCENT) {
            // Giá trị phải là số
            if (typeof value !== "number" || value <= 0 || value > 100) {
              return false;
            }

            // Kiểm tra số chữ số sau dấu phẩy (tối đa 2)
            const valueString = value.toString();
            const parts = valueString.split(".");
            if (parts.length === 2 && parts[1].length > 2) {
              return false;
            }

            // Kiểm tra tổng chiều dài không quá 5 ký tự (ví dụ: 99.99)
            if (valueString.replace(".", "").length > 5) {
              return false;
            }

            return true;
          }

          if (obj.type === InstallmentTypeEnum.CURRENCY) {
            if (
              typeof value !== "number" ||
              !Number.isInteger(value) ||
              value <= 0 ||
              value.toString().length > 15
            ) {
              return false;
            }
            return true;
          }

          return false;
        },
        defaultMessage(args: ValidationArguments) {
          const obj = args.object as any;
          if (obj.type === InstallmentTypeEnum.PERCENT) {
            return "Tỉ lệ TT phải là số thập phân > 0, tối đa 2 số sau dấu phẩy, độ dài tối đa 5 ký tự";
          }
          if (obj.type === InstallmentTypeEnum.CURRENCY) {
            return "Số tiền phải là số nguyên dương > 0, tối đa 15 ký tự";
          }
          return "Giá trị không hợp lệ";
        },
      },
    });
  };
}

export class OrgchartDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  id: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  code: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  name: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  taxCode: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  email: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  bpID: string;

  @IsNotEmpty()
  @IsEnum(OrgchartEnum, {
    message: `type only one of the following values is allowed: ${Object.values(
      OrgchartEnum
    ).join(", ")}`,
  })
  type: OrgchartEnum;
}

export class BanktDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  code: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  name: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  beneficiary: string;
}

export class CreateDepositContractDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsNotEmpty()
  @Type(() => OrgchartDto)
  @ValidateNested()
  orgchart: OrgchartDto;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  orgchartPartnerId: string;

  @IsNotEmpty()
  @Type(() => BanktDto)
  bank: BanktDto;

  @IsNotEmpty()
  @IsEnum(DepositContractFormEnum, {
    message: `depositForm only one of the following values is allowed: ${Object.values(
      DepositContractFormEnum
    ).join(", ")}`,
  })
  depositForm: DepositContractFormEnum;

  @IsOptional()
  @IsInt({ message: "depositTime must be an integer" })
  @Min(1, { message: "depositTime must be greater than 0" })
  @Max(9999, { message: "depositTime must be at most 4 digits" })
  depositTime: number;

  @ValidateIf((o) => o.depositForm !== DepositContractFormEnum.NO_DEPOSIT)
  @IsNotEmpty()
  @IsInt({ message: "depositAmount must be an integer" })
  @Min(1, { message: "depositAmount must be greater than 0" })
  @Max(***************, { message: "depositAmount must be at most 15 digits" })
  depositAmount: number;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  salePolicyId: string;

  // @IsNotEmpty()
  // @IsDate()
  // @Type(() => Date)
  // startDate: Date;
  
  // @IsNotEmpty()
  // @IsDate()
  // @Type(() => Date)
  // expiredDate?: Date;

  // @IsNotEmpty()
  // @IsEnum(DepositContractTypeEnum, {
  //   message: `type only one of the following values is allowed: ${Object.values(
  //     DepositContractTypeEnum
  //   ).join(", ")}`,
  // })
  // type: DepositContractTypeEnum;

  // @Transform((value) => trim(value))
  // @IsNotEmpty()
  // @IsString()
  // @MaxLength(35)
  // POnumber: string;

  // @IsNotEmpty()
  // @IsEnum(DistributionChannelEnum, {
  //   message: `distributionChannel only one of the following values is allowed: ${Object.values(DistributionChannelEnum).filter(v => typeof v === 'number').join(", ")}`
  // })
  // distributionChannel: DistributionChannelEnum;

  // @IsNotEmpty()
  // @IsEnum(DivisionEnum, {
  //   message: `division only one of the following values is allowed: ${Object.values(DivisionEnum).filter(v => typeof v === 'number').join(", ")}`,
  // })
  // division: DivisionEnum;

  @IsOptional()
  @IsArray()
  propertyUnitIds: any[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InstallmentsDto)
  installments: InstallmentsDto[];

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(10, { message: 'files cannot upload more than 10 files' })
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}

export class AddDepositPropertyUnitDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  @IsArray()
  propertyUnitIds: any[];
}

export enum InstallmentTypeEnum {
  PERCENT = "percent",
  CURRENCY = "currency",
}

export enum ExpiredDateType {
  EXPIRED_DATE_TYPE_NUMBER = 'numberDay',
  EXPIRED_DATE_TYPE_EXACT = 'exactDay'
}

class InstallmentsDto {
  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(150)
  name: string;

  // @IsNotEmpty()
  // @Transform((value) => trim(value))
  // @IsString()
  // paymentType: string;

  @IsNotEmpty()
  @IsEnum(InstallmentTypeEnum, {
    message:
      "type only one of the following values is allowed: percent, currency",
  })
  type: InstallmentTypeEnum;

  @IsNotEmpty()
  @IsNumber({}, { message: "value must be a number" })
  @IsCurrencyOrPercentage()
  value?: number;

  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsEnum(ExpiredDateType, {
    message: `expiredDateType only one of the following values is allowed: ${Object.values(
      ExpiredDateType
    ).join(", ")}`,
  })
  expiredDateType: ExpiredDateType;

  @ValidateIf((o) => o.expiredDateType === ExpiredDateType.EXPIRED_DATE_TYPE_EXACT)
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  exactDays?: Date;

  @ValidateIf((o) => o.expiredDateType === ExpiredDateType.EXPIRED_DATE_TYPE_NUMBER)
  @IsNotEmpty()
  @IsInt({ message: "expiredDays must be an integer" })
  @Min(1, { message: "expiredDays must be greater than 0" })
  expiredDays?: number;

  @IsOptional()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(255)
  description: string;

  @IsOptional()
  convertedAmount?: number;
}

export class AddInstallmentsDto {
  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsString()
  id: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InstallmentsDto)
  installments: InstallmentsDto[];
}

export class UpdateTransactionSuccessfulDto {
  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsString()
  id: string;

  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsString()
  installmentId: string;
}

export class FindAllDepositContractQueryDto {
  @Transform((value) => toNumber(value, { default: 1, min: 1 }))
  @IsNumber()
  @IsOptional()
  page: number;

  @Transform((value) => toNumber(value, { default: 20, min: 1, max: 100 }))
  @IsNumber()
  @IsOptional()
  pageSize: number;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  search: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  projectIds: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  orgType: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  depositForm: string;
}

export class UpdateDepositContractDto {
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsNotEmpty()
  @Type(() => OrgchartDto)
  @ValidateNested()
  orgchart: OrgchartDto;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  orgchartPartnerId: string;

  @IsNotEmpty()
  @Type(() => BanktDto)
  bank: BanktDto;

  @IsNotEmpty()
  @IsEnum(DepositContractFormEnum, {
    message: `depositForm only one of the following values is allowed: ${Object.values(
      DepositContractFormEnum
    ).join(", ")}`,
  })
  depositForm: DepositContractFormEnum;

  @IsOptional()
  @IsInt({ message: "depositTime must be an integer" })
  @Min(1, { message: "depositTime must be greater than 0" })
  @Max(9999, { message: "depositTime must be at most 4 digits" })
  depositTime: number;

  @IsNotEmpty()
  @IsInt({ message: "depositAmount must be an integer" })
  @Min(1, { message: "depositAmount must be greater than 0" })
  @Max(***************, { message: "depositAmount must be at most 15 digits" })
  depositAmount: number;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  salePolicyId: string;

  // @IsNotEmpty()
  // @IsDate()
  // @Type(() => Date)
  // startDate: Date;
  
  // @IsNotEmpty()
  // @IsDate()
  // @Type(() => Date)
  // expiredDate?: Date;

  // @IsNotEmpty()
  // @IsEnum(DepositContractTypeEnum, {
  //   message: `type only one of the following values is allowed: ${Object.values(
  //     DepositContractTypeEnum
  //   ).join(", ")}`,
  // })
  // type: DepositContractTypeEnum;

  // @Transform((value) => trim(value))
  // @IsNotEmpty()
  // @IsString()
  // @MaxLength(35)
  // POnumber: string;

  // @IsNotEmpty()
  // @IsEnum(DistributionChannelEnum, {
  //   message: `distributionChannel only one of the following values is allowed: ${Object.values(DistributionChannelEnum).filter(v => typeof v === 'number').join(", ")}`
  // })
  // distributionChannel: DistributionChannelEnum;

  // @IsNotEmpty()
  // @IsEnum(DivisionEnum, {
  //   message: `division only one of the following values is allowed: ${Object.values(DivisionEnum).filter(v => typeof v === 'number').join(", ")}`,
  // })
  // division: DivisionEnum;

  @IsOptional()
  @IsArray()
  propertyUnitIds: any[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InstallmentsDto)
  installments: InstallmentsDto[];

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(10, { message: 'files cannot upload more than 10 files' })
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}