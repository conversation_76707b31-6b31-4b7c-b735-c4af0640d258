export enum DebtCommissionStatus {
  INIT = 'INIT',
  NEW = 'NEW',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  ANNOUNCED = 'ANNOUNCED',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED',
  RETURNED = 'RETURNED',
  CREATED = "CREATED",
  CONFIRMED = "CONFIRMED"
}

export enum ActiveEnum {
  ACTIVE = 1,
  INACTIVE = 2,
}

export enum HistoryType {
  DEBT_COMMISSION = 'DEBT_COMMISSION',
}

export enum DebtCalculationType {
  ALL = 'all',
  BASE = 'base',
  INTEREST = 'interest',
}

export enum DebtType {
  PROJECT_DEBT = 'PROJECT_DEBT',
  BAD_DEBT = 'BAD_DEBT',
}

export enum CommissionUnitTypeEnum {
  PERCENT = 'percent',
  CURRENCY = 'currency',
}
