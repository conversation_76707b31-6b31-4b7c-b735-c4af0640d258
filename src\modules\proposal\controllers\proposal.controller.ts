import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from "@nestjs/common";
import { ProposalService } from "../services/proposal.service";
import {
  JwtAuthGuard,
  RoleGuard,
  Roles,
  User,
} from "../../../../shared-modules";
import { PermissionEnum } from "../../shared/enum/permission.enum";
import { Action } from "../../shared/enum/action.enum";
import { CreateProposalDto, UpdateProposalDto } from "../dto/proposal.dto";
import { Request } from 'express';
import { ProposalStatusDto } from "../../proposal.domain/dto/proposal.dto";

@Controller("v2/proposal")
@UseGuards(JwtAuthGuard)
export class ProposalController {
  private actionName: string = Action.NOTIFY;
  constructor(private readonly proposalService: ProposalService) {}

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_CREATE)
  @Post()
  async createProposal(
    @User() user,
    @Body(new ValidationPipe()) dto: CreateProposalDto,
    @Req() req: Request,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    const authorization = req.headers["authorization"];
    return await this.proposalService.createProposal(
      user,
      dto,
      this.actionName,
      authorization
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_GET_ALL)
  @Get()
  findAll(@User() user, @Query() query: any) {
    return this.proposalService.getAllByQuery(query, user);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_REQUEST_APPROVED)
  @Post('requestApproveProposal')
  async requestApproveProposal(
    @User() user: any,
    @Body(new ValidationPipe()) dto: ProposalStatusDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.proposalService.requestApproveProposal(user, dto, this.actionName);
  }
  
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_APPROVED)
  @Post('approveProposal')
  async approveProposal(
    @User() user: any,
    @Body(new ValidationPipe()) dto: ProposalStatusDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.proposalService.approveProposal(user, dto, this.actionName);
  }

  @Get("/getByContract/:id")
  getProposalByContract(@Param("id") id: string) {
    return this.proposalService.findByContract(id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_GET_ID)
  @Get(":id")
  findById(@Param("id") id: string) {
    return this.proposalService.findById(id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_UPDATE)
  @Put()
  async updateProposal(
    @User() user,
    @Body(new ValidationPipe()) dto: UpdateProposalDto,
    @Req() req: Request,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    const authorization = req.headers["authorization"];
    return await this.proposalService.updateProposal(
      user,
      dto,
      this.actionName,
      authorization
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PROPOSAL_DELETE)
  @Delete(":id")
  async deleteProposal(
    @User() user,
    @Param("id") id: string,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.proposalService.deleteProposal(user, id, this.actionName);
  }
}
