export class CmdPatternConst {
  static USER_EXISTENCE_CMD = "msx-adsg.user.existence";

  static SETUP_FEATURE = "msx-adsg.feature.setup";
  static VERIFY_USER = "msx-adsm.sts.findUserByMsx";

  static SEND_REGISTER_AUTO_USER = "msx-adsg-mailer-pwdUser";
  static SEND_LOG = "msx-adsg.logger.log";
  static SEND_ERROR = "msx-adsg.logger.error";
  static EMPLOYEE_UP_POINT = "msx-adsg.employee.up.point.listener";

  static CMD_PATTERN = "msx-adsg";
  static NOTIFY = {
    INFO: CmdPatternConst.CMD_PATTERN + ".notification.info",
    ERROR: CmdPatternConst.CMD_PATTERN + ".notification.error",
  };

  //
  static CHECK_USER_EXISTENCE = "msx-adsG.user.check-email-exist";
  static CREATE_USER_FROM_AGENCY = "msx-adsg.user.create-user-from-agency";

  static LOGGER = {
    SEND_LOG: CmdPatternConst.CMD_PATTERN + ".logger.log",
    SEND_ERROR: CmdPatternConst.CMD_PATTERN + ".logger.error",
  };

  static USER = {
    SIGNED: CmdPatternConst.CMD_PATTERN + ".user.signed",
    RESET_ROLE: CmdPatternConst.CMD_PATTERN + ".user.role.reset",
    CREATE_USER_WITH_PASSWORD:
      CmdPatternConst.CMD_PATTERN + ".user.create.with.password",
    GET_ROLE: CmdPatternConst.CMD_PATTERN + ".user.get.role",
  };

  static NOTIFICATION = {
    MESSAGE: CmdPatternConst.CMD_PATTERN + ".notification.message",
    CARE: CmdPatternConst.CMD_PATTERN + ".notification.care",
    NOTIFICATION_MULTIPLE: CmdPatternConst.CMD_PATTERN + '.notification.multiple',
  };

  static MAILER = {
    CREATED_USER: CmdPatternConst.CMD_PATTERN + ".mailer.pwdUser",
    USER_FORGOT_PWD: CmdPatternConst.CMD_PATTERN + ".user.forgot.pwd",
    USER_CHANGE_PWD: CmdPatternConst.CMD_PATTERN + ".user.change.pwd",
    SEND_MAIL_DELIVERY: CmdPatternConst.CMD_PATTERN + '.primary.contract.send.mail.delivery',
  };
  static PRIMARY_CONTRACT = {
    GET_PRIMARY_CONTRACT_BY_EMPLOYEE:
      CmdPatternConst.CMD_PATTERN + ".primaryContract.by.employee",
    CREATE_CONTRACT_SYNC_ERP: CmdPatternConst.CMD_PATTERN + ".create.contract.sync.erp",
  };
  static CUSTOMER = {
    GET_INFO: CmdPatternConst.CMD_PATTERN + ".customer.get.info",
    CREATE_CUSTOMER_FROM_LIQUIDATION_CONTRACT: CmdPatternConst.CMD_PATTERN + '.create.customer.from.liquidation.contract',
    CHECK_CUSTOMER_EXIST_BY_PHONE_EMAIL: CmdPatternConst.CMD_PATTERN + '.check.customer.exist.by.phone.email',
  };
  static LISTENER = {
    PRIMARY_CONTRACT: CmdPatternConst.CMD_PATTERN + ".primaryContract.listener",
    PRIMARY_CONTRACT_ONE_BY_QUERY: CmdPatternConst.CMD_PATTERN + ".primaryContract.one.by.query",
    PRIMARY_CONTRACT_UPDATE_PATHDTT: CmdPatternConst.CMD_PATTERN + ".primaryContract.update.pathdtt",
    PRIMARY_CONTRACT_UPDATE_RECEIPT_AMOUNT: CmdPatternConst.CMD_PATTERN + ".primaryContract.update.receipt.amount",
    GET_POS_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.get.pos.by.query.listener',
    EMPLOYEE: CmdPatternConst.CMD_PATTERN + '.employee.listener',
    GET_PROJECT_BY_ID: CmdPatternConst.CMD_PATTERN + '.get.project.id',
    GET_PROJECT_BY_IDS: CmdPatternConst.CMD_PATTERN + '.get.project.ids',
    GET_PROJECT_BY_CUSTOMER_SERVICE: CmdPatternConst.CMD_PATTERN + '.get.project.customer.service',
    PRIMARY_CONTRACT_TIMEOUT: CmdPatternConst.CMD_PATTERN + ".primary.contract.timeout",
    LIST_EMPLOYEE_HAND_OVER_BUSY: CmdPatternConst.CMD_PATTERN + '.list.employee.hand.over.busy',
    PRIMARY_CONTRACT_QUERY_SEND_MAIL: CmdPatternConst.CMD_PATTERN + '.primary.contract.query',
    PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM: CmdPatternConst.CMD_PATTERN + '.primary.contract.update.deposit.confirm',
    PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM: CmdPatternConst.CMD_PATTERN + '.primary.contract.update.transfer.confirm',
    GET_PRIMARY_CONTRACT_BY_CUSTOMER: CmdPatternConst.CMD_PATTERN + '.get.primary.contract.by.customer',
    GET_TRANSACTIONS_BY_TRANSACTION_CODES: CmdPatternConst.CMD_PATTERN + '.get.transactions.by.transaction.codes',
    GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + '.primary-contract.get.customer.identities.by.project.id',
    GET_PRIMARY_CONTRACT_BY_PROPERTY_UNIT_CODE_AND_PROJECT_ID: CmdPatternConst.CMD_PATTERN + '.get.primary.contract.by.property.unit.code.and.project.id',
    GET_PRIMARY_CONTRACTS_BY_PROPERTY_UNIT_CODES_AND_PROJECT_IDS: CmdPatternConst.CMD_PATTERN + '.get.primary.contracts.by.property.unit.codes.and.project.ids',
    UPDATE_PROPERTY_UNITS: CmdPatternConst.CMD_PATTERN + '.update.property.units',
    REQUEST_APPROVE: CmdPatternConst.CMD_PATTERN + '.primary.contract.request.approve',
    SYNC_PROJECT: CmdPatternConst.CMD_PATTERN + '.sync.project',
    SYNC_PRIMARY_TRANSACTION: CmdPatternConst.CMD_PATTERN + '.sync.primary.transaction',
    GET_ALL_PROJECT_BY_USER: CmdPatternConst.CMD_PATTERN + '.property.get.all.project.by.user',
    HANDOVER_SCHEDULE_NOTIFY_CUSTOMER: '.handover.schedule.notify.customer',
    UPDATE_HISTORIES_PROPERTY_UNIT: CmdPatternConst.CMD_PATTERN + '.update.histories.property.unit',
    PRIMARY_CONTRACT_CREATE_TRANSFERRED_TICKET_LISTENER: '.primary.contract.create.transferred.ticket.listener',
    UPDATE_TRADEHISTORY_SYNC_ERP: CmdPatternConst.CMD_PATTERN + '.update.trade.history.sync.erp',
    SEARCH_CONFIG_ERP_BY_CAMPAIGN: CmdPatternConst.CMD_PATTERN + '.search.config.erp.by.campaign',
    CREATE_PAYMENT_POLICY_SYNC_ERP: CmdPatternConst.CMD_PATTERN + '.create.payment.policy.sync.erp',
    GET_HANDED_CONTRACT_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + '.get.handed.contract.by.project.id',
    CHECK_DELIVERY_SCHEDULE_END_DATE: CmdPatternConst.CMD_PATTERN + '.check.delivery.schedule.end.date',
    SEND_NOTICE_DELIVERY_BEFORE_DEADLINE: CmdPatternConst.CMD_PATTERN + '.send.notice.delivery.before.deadline',
    GET_ONE_HANDOVER_SETTING: CmdPatternConst.CMD_PATTERN + '.get.one.handover.setting',
    GET_ALL_TRANSACTION_HAS_EAPP_NUMBER: CmdPatternConst.CMD_PATTERN + '.get.all.transaction.has.eapp.number',
    SEND_SAP_LIQUIDATED_CONTRACT: CmdPatternConst.CMD_PATTERN + '.send.sap.liquidated.contract',
  };
  static SMS = {
    PRIMARY_CONTRACT_PAYMENT_REMINDER: CmdPatternConst.CMD_PATTERN + '.sms.primary.contract.payment.reminder',
    PRIMARY_CONTRACT_DELIVERY_REMINDER: CmdPatternConst.CMD_PATTERN + '.sms.primary.contract.delivery.reminder',
  }
  static  PROPERTY_PRIMARY_TRANSACTION = {
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + '.get.property-primary-transaction.id',
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_CODE: CmdPatternConst.CMD_PATTERN + '.get.property-primary-transaction.code',
    UPDATE_CONTRACT_IN_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + '.update.contract-in-transaction.id',
    REMOVE_CONTRACT_IN_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + '.remove.contract-in-transaction.id',
    LIQUIDATION_PRIMARY_TRANSACTION: CmdPatternConst.CMD_PATTERN + '.liquidation.primary.transaction',
    UPDATE_CONTRACT_IN_PROPERTY_UNIT: CmdPatternConst.CMD_PATTERN + '.update.contract.in.property.unit',
    LIQUIDATION_PROPOSAL_APPROVED: CmdPatternConst.CMD_PATTERN + '.liquidation.proposal.approved',
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.get.property-primary-transaction.by.query',
    UPDATE_CUSTOMER_FROM_CONTRACT: CmdPatternConst.CMD_PATTERN + '.update.customer.from.contract',
  };
  static PROJECT = {
    GET_PROJECT_BY_ACCOUNTANT: CmdPatternConst.CMD_PATTERN + '.get.project.accountant',
    RATING: CmdPatternConst.CMD_PATTERN + '.project.rating',
    GET_PROJECT_BY_ID_FPT: CmdPatternConst.CMD_PATTERN + '.property.get.project.by.id.fpt',
  };
  static TRANSACTION = {
    CLONE_RECEIPTS: CmdPatternConst.CMD_PATTERN + '.clone.receipts'
  };
  static CARE = {
    GET_CUSTOMER_BY_ID: CmdPatternConst.CMD_PATTERN + '.care.get.customer.by.id',
    GET_CUSTOMER_BY_IDENTITY: CmdPatternConst.CMD_PATTERN + ".care.get.customer.by.identity",
    GET_TRANSACTION: CmdPatternConst.CMD_PATTERN + ".care.get.transaction.by.contract",
    CHECK_EXISTENT_CUSTOMER: CmdPatternConst.CMD_PATTERN + 'care.check.existent.customer',
    CREATE_USER_CARE_AUTO: CmdPatternConst.CMD_PATTERN + ".user.create.care.auto",
    ACTIVE_CUSTOMER: CmdPatternConst.CMD_PATTERN + ".care.active.customer"
  }

  static EMPLOYEE = {
    EMPLOYEE_GET_BY_ID: CmdPatternConst.CMD_PATTERN + '.employee.get.by.id',
    GET_EMPLOYEE_BY_INTERNAL_ORG_CODE: CmdPatternConst.CMD_PATTERN + '.get.employee.by.internal_orgchart.code.listener'
  }

  static SALES_PROGRAM = {
    GET_SALES_PROGRAM_BY_ID: CmdPatternConst.CMD_PATTERN + '.get.sales.program.id',
  };

  static ORGCHART = {
    GET_TEMPLATE_DEPT_REPORT_FILE_BY_POS_ID: CmdPatternConst.CMD_PATTERN + '.get.template.debt.report.file.by.pos.id',
    GET_BY_ID: CmdPatternConst.CMD_PATTERN + '.orgchart.get.by.id',
    GET_ALL_ORGCHART_EXTERNAL_BY_IDS: CmdPatternConst.CMD_PATTERN + '.get.all.orgchart.external.by.ids',
    GET_EXTERNAL_EMPLOYEE_BY_PARTNERSHIPCODES: CmdPatternConst.CMD_PATTERN + '.get.external.employee.by.partnershipcodes',
    GET_ORGCHART_BY_ID: CmdPatternConst.CMD_PATTERN + '.get.orgchart.by.id',
  }

  static UPLOAD = {
    STREAMBUFFER: CmdPatternConst.CMD_PATTERN + '.uploader.streambuffer'
  }

  static SYNC_ERP = {
    GET_CAMPAIGN_ERP_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + '.get.campaign.erp.by.project.id',
    SEND_REQUEST_TO_ERP: CmdPatternConst.CMD_PATTERN + '.send.request.to.erp',
}

  static PROPERTY = {
    GET_PROPERTY_UNIT_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + '.get.property.unit.by.project.id',
    FIND_ONE_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.find.one.property.unit.by.query',
    UPDATE_ONE_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.update.one.property.unit.by.query',
    UPDATE_MANY_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + '.update.many.property.unit.by.query',
    LIQUIDATE_PROPERTY_UNIT_ERP: CmdPatternConst.CMD_PATTERN + '.liquidate.property.unit.erp',
    PROPERTY_DEPOSIT_CONTRACT_UPDATE_QUEUE: CmdPatternConst.CMD_PATTERN + '.property.deposit.contract.update.queue',
    GET_PROPERTY_UNIT_BY_IDS: CmdPatternConst.CMD_PATTERN + '.get.property.unit.by.ids'
  }

  static SOCIAL = {
    CREATE_MARKET_PLACE_GROUP: CmdPatternConst.CMD_PATTERN + '.social.create.market.place.group',
  }

   static SALE_POLICY = {
        GET: CmdPatternConst.CMD_PATTERN + '.sale.policy.get',
    };

  static DEMAND = {
    GET_BY_CUSTOMER_ID: CmdPatternConst.CMD_PATTERN + '.demand.get.customer.id',
    GET_BY_CUSTOMER_CODE_SYNC_ERP: CmdPatternConst.CMD_PATTERN + '.demand.get.by.customer.code.sync.erp',
    GET_CUSTOMER_BY_CUSTOMER_CODE: CmdPatternConst.CMD_PATTERN + '.demand.get.by.customer.code',
  };
}
