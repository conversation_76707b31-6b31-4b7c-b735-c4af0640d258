import { Module } from '@nestjs/common';
import { DepositContractService } from './service/deposit-contract.service';
import { DepositContractRepository } from './repository/deposit-contract.repository';
import { DepositContractController } from './controller/deposit-contract.controller';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { SharedModule } from '../../../shared-modules';
import { DepositContractProviders } from './providers/deposit-contract.providers';
import { CodeGenerateModule } from '../code-generate/module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';

@Module({
  imports: [QueryDatabaseModule, SharedModule, CodeGenerateModule, MgsSenderModule],
  controllers: [DepositContractController],
  providers: [DepositContractService, DepositContractRepository, ...DepositContractProviders],
  exports: [DepositContractService, DepositContractRepository],
})
export class DepositContractModule { }
