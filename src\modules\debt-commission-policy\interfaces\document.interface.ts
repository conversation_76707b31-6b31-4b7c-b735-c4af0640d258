import { Document } from 'mongoose';

export interface IDebtCommissionPolicyDocument extends Document {
  _id: string;
  id: string;
  code: string;
  name: string;

  modifiedBy?: any;
  modifiedDate?: Date;
  createdBy?: any;
  createdDate?: Date;

  reasonDelete?: string;
  softDelete?: boolean;
  isInterestSupport?: boolean;

  period?: string;
  year: number;
  periodFrom: Date;
  periodTo: Date;
  periodName: string;
  isActive: number;

  project: any;
  penalty: any;
  projectDebt: any;
  badDebt: any;

  eappNumber: string;
  status: string;
  eappUrl: string;
}
