import { Body, Controller, Delete, Get, Injectable, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import {
  JwtAuthGuard,
  RoleGuard,
  User
} from "../../../shared-modules";
import { DepositContractDocumentService } from './service';
import { AddItemDepositContractDocumentDto, CreateDepositContractDocumentDto, UpdateDepositContractDocumentDto } from './dto/deposit-contract-document.dto';

@Injectable()
@Controller('v2/deposit-contract-document')
@UseGuards(JwtAuthGuard)
export class DepositContractDocumentController {
  constructor(private readonly DepositContractDocumentService: DepositContractDocumentService) {
  }

  @Get('/item/:documentId')
  async getItemDepositContractDocument(@Param('documentId') id: string) {
    return await this.DepositContractDocumentService.getItemDepositContractDocument(id);
  }

  @Put('/addItem')
  async addItemDepositContractDocument(@Body() dto: AddItemDepositContractDocumentDto, @User() user) {
    return await this.DepositContractDocumentService.addItemDepositContractDocument(dto.id, dto, user);
  }

  @Delete('/deleteItem/:documentId/:itemId')
  async deleteItemDepositContractDocument(@Param('itemId') itemId: string, @Param('documentId') documentId: string, @User() user) {
    return await this.DepositContractDocumentService.deleteItemDepositContractDocument(documentId, itemId, user);
  }

  @Get(':depositContractId')
  async getDepositContractDocument(@Param('depositContractId') depositContractId: string) {
    return await this.DepositContractDocumentService.getDepositContractDocument(depositContractId);
  }

  @Post('')
  async createDepositContractDocument(@Body() dto: CreateDepositContractDocumentDto, @User() user) {
    return await this.DepositContractDocumentService.createDepositContractDocument(dto, user);
  }

  @Put('')
  async updateDepositContractDocument(@Body() dto: UpdateDepositContractDocumentDto, @User() user) {
    return await this.DepositContractDocumentService.updateDepositContractDocument(dto.depositContractId, dto.id, dto, user);
  }

  @Delete(':documentId')
  async deleteDepositContractDocument(@Param('documentId') documentId: string, @Param('id') id: string, @User() user) {
    console.log('documentId', documentId);
    return await this.DepositContractDocumentService.deleteDepositContractDocument(documentId, user);
  }
}