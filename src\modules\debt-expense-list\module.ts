import { CqrsModule } from '@nestjs/cqrs';
import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LoggerModule } from '../logger/logger.module';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { FileGenerationService } from './file-generation.service';
import { StaticAssetModule } from '../config/static-asset.module';
import { HistoryImportQuerySideModule } from '../import-history.queryside/module';
import { DomainDatabaseModule } from '../database/domain/domain.database.module';
import { DebtCommissionModule } from '../debt-commission/module';
import { DebtExpenseListRepository } from './repository/repository';
import { DebtExpenseListService } from './service';
import { DebtExpenseListProviders } from './providers/debt-expense-list.providers';
import { PrimaryContractModule } from '../primary-contract/module';
import { DebtCommissionPolicyModule } from '../debt-commission-policy/module';
import { DebtExpenseListController } from './controller';

@Module({
  imports: [
    QueryDatabaseModule,
    CqrsModule,
    AuthModule,
    MgsSenderModule,
    LoggerModule,
    StaticAssetModule,
    HistoryImportQuerySideModule,
    CqrsModule,
    DomainDatabaseModule,
    DebtExpenseListModule,
    MgsSenderModule,
    LoggerModule,
    HistoryImportQuerySideModule,
    DebtCommissionModule,
    StaticAssetModule,
    PrimaryContractModule,
    DebtCommissionPolicyModule
  ],
  controllers: [DebtExpenseListController],
  providers: [
    DebtExpenseListRepository,
    FileGenerationService,
    DebtExpenseListService,
    ...DebtExpenseListProviders,
  ],
  exports: [DebtExpenseListRepository, DebtExpenseListService],
})
export class DebtExpenseListModule {}
