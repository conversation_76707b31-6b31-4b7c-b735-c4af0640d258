import { Modu<PERSON> } from "@nestjs/common";
import { ListenerController } from "./listener.controller";
import { PrimaryContractQuerySideModule } from "../primary-contract.queryside/module";
import { PrimaryContractDomainModule } from '../primary-contract.domain/module';
import { EmployeeQuerySideModule } from "../employee.query/module";
import { LoggerModule } from "../logger/logger.module";
import {LiquidationDomainModule} from "../liquidation.domain/module";
import { HandoverScheduleQuerySideModule } from "../handover-schedule.queryside/module";
import {StatushistoryModule} from "../statusHistory/module";
import {PolicyQuerySideModule} from "../policy.queryside/module";
import {ScheduleQuerySideModule} from "../schedule.queryside/module";
import { HandoverQuerySideModule } from "../handover.queryside/module";
import { HandoverScheduleDomainModule } from "../handover-schedule.domain/module";
import { OwnershipCertificateQuerySideModule } from "../ownership-certificate/module";
import { PrimaryContractModule } from "../primary-contract/module";
import { DebtCommissionPolicyModule } from "../debt-commission-policy/module";

@Module({
  imports: [
    PrimaryContractDomainModule,
    PrimaryContractQuerySideModule,
    EmployeeQuerySideModule,
    LoggerModule,
    LiquidationDomainModule,
    OwnershipCertificateQuerySideModule,
    HandoverScheduleQuerySideModule,
    StatushistoryModule,
    PolicyQuerySideModule,
    ScheduleQuerySideModule,
    HandoverScheduleDomainModule,
    HandoverQuerySideModule,
    PrimaryContractModule,
    DebtCommissionPolicyModule
  ],
  controllers: [ListenerController],
})
export class ListenerModule {}
