import { <PERSON><PERSON><PERSON>y, IsIn, IsOptional, IsPositive, IsString, <PERSON>, <PERSON><PERSON>eng<PERSON>, registerDecorator, Validate, ValidateIf, ValidationArguments, ValidationOptions, ValidatorConstraint, ValidatorConstraintInterface } from "class-validator";
import { IsNotEmpty } from "class-validator";
import { InterestCalculationStatusEnum } from "src/modules/shared/enum/status.enum";
import { ClassBased } from "../../shared/classes/class-based";
import {
  IInterestCalculation,
  IPrimaryContract,
} from "../../shared/services/primary-contract/interfaces/interface";

export class PrimaryContractDto extends ClassBased implements IPrimaryContract {
  @IsOptional()
  liquidation: any;
  @IsOptional()
  liquidate: Date;
  @IsOptional()
  id: string;
  @IsOptional()
  name: string;
  @IsOptional()
  code: string;
  @IsOptional()
  status: string;
  @IsOptional()
  @IsBefore('expiredDate', {
    message: 'startDate must not be after expiredDate',
  })
  startDate: Date;
  @IsOptional()
  expiredDate: Date;
  @IsOptional()
  signedDate: Date;
  @IsOptional()
  primaryTransactionId: string;
  @IsOptional()
  policyPaymentId: string;
  @IsOptional()
  policyDiscountId: string;
  @IsOptional()
  policyDiscountIds: string[];
  @IsOptional()
  salesPolicy: any;
  @IsOptional()
  staffsInvolved: any;

  @IsOptional()
  calcCurrencyFirst: boolean;
  @IsOptional()
  calcPriceVat: boolean;
  @IsOptional()
  calcContractPrice: boolean;
  @IsOptional()
  maintenanceFee: any;

  @IsOptional()
  interestCalculations: IInterestCalculation[];
  @IsOptional()
  currency: string;
  @IsOptional()
  customer2: any;
  @IsOptional()
  type: string;
  @IsOptional()
  transferType: string;
  @IsOptional()
  description: string;
  @IsOptional()
  policyPayment: any;
  @IsOptional()
  reason: string;
  @IsOptional()
  isDebtRemind: boolean;
  @IsOptional()
  isShowedInstallment: boolean;
  @IsOptional()
  deposit: any;
  @IsOptional()
  files: any;
  @IsOptional()
  companyInformation: any;
  @IsOptional()
  @IsBefore('releaseEndDate', {
    message: 'releaseStartDate must not be after releaseEndDate',
  })
  releaseStartDate: any;
  @IsOptional()
  releaseEndDate: any;

  @IsOptional()
  debtCollector: any;

  @IsOptional()
  assignHistories: any;

  @IsOptional()
  assignStatus: string;

  @IsOptional()
  debtHistory: any;

  @IsOptional()
  callRecords: any;

  @IsOptional()
  notes: any;

  @IsOptional()
  isAddCoOwnerShipInfo: boolean;

  @IsOptional()
  isAddCompanyInfo: boolean;

  eapStatus: string;
  interestExemptionReason: string;
  restoreinterestReason: string;
  isInterestExemption: boolean;

  @IsOptional()
  poNumber: string;

  @IsOptional()
  sapCode: string;

  @IsOptional()
  businessArea: any;

  @IsOptional()
  distributionChannel: any;

  @IsOptional()
  productCategory: any;

  @IsOptional()
  discountValue: number;

  @IsOptional()
  issuedPrice: number;

  @IsOptional()
  productPrice: number;

  @IsOptional()
  @IsIn(['yes', 'no'], { message: 'loanType must be either "yes" or "no"' })
  loanType: string;

  @IsOptional()
  loanBankInfo: any;

  @IsOptional()
  loanAmount: number;

  @IsOptional()
  loanTermYear: number;
}
export class InterestCalculationDto
  extends ClassBased
  implements IInterestCalculation {
  id: string;
  name: string;
  title: string;
  code: string;
  status: InterestCalculationStatusEnum;
  principalAmount: number = 0;
  interestRate: number = 0;
  interestAmount: number = 0;
  interestAmountTransferred: number = 0;
  interestReductionAmount: number = 0;
  remainingAmount: number = 0;
  createdDate: Date;
  startDate: Date;
  endDate: Date;
  dayOfLatePayment: number = 0;
  needTransfer: number;
  installmentName: string;
  receipts: any;
  description: string;
  debtage: any;
  delayFrom: Date;
  delayTo: Date;
  totalDelayDate: number;
  latePaymentFee: number;
  totalSettlementAmount: number;
  eapStatus: string;
  interestReductionAmountEap: number;
  interestReductionReason: string;
  debtType: string;
  eapCode: string;
  isStartCalcInterest: boolean;
  custommer: object;
  proposal: object;
}
export class DownloadInterestCalculationDto extends InterestCalculationDto {
  customerName: string;
  productCode: string;
  projectName: string;
  address: string;
  contractName: string;
  signedDate: Date;
  companyName: string;
  banks: any;
}
export class CreatePrimaryContractDto extends PrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  primaryTransactionId: string;

  @IsNotEmpty()
  salesPolicy: any;

  @IsNotEmpty()
  staffsInvolved: any;

  @IsOptional()
  changeInstallment: any;

  @IsOptional()
  poNumber: string;

  @IsNotEmpty()
  businessArea: any;

  @IsNotEmpty()
  distributionChannel: any;

  @IsNotEmpty()
  productCategory: any;

  @IsOptional()
  @IsIn(['yes', 'no'], { message: 'loanType must be either "yes" or "no"' })
  loanType: string;

  @IsOptional()
  loanBankInfo: any;

  @IsOptional()
  @ValidateIf((object, value) => value !== null && value !== undefined)
  @IsPositive({ message: 'Loan amount must be greater than 0' })
  @Max(***************, { message: 'Loan amount cannot exceed 15 digits' })
  loanAmount: number;

  @IsOptional()
  @ValidateIf((object, value) => value !== null && value !== undefined)
  @IsPositive({ message: 'loanTermYear must be greater than 0' })
  @Max(9999, { message: 'loanTermYear cannot exceed 4 digits' })
  loanTermYear: number;
}
export class UpdatePrimaryContractDto extends CreatePrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  @IsOptional()
  changeInstallment: any;
}
export class UpdateInterestCalculationDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  
  @IsNotEmpty()
  interestCalculation: InterestCalculationDto;
}
export class PrimaryContractStatusDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  @IsString()
  @IsNotEmpty()
  status: string;
  reason: string;
}
export class UpdateDepositConfirmDto {
  @IsString()
  @IsNotEmpty()
  id: string;
}
export class UpdatePrimaryContractFileDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  files: any;
}
export class UpdatePrimaryContractDeliveryDateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  deliveryDate: any;

  filesDelivery: any;
}
export class SendPrimaryContractDeliveryNotifyDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  contracts: any;
}

export class HandoverPrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  deliveryResult: any;

  @IsNotEmpty()
  deliveryItems: any;

  @IsString()
  @IsNotEmpty()
  handoverStatus: any;

  files: any;
  deliveryDate: any;
}

export class UpdateShowReceiptDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  receiptId: string;

  @IsNotEmpty()
  isShowedReceipt: boolean;
}

export class UpdateManyPrimaryContract {
  @IsNotEmpty()
  lstIdPrimaryContract: any[];
  @IsNotEmpty()
  startDate: any;
  @IsNotEmpty()
  endDate: any;
}

export class AssignDebtCollectorDto {
  @IsArray()
  @IsNotEmpty()
  contractIds: string[];

  @IsNotEmpty()
  employee: string;
}

export class DeassignDebtCollectorDto {
  @IsArray()
  @IsNotEmpty()
  contractIds: string[];
}

export class ResendEmailDto {
  @IsString()
  @IsNotEmpty()
  id: string; // contract Id

  @IsOptional()
  customerCodes: string[];
}

export class InteractionDto {
  @IsString()
  @IsNotEmpty()
  id: string; // contract Id

  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  title: string;

  @IsString()
  @IsNotEmpty()
  detail: string;
}

export function IsBefore(property: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isBefore',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];

          if (!value || !relatedValue) return true;

          const d1 = new Date(value);
          const d2 = new Date(relatedValue);

          return d1 <= d2;
        },
        defaultMessage(args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          return `${args.property} must be before or equal to ${relatedPropertyName}`;
        },
      },
    });
  };
}

export function IsAfterNow(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isAfterNow',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {

          const d1 = new Date(value);
          const d2 = new Date();

          return d1 >= d2;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be after or equal to now`;
        },
      },
    });
  };
}