import { BaseServiceRepository, CmdPatternConst, IListQueryInterface } from '../../../../shared-modules'
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { isEmpty } from "lodash";
import { IPolicyDocument } from '../../policy.queryside/interfaces/document.interface';
import { PolicyRepository } from '../repositories/policy.repository';
import { IPolicy } from '../../shared/services/policy/interfaces/interface';
import { CodeGenerateService } from "../../../modules/code-generate/service";
import { CommonConst } from '../../shared/constant';
import { PropertyClient } from '../../mgs-sender/property.client';
import { CreateDiscountPolicyDto, CreatePaymentPolicyDto, DeletePolicyDto, ExpiredDateType, FindAllDiscountPolicyQueryDto, FindAllPaymentPolicyQueryDto, OutputFile, PolicyProposalDto, PreviewFormDto, ProposalTypeEnum, TicketEappStatusEnum, UpdateDiscountPolicyDto, UpdatePaymentPolicyDto, UpdateStatusDto } from '../dto/policy.dto';
import { PolicyTypeEnum, StatusPolicyFPTEnum } from '../../shared/enum/primary-contract.enum';
import { StatusPolicy, TicketStatus } from '../../shared/constant/data.const';
import axios from 'axios';
import { ProposalClient } from '../../mgs-sender/proposal.client';
import * as Bluebird from "bluebird";
import * as path from 'path';
import PizZip from 'pizzip';
import Docxtemplater from 'docxtemplater';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from "@nestjs/config";
import moment = require("moment");
import { CustomHttpService } from '../../../shared-modules/http/http.service';
import * as querystring from 'querystring';

const FormData = require('form-data');
const fs = Bluebird.promisifyAll(require("fs"));
const ImageModule = require('docxtemplater-image-module-free');
import * as AWS from 'aws-sdk';
import { PrimaryContractService } from '../../primary-contract/service';
const sizeOf = require('image-size');
@Injectable()
export class PolicyService extends BaseServiceRepository<IPolicyDocument> {
  private s3: AWS.S3;
  constructor(
    private readonly codeGenerateService: CodeGenerateService,
    private readonly propertyClient: PropertyClient,
    private readonly policyRepository: PolicyRepository,
    private readonly proposalClient: ProposalClient,
    private readonly configService: ConfigService,
    private readonly primaryContractService: PrimaryContractService,
    private readonly httpService: CustomHttpService
  ) {
    super(policyRepository); // Gọi BaseService với EmployeeRepository
    this.s3 = new AWS.S3({
      accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      region: this.configService.get('AWS_DEFAULT_REGION'),
      endpoint: this.configService.get('AWS_EMDPOINT_URL'),
    });
  }

  async checkExistContract(existingPolicy: IPolicy): Promise<any> {
    let contractQuery = {};
    if (existingPolicy.type === PolicyTypeEnum.PAYMENT) {
      contractQuery = { policyPayment: existingPolicy.id };
    } else if (existingPolicy.type === PolicyTypeEnum.DISCOUNT) {
      contractQuery = { policyDiscounts: existingPolicy.id };
    }
    const existingContract = Object.keys(contractQuery).length
      ? await this.primaryContractService.getContractByQuery(contractQuery)
      : [];

    if (existingContract && existingContract.length > 0) {
      throw new HttpException({ errorCode: 'POLI0014' }, HttpStatus.OK);
    }
  }

  async checkExpiredDate(dto: any,existingPolicy: IPolicy) {
    if (dto.expiredDate) {
      const expiredDate = new Date(dto.expiredDate).toISOString().split("T")[0];
      const now = new Date().toISOString().split("T")[0];
      if (expiredDate < now) {
        throw new HttpException({
          errorCode: 'POLI0013',
        }, HttpStatus.OK);
      }
      existingPolicy.expiredDate = dto.expiredDate;
    }else{
      if (existingPolicy.expiredDate < new Date() && !existingPolicy.active) {
        throw new HttpException({
          errorCode: 'POLI0013',
        }, HttpStatus.OK);
      }
    }
  }

  convertStatus(status: string, active: boolean) {
    const newStatus = active && status === StatusPolicyFPTEnum.APPROVED_ACTIVE
      ? StatusPolicyFPTEnum.APPROVED_INACTIVE
      : !active && status === StatusPolicyFPTEnum.APPROVED_INACTIVE
        ? StatusPolicyFPTEnum.APPROVED_ACTIVE
        : status;
    return newStatus;
  }

  async updateStatus(id: string, user: any, dto: UpdateStatusDto): Promise<IPolicy> {
    const existingPolicy = await super.findOne({ id, softDelete: false });
    if (!existingPolicy) {
      throw new HttpException({ errorCode: 'POLI0001' }, HttpStatus.NOT_FOUND);
    }

    if (existingPolicy.active) await this.checkExistContract(existingPolicy);

    if (!existingPolicy.active) await this.checkExpiredDate(dto, existingPolicy);
    const newStatus = await this.convertStatus(existingPolicy.status, existingPolicy.active);
    return await super.update(id, {
      ...existingPolicy,
      active: !existingPolicy.active,
      status: newStatus,
      modifiedBy: user.id,
      modifiedDate: new Date()
    });
  }

  getFileExtension(filename) {
    if (!filename || typeof filename !== 'string') {
      return '';
    }

    const lastDotIndex = filename.lastIndexOf('.');

    if (lastDotIndex === -1) {
      return '';
    }

    return filename.slice(lastDotIndex);
  }

  convertStringInputToArrayDto(val) {
    if (val && typeof val === 'string') {
      return val.split(',').map(item => item.trim());
    }
    return [];
  }

  async createDiscountPolicy(user: any, policy: CreateDiscountPolicyDto): Promise<IPolicy> {
    await super.isExistThrowError({ name: policy.name, type: PolicyTypeEnum.DISCOUNT, softDelete: false }, 'POLI0002');
    const getProject = await this.propertyClient.sendDataPromise({ id: policy.project.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT);
    if (!getProject) {
      throw new HttpException({ errorCode: 'PRJE0002' }, HttpStatus.OK);
    }
    return await super.create({
      ...policy,
      code: await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.POLICY.NAME, CommonConst.AGGREGATES.POLICY.CODE_PREFIX_DISCOUNT),
      createdBy: user.id, type: PolicyTypeEnum.DISCOUNT,
      eappNumber: null, approvedDate: null, urlEapp: null, ticketStatus: null, proposalId: null
    });
  }

  async updateDiscountPolicy(id: string, user: any, policy: UpdateDiscountPolicyDto): Promise<IPolicy> {
    const existingPolicy = await this.policyRepository.findOne({ id, softDelete: false });
    if (!existingPolicy) {
      throw new HttpException({ errorCode: 'POLI0001' }, HttpStatus.NOT_FOUND);
    }
    if (existingPolicy.name !== policy.name) {
      await super.isExistThrowError({ name: policy.name, type: PolicyTypeEnum.DISCOUNT }, 'POLI0002');
    }
    let newStatus = existingPolicy.status;
    if (policy.active !== existingPolicy.active) {
      if (existingPolicy.active) await this.checkExistContract(existingPolicy);
      if (!existingPolicy.active) await this.checkExpiredDate(policy, existingPolicy);
      newStatus = await this.convertStatus(existingPolicy.status, existingPolicy.active);
    }
    const getProject = await this.propertyClient.sendDataPromise({ id: policy.project.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT);
    if (!getProject) {
      throw new HttpException({ errorCode: 'PRJE0002' }, HttpStatus.OK);
    }
    return await super.update(id, { ...policy, modifiedBy: user.id,status: newStatus, modifiedDate: new Date() });
  }

  async findPolicyById(id: string): Promise<IPolicy> {
    const existingPolicy = await this.policyRepository.findOne({ id, softDelete: false });
    if (!existingPolicy) {
      throw new HttpException({ errorCode: 'POLI0001' }, HttpStatus.NOT_FOUND);
    }
    const getProject = await this.propertyClient.sendDataPromise({ id: existingPolicy.project.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT);
    if (!getProject) {
      throw new HttpException({ errorCode: 'PRJE0002' }, HttpStatus.OK);
    }
    existingPolicy.project = getProject;
    return existingPolicy
  }

  async getAllDiscountPolicyByQuery(queryParams: Partial<FindAllDiscountPolicyQueryDto>): Promise<IListQueryInterface<IPolicy>> {
    const query: any = {};
    // Search conditions
    const searchConditions = [];
    if (!isEmpty(queryParams.search)) {
      const search = this.escapeRegex(queryParams.search);
      const regexSearch = new RegExp(search, 'i');
      searchConditions.push(
        { code: { $regex: regexSearch } },
        { name: { $regex: regexSearch } }
      );
    }

    // Date conditions
    const dateConditions = [];
    if (queryParams.startDate && queryParams.endDate) {
      dateConditions.push(
        {
          $and: [
            { startDate: { $gte: new Date(queryParams.startDate) } },
            { expiredDate: { $lte: new Date(queryParams.endDate) } }
          ]
        },
        {
          $and: [
            { startDate: { $gte: new Date(queryParams.startDate) } },
            { startDate: { $lte: new Date(queryParams.endDate) } },
            { expiredDate: null }
          ]
        }
      );
    } else {
      if (queryParams.startDate) {
        query.startDate = { $gte: new Date(queryParams.startDate) };
      }

      if (queryParams.endDate) {
        dateConditions.push(
          {
            $and: [
              { startDate: { $gte: new Date(0) } },
              { expiredDate: { $lte: new Date(queryParams.endDate) } }
            ]
          },
          {
            $and: [
              { startDate: { $gte: new Date(0) } },
              { startDate: { $lte: new Date(queryParams.endDate) } },
              { expiredDate: null }
            ]
          }
        );
      }
    }

    // Combine conditions
    if (searchConditions.length > 0 && dateConditions.length > 0) {
      query.$and = [
        { $or: searchConditions },
        { $or: dateConditions }
      ];
    } else if (searchConditions.length > 0) {
      query.$or = searchConditions;
    } else if (dateConditions.length > 0) {
      query.$or = dateConditions;
    }

    if (!isEmpty(queryParams.statuses)) {
      const statuses = await this.convertStringInputToArrayDto(queryParams.statuses);
      query.status = { $in: statuses };
    }

    if (!isEmpty(queryParams.projectIds)) {
      const projectIds = await this.convertStringInputToArrayDto(queryParams.projectIds);
      query['project.id'] = { $in: projectIds };
    }

    if (!isEmpty(queryParams.createdBys)) {
      const createdBys = await this.convertStringInputToArrayDto(queryParams.createdBys);
      query.createdBy = { $in: createdBys };
    }

    if (!isEmpty(queryParams.ticketStatuses)) {
      const ticketStatuses = await this.convertStringInputToArrayDto(queryParams.ticketStatuses);
      // if (ticketStatuses.includes("DELETE_DRAFT_TICKET")) {
      //   query.ticketStatus = { $in: ["DELETE_DRAFT_TICKET", null, ""] };
      // } else {
        query.ticketStatus = { $in: ticketStatuses };
      // }
    }

    if (queryParams.active) {
      query.active = queryParams.active;
    }

    query.type = PolicyTypeEnum.DISCOUNT;
    query.softDelete = false;
    const result = await super.pagination(query, queryParams.page, queryParams.pageSize, { code: -1 });

    let listAllPolicy: IPolicy[] = [];
    for (const item of result.rows) {
      if (item.project?.id) {
        const getProject = await this.propertyClient.sendDataPromise(
          { id: item.project.id },
          CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT
        );
        if (getProject) item.project = getProject;
      }
      listAllPolicy.push(item);
    }

    return {
      rows: listAllPolicy,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      totalPages: result.totalPages
    };
  }

  async createPaymentPolicy(user: any, policy: CreatePaymentPolicyDto): Promise<IPolicy> {
    await super.isExistThrowError({ name: policy.name, type: PolicyTypeEnum.PAYMENT, softDelete: false }, 'POLI0002');
    const getProject = await this.propertyClient.sendDataPromise({ id: policy.project.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT);
    if (!getProject) {
      throw new HttpException({ errorCode: 'PRJE0002' }, HttpStatus.OK);
    }
    if (policy.defaultApply) {
      const findPolicyPayment = await this.findAll({ "project.id": policy.project.id, type: PolicyTypeEnum.PAYMENT })
      if (findPolicyPayment && findPolicyPayment.length > 0) {
        await this.policyRepository.updatePolicyPaymentMany(
          policy.project.id,
          { defaultApply: false, modifiedBy: user.id, modifiedDate: new Date() },
        );
      }
    }
    const installments = policy.schedule.installments;

    const names = installments.map(item => item.name);
    const uniqueNames = new Set(names);
    if (uniqueNames.size !== names.length)
      throw new HttpException({ errorCode: 'POLI0011' }, HttpStatus.OK);
    if (installments.some(x => x.name.toLowerCase().trim() === 'khác'))
      throw new HttpException({ errorCode: 'POLI0012' }, HttpStatus.OK);

    if (installments.length > 0) {
      const firstInstallment = installments[0];

      if (installments.every(x => !x.isToContract)) {
        firstInstallment.isToContract = true;
      }

      if (installments.every(x => !x.transactionSuccessful)) {
        firstInstallment.transactionSuccessful = true;
      }
    }

    return await super.create({
      ...policy,
      code: await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.POLICY.NAME, CommonConst.AGGREGATES.POLICY.CODE_PREFIX_PAYMENT),
      createdBy: user.id, type: PolicyTypeEnum.PAYMENT,
      eappNumber: null, approvedDate: null, urlEapp: null, ticketStatus: null
    });
  }

  async updatePaymentPolicy(id: string, user: any, policy: UpdatePaymentPolicyDto): Promise<IPolicy> {
    const existingPolicy = await this.policyRepository.findOne({ id, softDelete: false });
    if (!existingPolicy) {
      throw new HttpException({ errorCode: 'POLI0001' }, HttpStatus.NOT_FOUND);
    }
    if (existingPolicy.name !== policy.name) {
      await super.isExistThrowError({ name: policy.name, type: PolicyTypeEnum.PAYMENT }, 'POLI0002');
    }
    
    let newStatus = existingPolicy.status;
    if (policy.active !== existingPolicy.active) {
      if (existingPolicy.active) await this.checkExistContract(existingPolicy);
      if (!existingPolicy.active) await this.checkExpiredDate(policy, existingPolicy);
      newStatus = await this.convertStatus(existingPolicy.status, existingPolicy.active);
    }

    const names = policy.schedule.installments.map(item => item.name);
    const uniqueNames = new Set(names);
    if (uniqueNames.size !== names.length)
      throw new HttpException({ errorCode: 'POLI0011' }, HttpStatus.OK);
    if (policy.schedule.installments.some(x => x.name.toLowerCase().trim() === 'khác'))
      throw new HttpException({ errorCode: 'POLI0012' }, HttpStatus.OK);

    let error: string[] = [];
    if (policy.schedule && policy.schedule.installments && policy.schedule.installments.length > 0) {
      policy.schedule.installments.forEach(inputInstallment => {
        if (inputInstallment.expiredDateType === ExpiredDateType.EXPIRED_DATE_TYPE_EXACT) {
          const existingInstallment = existingPolicy.schedule.installments.find(
            installment => installment.id === inputInstallment.id
          );
          if (existingInstallment) {
            if (existingInstallment.expiredDateType === inputInstallment.expiredDateType) {
              const existingExactDays = new Date(
                existingInstallment.exactDays.getFullYear(),
                existingInstallment.exactDays.getMonth(),
                existingInstallment.exactDays.getDate()
              ).getTime();

              const inputExactDays = new Date(
                inputInstallment.exactDays.getFullYear(),
                inputInstallment.exactDays.getMonth(),
                inputInstallment.exactDays.getDate()
              ).getTime();

              const currentDate = new Date(
                new Date().getFullYear(),
                new Date().getMonth(),
                new Date().getDate()
              ).getTime();

              if (existingExactDays !== inputExactDays && inputExactDays < currentDate) {
                error.push(inputInstallment.id)
              }
            }
          }
        }
      });
    }
    if (error.length > 0) {
      throw new HttpException({ errorCode: 'POLI0003' }, HttpStatus.OK);
    }
    const getProject = await this.propertyClient.sendDataPromise({ id: policy.project.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT);
    if (!getProject) {
      throw new HttpException({ errorCode: 'PRJE0002' }, HttpStatus.OK);
    }
    if (policy.defaultApply) {
      const findPolicyPayment = await this.findAll({ "project.id": policy.project.id, type: PolicyTypeEnum.PAYMENT })
      if (findPolicyPayment && findPolicyPayment.length > 0) {
        await this.policyRepository.updatePolicyPaymentMany(
          policy.project.id,
          { defaultApply: false, modifiedBy: user.id, modifiedDate: new Date() },
        );
      }
    }
    return await super.update(id, { ...policy, modifiedBy: user.id, status: newStatus, modifiedDate: new Date() });
  }


  private escapeRegex(text) {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  }

  async getAllPaymentPolicyByQuery(queryParams: Partial<FindAllPaymentPolicyQueryDto>): Promise<IListQueryInterface<IPolicy>> {
    const query: any = {};
    // Search conditions
    const searchConditions = [];
    if (!isEmpty(queryParams.search)) {
      const search = this.escapeRegex(queryParams.search);
      const regexSearch = new RegExp(search, 'i');
      searchConditions.push(
        { code: { $regex: regexSearch } },
        { name: { $regex: regexSearch } }
      );
    }

    // Date conditions
    const dateConditions = [];
    if (queryParams.startDate && queryParams.endDate) {
      dateConditions.push(
        {
          $and: [
            { startDate: { $gte: new Date(queryParams.startDate) } },
            { expiredDate: { $lte: new Date(queryParams.endDate) } }
          ]
        },
        {
          $and: [
            { startDate: { $gte: new Date(queryParams.startDate) } },
            { startDate: { $lte: new Date(queryParams.endDate) } },
            { expiredDate: null }
          ]
        }
      );
    } else {
      if (queryParams.startDate) {
        query.startDate = { $gte: new Date(queryParams.startDate) };
      }

      if (queryParams.endDate) {
        dateConditions.push(
          {
            $and: [
              { startDate: { $gte: new Date(0) } },
              { expiredDate: { $lte: new Date(queryParams.endDate) } }
            ]
          },
          {
            $and: [
              { startDate: { $gte: new Date(0) } },
              { startDate: { $lte: new Date(queryParams.endDate) } },
              { expiredDate: null }
            ]
          }
        );
      }
    }

    // Combine conditions
    if (searchConditions.length > 0 && dateConditions.length > 0) {
      query.$and = [
        { $or: searchConditions },
        { $or: dateConditions }
      ];
    } else if (searchConditions.length > 0) {
      query.$or = searchConditions;
    } else if (dateConditions.length > 0) {
      query.$or = dateConditions;
    }

    if (!isEmpty(queryParams.statuses)) {
      const statuses = await this.convertStringInputToArrayDto(queryParams.statuses);
      query.status = { $in: statuses };
    }

    if (!isEmpty(queryParams.projectIds)) {
      const projectIds = await this.convertStringInputToArrayDto(queryParams.projectIds);
      query['project.id'] = { $in: projectIds };
    }

    if (!isEmpty(queryParams.createdBys)) {
      const createdBys = await this.convertStringInputToArrayDto(queryParams.createdBys);
      query.createdBy = { $in: createdBys };
    }

    if (!isEmpty(queryParams.ticketStatuses)) {
      const ticketStatuses = await this.convertStringInputToArrayDto(queryParams.ticketStatuses);
      // if (ticketStatuses.includes("DELETE_DRAFT_TICKET")) {
      //   query.ticketStatus = { $in: ["DELETE_DRAFT_TICKET", null, ""] };
      // } else {
        query.ticketStatus = { $in: ticketStatuses };
      // }
    }
    if (queryParams.active) {
      query.active = queryParams.active;
    }
    query.type = PolicyTypeEnum.PAYMENT;
    query.softDelete = false;
    const result = await super.pagination(query, queryParams.page, queryParams.pageSize, { code: -1 });

    let listAllPolicy: IPolicy[] = [];
    for (const item of result.rows) {
      if (item.project?.id) {
        const getProject = await this.propertyClient.sendDataPromise(
          { id: item.project.id },
          CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT
        );
        if (getProject) item.project = getProject;
      }
      listAllPolicy.push(item);
    }

    return {
      rows: listAllPolicy,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      totalPages: result.totalPages
    };
  }

  async getListStatus(query?) {
    if (query.search && query.search.length > 0) {
      return StatusPolicy.filter(item => item.name.toLowerCase().includes(query.search.toLowerCase()));
    }
    return StatusPolicy
  }

  async getListTicketStatus(query?) {
    if (query.search && query.search.length > 0) {
      return TicketStatus.filter(item => item.name.toLowerCase().includes(query.search.toLowerCase()));
    }
    return TicketStatus
  }

  async getPolicyProposalNumber(id: string, user: any, headers: any) {
    const policy = await this.findPolicyById(id);
    if (policy.eappNumber) {
      throw new HttpException({ errorCode: 'POLI0005' }, HttpStatus.OK);
    }

    const response = await axios.post(
      process.env.API_GATEWAY_URL,
      { type: process.env.TYPE_PROPOSAL_GET_NUMBER, data: {} },
      {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      }
    );

    if (!response.data?.data || response.data.statusCode !== "0") {
      throw new HttpException({
        errorCode: 'POLI0004',
        message: response.data?.message || 'Invalid response'
      }, HttpStatus.OK);
    }

    return await super.update(id, {
      eappNumber: response.data.data,
      modifiedBy: user.id,
      modifiedDate: new Date()
    });
  }

  private checkTypeProposal(typeDTO: string, typePolicy: string) {

    if (typeDTO !== PolicyTypeEnum.PAYMENT && typePolicy !== PolicyTypeEnum.PAYMENT) {
      return ProposalTypeEnum.DISCOUNT_POLICY_PROPOSAL;
    }
    if (typeDTO !== PolicyTypeEnum.DISCOUNT && typePolicy !== PolicyTypeEnum.DISCOUNT) {
      return ProposalTypeEnum.PAYMENT_POLICY_PROPOSAL;
    }
    throw new HttpException({ errorCode: 'POLI0010' }, HttpStatus.OK);
  }


  private checkNumberProposal(eappNumber: string, proposalId: string, status: string) {
    if (!eappNumber) {
      throw new HttpException({ errorCode: 'POLI0009' }, HttpStatus.OK);
    }
    const invalidStatuses = [
      StatusPolicyFPTEnum.RETURNED,
      StatusPolicyFPTEnum.REJECTED,
      StatusPolicyFPTEnum.NEW,
      StatusPolicyFPTEnum.CANCELLED
    ];
    if (proposalId && !invalidStatuses.includes(status as StatusPolicyFPTEnum)) {
      throw new HttpException({ errorCode: 'POLI0008' }, HttpStatus.OK);
    }
  }

  async getProposalByQuery(query: any, headers: any) {
    if (query.type === PolicyTypeEnum.PAYMENT) {
      query.type = ProposalTypeEnum.PAYMENT_POLICY_PROPOSAL;
    } else {
      query.type = ProposalTypeEnum.DISCOUNT_POLICY_PROPOSAL;
    }
    if (query.name) {
      query.search = query.name;
    }
    const baseUrl = process.env.GET_PROPOSAL_BY_QUERY;
    const url = `${baseUrl}?${querystring.stringify(query)}`;
    const response = await axios.get(
      url,
      {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      }
    );
    return response.data.data;
  }

  private buildRequestBodyPrepare(
    proposal: any,
    param: {
      sheetUrl: string,
      name: string,
      username: string
    },
    serviceId: string,
    fileName: string,
    policyId: string,
    policyType: string,
  ) {
    return {
      variables: {
        start_txt_maToTrinh: {
          value: proposal.eappNumber,
          type: "string",
          valueInfo: null
        },
        start_up_fileTrinhKy: {
          value: param.sheetUrl,
          type: "file",
          valueInfo: {
            filename: fileName,
            mimeType: this.getFileExtension(fileName)
          }
        },
        start_url_linkPhieuCrm: {
          value: process.env.SERVICE_URL + policyType + '-policy/' + policyId,
          type: "string",
          valueInfo: null
        },
        requestSubject: {
          value: param.name,
          type: "string",
          valueInfo: null
        }
      },
      serviceId: serviceId,
      isDraft: true,
      // procDefId: proposalType,
      appCode: process.env.APP_CODE,
      requestCode: proposal.eappNumber,
      account: param.username
    };
  }

  private getServiceIdByProposalType(type: string): string {
    return type !== PolicyTypeEnum.PAYMENT
      ? process.env.EAPP_SERVICE_ID_DISCOUNT_PROPOSAL
      : process.env.EAPP_SERVICE_ID_PAYMENT_PROPOSAL;
  }

  private async createProposalCRM(param: {
    eappNumber: string,
    type: string,
    sheetName: string,
    files: any,
    sheetUrl: string,
    name: string,
    username: string,
  }, headers: any, proposalType: string, proposalId: string, policyId:string) {
    const serviceId = this.getServiceIdByProposalType(param.type);
    let proposal:any;
    let policyType = param.type;
    if(!proposalId){
      param.type = param.type === PolicyTypeEnum.PAYMENT ? ProposalTypeEnum.PAYMENT_POLICY_PROPOSAL : ProposalTypeEnum.DISCOUNT_POLICY_PROPOSAL;
      const response = await axios.post(
      process.env.API_CREATE_PROPOSAL,
      param,
      {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      }
    );
      if (response.data.data.proposal){
        proposal = response.data.data.proposal;
        await super.update(policyId, {proposalId :proposal.id,modifiedDate: new Date()});
      }
    }else{
      const url = `${process.env.UPDATE_PROPOSAL}/${proposalId}`;
      const getProposal = (await axios.get(url, {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      })).data.data;
      param.eappNumber = getProposal.eappNumber;
      proposal = getProposal;
    }

    const fileName = param.sheetUrl.split('/').pop();
    const requestBodyPrepare = this.buildRequestBodyPrepare(proposal, param, serviceId, fileName, policyId, policyType);

    return {
      requestBodyPrepare,
      proposal
    }
  }

  buildMultiFileVariables(files: Array<{ url: string, name?: string }>): Record<string, any> {
    const result = {};
    if (files && Array.isArray(files) && files.length > 0) {
      files.forEach((file, index) => {
        const key = `start_up_chungTuDinhKem_multiFile_${index + 1}`;
        result[key] = {
          value: file.url,
          type: 'file',
          valueInfo: {
            filename: file.name || '',
            mimeType: this.getFileExtension(file.name) || '',
          },
        };
      });
    }
    return result;
  }

  async createPolicyProposal(body: PolicyProposalDto, user: any, headers: any) {
    const { eappNumber, proposalId, type, name, files, status, id } = await this.findPolicyById(body.id);
    this.checkNumberProposal(eappNumber, proposalId, status);
    this.checkTypeProposal(body.type, type);
    let proposalType = process.env.DISCOUNT_EAPP_PROC_DEF_ID;;
      if (type === PolicyTypeEnum.PAYMENT) {
        proposalType = process.env.PAYMENT_EAPP_PROC_DEF_ID;
      }
    const { proposal, requestBodyPrepare, ticketId } = await this.prepareProposalData(
      status, body, user, headers, eappNumber, type, proposalId, name, proposalType, id
    );
    this.mergeMultiFileVariables(requestBodyPrepare, files);
    this.setProposalStartVariable(body, requestBodyPrepare, proposal);

    const { statusCode, data, message } = await this.sendProposalToWorkflow(requestBodyPrepare, ticketId, headers, type);

    if (statusCode === "EAPPPROPOSAL0001") {
      return { statusCode, success: false, message };
    }

    if (statusCode !== "0") {
      throw new HttpException({ errorCode: 'POLI0007' }, HttpStatus.OK);
    }

    if (status === StatusPolicyFPTEnum.NEW || status === StatusPolicyFPTEnum.CANCELLED) {
      proposal.type = proposal.type === PolicyTypeEnum.PAYMENT ? ProposalTypeEnum.PAYMENT_POLICY_PROPOSAL : ProposalTypeEnum.DISCOUNT_POLICY_PROPOSAL;
      proposal.status = StatusPolicyFPTEnum.INIT;
      await this.updateProposalAfterCreation(proposal, data, headers);
      return await super.update(body.id, {
        urlEapp: data.url,
        status: StatusPolicyFPTEnum.INIT.toString(),
        ticketStatus: TicketEappStatusEnum.DRAFT,
        proposalId: proposal.id,
        modifiedBy: user.id,
        modifiedDate: new Date()
      });
    }

    return { id };
  }

  private async prepareProposalData(status, body, user, headers, eappNumber, _type, proposalId, name, proposalType, policyId) {
    if ([StatusPolicyFPTEnum.NEW, StatusPolicyFPTEnum.CANCELLED].includes(status)) {
      const result = await this.createProposalCRM({
        eappNumber,
        type: _type,
        sheetName: body.sheetName,
        files: body.files,
        sheetUrl: body.sheetUrl,
        name,
        username: user.username
      }, headers, proposalType, proposalId, policyId);
      return {
        proposal: result.proposal,
        requestBodyPrepare: result.requestBodyPrepare,
        ticketId: 0
      };
    }

    if ([StatusPolicyFPTEnum.RETURNED, StatusPolicyFPTEnum.REJECTED].includes(status)) {
      const url = `${process.env.UPDATE_PROPOSAL}/${proposalId}`;
      const { data } = (await axios.get(url, {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      })).data;

      const serviceId = this.getServiceIdByProposalType(_type);
      const fileName = body.sheetUrl.split('/').pop();

      const proposal = {
        ...data,
        sheetName: body.sheetName,
        files: body.files,
        sheetUrl: body.sheetUrl,
        name
      };

      const requestBodyPrepare = this.buildRequestBodyPrepare(proposal, {
        sheetUrl: body.sheetUrl,
        name,
        username: user.username
      }, serviceId, fileName, policyId, _type);

      return { proposal, requestBodyPrepare, ticketId: proposal.ticketId };
    }

    throw new Error('Unsupported status in proposal preparation.');
  }

  private mergeMultiFileVariables(requestBodyPrepare, files) {
    if (files && files.length > 0) {
      const buildMultiFileVariables = this.buildMultiFileVariables(files);
      requestBodyPrepare.variables = {
        ...requestBodyPrepare.variables,
        ...buildMultiFileVariables
      };
    }
  }

  private setProposalStartVariable(body, requestBodyPrepare, proposal) {
    const start_txt = {
      value: proposal.code,
      type: "string",
      valueInfo: null
    };
    const key = body.type !== PolicyTypeEnum.PAYMENT
      ? "start_txt_maPheDuyetChinhSachChietKhau"
      : "start_txt_maPheDuyetChinhSachThanhToan";

    requestBodyPrepare.variables[key] = start_txt;
  }

  private async sendProposalToWorkflow(requestBodyPrepare, ticketId, headers, policyType: string) {
    const response = await axios.post(
      process.env.API_GATEWAY_URL,
      {
        type: process.env.TYPE_PROPOSAL_CREATE_PROPOSAL,
        data: requestBodyPrepare,
        ticketId,
        proposalType: policyType
      },
      {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      }
    );
    return response.data;
  }

  private async updateProposalAfterCreation(proposal, data, headers) {
    const dataProposal = { ...proposal, ticketId: data.ticketId, url: data.url, urlEapp: data.url };
    await axios.put(
      process.env.UPDATE_PROPOSAL,
      dataProposal,
      {
        headers: { 'Content-Type': 'application/json', Authorization: headers.authorization },
        timeout: parseInt(process.env.TIMEOUT_API) || 120000
      }
    );
  }

  async fillDocxTemplateFromBuffer(buffer: Buffer, data: any): Promise<Buffer> {
    try {
      const content = buffer.toString('binary');
      const zip = new PizZip(content);
      const imageModule = new ImageModule({
        centered: true,
        async getImage(tagValue, tagName) {
          // Fetch image từ URL
          const response = await axios.get(tagValue, {
            responseType: 'arraybuffer'
          });
          return response.data;
        },
        // getSize(imgBuffer, tagValue, tagName) {
        //   return [80, 80];
        // }
        getSize(imgBuffer: Buffer, tagValue: string, tagName: string) {
          const dimensions = sizeOf(imgBuffer);
          if (!dimensions.width || !dimensions.height) {
            throw new Error(`Cannot determine image size for tag "${tagName}"`);
          }
          const maxWidth = 120;
          const ratio = maxWidth / dimensions.width;
          return [maxWidth, dimensions.height * ratio];
        }

      });
      const doc = new Docxtemplater(zip, {
        modules: [imageModule],
        paragraphLoop: true,
        linebreaks: true,
        nullGetter() { return ''; }
      });
      await doc.renderAsync(data);
      const bufferOutput = doc.getZip().generate({ type: 'nodebuffer' });
      return bufferOutput;
    } catch (error) {
      throw new HttpException({ errorCode: 'FILETEMPLATE0001', message: "Failed to fill template, please try again later" }, HttpStatus.OK);
    }
  }

  async getValueFileBuffer(fileUrl: string): Promise<Buffer> {
    try {
      const response = await axios.get(fileUrl, {
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
      });
      return Buffer.from(response.data);
    } catch (error) {
      throw new HttpException({ errorCode: 'FILETEMPLATE0002', message: "Failed to fetch file, please try again later" }, HttpStatus.OK);
      // throw new Error(`Failed to fetch file: ${error.message}`);
    }
  }

  async paymentPolicyPreviewForm(dto: PreviewFormDto, headers: any) {
    const policy = await this.findPolicyById(dto.policyId);
    let { fileUrl, dear, body } = dto;
    fileUrl = this.configService.get("S3_URL") + `/${fileUrl}`;
    const fileBuffer = await this.getValueFileBuffer(fileUrl);
    if (!Buffer.isBuffer(fileBuffer)) {
      throw new Error('Input must be a Buffer');
    }
    const backendUrl = this.configService.get("BACKEND_URL");
    const employee = await this.httpService.getAsync(
      `${backendUrl}/msx-employee/api/v2/internal-employee/getInternalEmployeeByAccountLogin`,
      {
        headers: {
          Authorization: headers.authorization,
          'Content-Type': 'application/json',
        },
      },
    )
    const employeeData = employee['data'] || {};
    const level1 = employeeData['level1'] || {};
    const address = employeeData['address'] || {};
    const city = address['city'] || {};
    const cityName = city['name'] || '';
    const level1Code = level1['code'] || '';
    const logo = `${this.configService.get("S3_URL_DXG")}/dxg/${level1Code}.png`;
    const { eappNumber, description, proposalId, type } = policy;
    const project = policy['project'] || {}
    const startDate = policy['startDate'];
    const expiredDate = policy['expiredDate'];
    const schedule = policy['schedule'] || {}
    const installments = schedule['installments'] || []

    const listInstallments = installments.map((installment) => {

      const valuePaymentBasedOnProductPrice = installment.typeRealEstate === 'default' ? new Intl.NumberFormat('en-US', { maximumFractionDigits: 0 }).format(installment.value) : ""
      const unitPaymentBasedOnProductPrice = installment.typeRealEstate === 'default' ? installment.type === 'percent' ? '%' : 'VND' : ""

      const valuePaymentBasedOnHousePrice = installment.typeRealEstate === 'default' ? "" : new Intl.NumberFormat('en-US', { maximumFractionDigits: 0 }).format(installment.value)
      const unitPaymentBasedOnHousePrice = installment.typeRealEstate === 'default' ? "" : installment.type === 'percent' ? '%' : 'VND'
      const valuePaymentBasedOnLandPrice = installment.typeRealEstate === 'default' ? "" : new Intl.NumberFormat('en-US', { maximumFractionDigits: 0 }).format(installment.value2)
      const unitPaymentBasedOnLandPrice = installment.typeRealEstate === 'default' ? "" : installment.type2 === 'percent' ? '%' : 'VND'

      const valuePaymentTimeOrPaymentDate = installment.expiredDateType === 'numberDay' ? installment.expiredDays : moment(installment.exactDays).format('DD/MM/YYYY')

      return {
        installmentsName: installment.name,
        valuePaymentBasedOnProductPrice,
        unitPaymentBasedOnProductPrice,
        valuePaymentBasedOnHousePrice,
        unitPaymentBasedOnHousePrice,
        valuePaymentBasedOnLandPrice,
        unitPaymentBasedOnLandPrice,
        valuePaymentTimeOrPaymentDate
      };
    })

    const data = {
      eappNumber: eappNumber,
      name: policy.name,
      code: policy.code,
      projectName: project.name,
      startDate: moment(startDate).format('DD/MM/YYYY'),
      endDate: expiredDate ? moment(expiredDate).format('DD/MM/YYYY') : '...',
      city: cityName,
      date: moment().format('DD'),
      month: moment().format('MM'),
      year: moment().format('YYYY'),
      dear: dear,
      body: body,
      logo: logo,
      description,
      installments: listInstallments,
    };

    const outputBuffer = await this.fillDocxTemplateFromBuffer(
      fileBuffer,
      data,
    );

    const outputDir = path.join(__dirname, '../../output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    const outputFile = dto.output || OutputFile.FILE;
    const outputPath = path.join(outputDir, `template-${Date.now()}.docx`);
    fs.writeFileSync(outputPath, outputBuffer);
    const filePDF = await this.convertToPdf(outputPath, outputFile)
    return filePDF;
  }

  async convertToPdf(outputPath: string, outputFile: string): Promise<any> {
    try {
      const formData = new FormData();
      const fileStream = fs.createReadStream(outputPath);
      formData.append('files', fileStream);
      formData.append('merge', 'true');
      let config: any = {
        method: 'post',
        url: `${this.configService.get("EAPP_LOCATION_URL")}/forms/libreoffice/convert`,
        headers: {
          ...formData.getHeaders()
        },
        responseType: 'arraybuffer',
        data: formData
      };

      const response = await axios.request(config);
      const base64 = Buffer.from(response.data).toString('base64');

      if (outputFile !== OutputFile.FILE) {
        const s3Params = {
          Bucket: this.configService.get("AWS_BUCKET_NAME"),
          Key: `policy/${Date.now()}-${outputPath.split('/').pop()}.pdf`,
          Body: Buffer.from(response.data),
          ACL: 'public-read',
          ContentType: 'application/pdf',
        };
        const s3Response = await this.s3.upload(s3Params).promise();
        if (!s3Response.hasOwnProperty('key')) {
          s3Response['key'] = s3Response.Key;
        }

        s3Response['Location'] = this.configService.get('S3_URL') + '/' + s3Response['key'];
        return s3Response;
      }

      return `data:application/pdf;base64,${base64}`;
    } catch (error) {
      throw new HttpException({ errorCode: 'FILETEMPLATE0003', message: "Failed to convert to PDF" }, HttpStatus.OK);
    } finally {
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
    }
  }

  async discountPolicyPreviewForm(dto: PreviewFormDto, headers: any) {
    const policy = await this.findPolicyById(dto.policyId);

    let { fileUrl, dear, body, output } = dto;
    fileUrl = this.configService.get("S3_URL") + `/${fileUrl}`;

    const fileBuffer = await this.getValueFileBuffer(fileUrl);
    if (!Buffer.isBuffer(fileBuffer)) {
      throw new Error('Input must be a Buffer');
    }
    const backendUrl = this.configService.get("BACKEND_URL");
    const employee = await this.httpService.getAsync(
      `${backendUrl}/msx-employee/api/v2/internal-employee/getInternalEmployeeByAccountLogin`,
      {
        headers: {
          Authorization: headers.authorization,
          'Content-Type': 'application/json',
        },
      },
    )

    const employeeData = employee['data'] || {};
    const level1 = employeeData['level1'] || {};
    const address = employeeData['address'] || {};
    const city = address['city'] || {};
    const cityName = city['name'] || '';
    const level1Code = level1['code'] || '';
    const logo = `${this.configService.get("S3_URL_DXG")}/dxg/${level1Code}.png`;
    const { eappNumber } = policy;

    let discountPolicyTypeRealEstate = '';
    switch (policy['typeRealEstate']) {
      case 'default': {
        discountPolicyTypeRealEstate = "Giá bán";
        break;
      }
      case 'house': {
        discountPolicyTypeRealEstate = "Giá nhà";
        break;
      }
      case 'land': {
        discountPolicyTypeRealEstate = "Giá đất";
        break;
      }
    }
    const data = {
      eappNumber: eappNumber || '',
      discountPolicyName: policy.name,
      discountPolicyCode: policy.code,
      projectName: policy['project']?.name,
      discountPolicyStartDate: moment(policy['startDate']).format('DD/MM/YYYY'),
      discountPolicyEndDate: policy['expiredDate'] ? moment(policy['expiredDate']).format('DD/MM/YYYY') : '...',
      discountPolicyValue: new Intl.NumberFormat('en-US', { maximumFractionDigits: 0 }).format(policy['value']),
      discountPolicyType: policy['typeDiscount'] === 'percent' ? '%' : 'VND',
      discountPolicyTypeRealEstate,
      description: policy['description'],
      city: cityName,
      date: moment().format('DD'),
      month: moment().format('MM'),
      year: moment().format('YYYY'),
      dear: dear,
      body: body,
      logo: logo,
    }

    const outputBuffer = await this.fillDocxTemplateFromBuffer(fileBuffer, data);

    const outputDir = path.join(__dirname, '../../output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    const outputPath = path.join(outputDir, `template-${Date.now()}.docx`);

    const outputFile = dto.output || OutputFile.FILE;

    fs.writeFileSync(outputPath, outputBuffer);
    const filePDF = await this.convertToPdf(outputPath, outputFile)
    return filePDF;
  }

  async deletePolicy(id: string, user: any,dto: DeletePolicyDto) {
    const existingPolicy = await super.findOne({id: id, softDelete: false, status: StatusPolicyFPTEnum.NEW });
    if (!existingPolicy) {
      throw new HttpException({ errorCode: 'POLI0001' }, HttpStatus.NOT_FOUND);
    }
    
    return await super.update(id, { modifiedBy: user.id, modifiedDate: new Date(), softDelete: true, reasonDelete: dto.reasonDelete });
  }
}
