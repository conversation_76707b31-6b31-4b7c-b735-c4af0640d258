import { Connection } from 'mongoose';
import { CommonConst } from '../../shared/constant';
import { ProposalQuerySchema } from '../../proposal.queryside/schemas/proposal.query.schema';

export const QueryProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) => connection.model(CommonConst.POLICY_COLLECTION, ProposalQuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
