import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import * as _ from 'lodash';
import { existsSync, unlinkSync } from 'fs';
import { join } from 'path';
import { FileGenerationService } from './file-generation.service';
import { StaticAssetService } from '../config/static-asset.service';
import Decimal from 'decimal.js';
import { DebtExpenseListRepository } from './repository/repository';
import { CreateDebtExpenseListDto } from './dto/debt-expense-list.dto';
import uuid = require('uuid');
import { InvalidCommandHandlerException } from '@nestjs/cqrs';
import { CommonConst } from '../shared/constant';
import { ErrorConst } from '../shared/constant/error.const';
import { PrimaryContractRepository } from '../primary-contract/repository/primary-contract-query.repository';
import { DebtCommissionService } from '../debt-commission/service';
import { DebtCommissionStatus } from '../shared/enum/commission.enum';
import { DebtCommissionPolicyService } from '../debt-commission-policy/service';
import { PropertyClient } from '../mgs-sender/property.client';
import { CmdPatternConst } from '../../../shared-modules';
import * as fs from 'fs';
import { countWeekendDays, pushFileS3 } from '../shared/utils/transform';

@Injectable()
export class DebtExpenseListService {
  private ONE_DAY = 1000 * 60 * 60 * 24;
  constructor(
    private readonly repository: DebtExpenseListRepository,
    private readonly primaryContractRepository: PrimaryContractRepository,
    private readonly fileGenerationService: FileGenerationService,
    private readonly staticAssetService: StaticAssetService,
    private readonly debtCommissionService: DebtCommissionService,
    private readonly debtCommissionPolicyService: DebtCommissionPolicyService,
    private readonly propertyClient: PropertyClient
  ) {}

  async listAll(query: any) {
    const _query: any = {};
    if (query.debtCommissionId) {
      _query['debtCommission.id'] = query.debtCommissionId;
    }
    if (query.projectId) {
      _query['project.id'] = query.projectId;
    }
    if (query.search) {
      _query['$or'] = [
        { code: { $regex: new RegExp(query.search), $options: 'i' } },
        { name: { $regex: new RegExp(query.search), $options: 'i' } },
      ];
    }

    const DebtExpenseList = await this.repository.find(_query);
    return DebtExpenseList;
  }

  async downloadListTransaction(fileName, query, res) {
    let result = await this.listAll(query);

    this.fileGenerationService
      .exportTransaction(fileName, result, query)
      .then(() => {
        fileName = fileName + '.xlsx';
        const filePath = join(
          this.staticAssetService.getUploadFolderPath(),
          fileName
        );

        const isFileExisted = existsSync(filePath);
        if (isFileExisted === false) {
          throw new NotFoundException('File not found');
        }

        res.sendFile(fileName, {
          root: this.staticAssetService.getUploadFolderPath(),
        });
        setTimeout(() => {
          unlinkSync(filePath);
        }, 10000);
      });
  }

  async createDebtExpenseList(user, dto: CreateDebtExpenseListDto) {
    dto.modifiedBy = user?.id;
    dto.createdBy = user?.id;
    await this.repository.create(dto);
    return { id: dto.id };
  }

  async updateDebtExpenseList(user, dto: any) {
    if (dto.id === '') {
      throw new InvalidCommandHandlerException();
    }
    const debtCommission = await this.repository.findOne({ id: dto.id });
    if (!debtCommission) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.NOT_FOUND,
          'DebtExpenseList',
          'id',
          dto.id
        ),
      });
    }
    dto.modifiedBy = user?.id;
    await this.repository.update(dto);
    return { id: dto.id };
  }

  async deleteDebtExpenseList(id: string) {
    const debtCommissionList = new CreateDebtExpenseListDto();
    debtCommissionList.id = id;
    debtCommissionList.softDelete = true;
    await this.repository.delete(debtCommissionList);
  }

  async publishDebtExpenseList(user, id: string) {
    const debtCommission = await this.repository.findOne({ id });
    if (!debtCommission) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.NOT_FOUND,
          'DebtExpenseList',
          'id',
          id
        ),
      });
    }
    debtCommission.isPublish = true;
    debtCommission.modifiedBy = user.id;
    return await this.repository.update(debtCommission);
  }

  async unPublishDebtExpenseList(user, id: string) {
    const debtCommission = await this.repository.findOne({ id });
    if (!debtCommission) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.NOT_FOUND,
          'DebtExpenseList',
          'id',
          id
        ),
      });
    }
    debtCommission.isPublish = false;
    debtCommission.modifiedBy = user.id;
    return await this.repository.update(debtCommission);
  }

  async calcData(user: any, dto: any) {
    const contracts = await this.primaryContractRepository.findOverdueContracts();

    if (!contracts || contracts?.length === 0) return [];
    const debtCommission = await this.debtCommissionService.findById(dto.debtCommissionId);
    if (!debtCommission) return []
    let debtCommissionPolicy = await this.debtCommissionPolicyService.findById(debtCommission.commissionPolicy.id);
    const projectIds = Array.from(
      new Set(
        contracts
          .map(x => x.primaryTransaction?.project?.id)
          .filter(id => id != null)
      )
    );
    const projects = await this.propertyClient.sendDataPromise( 
      { ids: projectIds },
      CmdPatternConst.PROJECT.GET_PROJECT_BY_IDS
    );
    if (!debtCommissionPolicy) return [];
    else debtCommissionPolicy = debtCommissionPolicy.toObject();

    // Mapping lại danh sách dữ liệu
    const modelList: any[] = contracts
      .map((contract) => {
        const project = projects.find(
          (pro) => pro.id === contract.primaryTransaction?.project?.id
        );
        let paymentDue: any;
        let today = new Date();
        let lateDay: number;
        let currentDebtage: any;
        const currentInstallment = contract.policyPayment.schedule.installments
          .filter((x) => x.totalAmount >= x.totalTransfered) // Chỉ lấy đợt chưa thanh toán hết
          .filter(
            (i) => new Date(i.paymentDueDate).getTime() < new Date().getTime()
          ) // Chỉ lấy quá khứ
          .reduce((farthest, current) => {
            return new Date(current.paymentDueDate).getTime() <
              new Date(farthest.paymentDueDate).getTime()
              ? current
              : farthest;
          });
        if (project?.setting?.debtage){
          paymentDue = new Date(currentInstallment.paymentDueDate);
          paymentDue.setDate(paymentDue.getDate() + 1);
          if (project?.setting?.onlyWorkday) {
            const numberOfSatSun = countWeekendDays(paymentDue, today)
            lateDay = today.getTime() - paymentDue.getTime() + this.ONE_DAY - (numberOfSatSun * this.ONE_DAY);
          }
          else lateDay = today.getTime() - paymentDue.getTime() + this.ONE_DAY;
          lateDay = Math.floor(lateDay / this.ONE_DAY);
          currentDebtage = project?.setting?.debtage.find(item => {
            if (item.isFinalRange) {
              return lateDay >= item.fromDay;
            }
            return lateDay >= item.fromDay && lateDay <= item.toDay;
          });
        }
        let penalty = debtCommissionPolicy.penalty.find(
          (x) => x.name === currentDebtage?.name
        );
        return {
          debtCommissionId: debtCommission.id,
          debtCollector: contract.debtCollector,
          code: contract.code,
          name: contract.name,
          primaryTransaction: contract.primaryTransaction,
          policyPayment: contract.policyPayment,
          interestCalculations: contract.interestCalculations,
          penalty: penalty,
          penaltyPerContract: penalty ? this.calcPenaltyPerContract(
            penalty,
            contract,
            project,
            currentInstallment
          ) : 0,
        };
      })
      .filter((item) => item);

    return modelList;
  }

  calcPenaltyPerContract(penalty, contract, project, currentInstallment) {
    const interestCalculation =
      contract.interestCalculations[contract.interestCalculations.length - 1]; // luôn lấy phiếu lãi mới nhất

    if (penalty.unit === '%') {
      let baseAmount = 0;
      if (penalty.type === 'principal')
        baseAmount =
          currentInstallment.totalAmount - currentInstallment.totalTransfered;
      else if (penalty.type === 'interest_latePaymentFee')
        baseAmount =
          interestCalculation.interestAmount + project?.setting?.latePaymentFee;
      else if (penalty.type === 'principal_and_interest_latePaymentFee')
        baseAmount =
          currentInstallment.totalAmount -
          currentInstallment.totalTransfered +
          interestCalculation.interestAmount +
          project?.setting?.latePaymentFee;

      return baseAmount * (penalty.amount / 100);
    } else return penalty.amount;
  }

  async mergeData(user: any, dto: any) {
    let debtCommission = await this.debtCommissionService.findById(dto.debtCommissionId);
    if (!debtCommission) return [];
    if (
      debtCommission.adjustmentVersions &&
      debtCommission.adjustmentVersions.length > 0
    )
      return [];
    Promise.all(
      dto.modelList.map(async (dto, index) => {
        const entity = await this.repository.findOne({
          'debtCommission.periodFrom': debtCommission.periodFrom,
          'debtCommission.periodTo': debtCommission.periodTo,
          'primaryTransaction.propertyUnit.view1': dto.primaryTransaction.propertyUnit.view1,
        });
        if (entity) {
          // Update
          dto.id = entity.id;
          return await this.updateDebtExpenseList(user, dto);
        } else {
          // Create
          return await this.createDebtExpenseList(user, dto);
        }
      })
    );

    await this.fileGenerationService.exportTransaction(
      'danh-sach-giao-dich-tinh-lai-phat',
      { expenseList: dto.modelList },
      {}
    );
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      'danh-sach-giao-dich-tinh-lai-phat.xlsx'
    );
    const buffer = fs.readFileSync(filePath);
    const response = await pushFileS3(buffer, true, 'danh-sach-giao-dich-tinh-lai-phat.xlsx');

    let currentId = uuid.v4();
    let adjustmentVersion = {
      version: 'v.1',
      status: DebtCommissionStatus.NEW,
      id: currentId,
      uploadDate: new Date(),
      uploadBy: user?.id,
      fileName: response.data.originalname,
      fileUrl: response.data.key,
      filePath: response.data.key,
    };
    debtCommission.adjustmentVersions.push(adjustmentVersion);
    await this.debtCommissionService.updateDebtCommission(
      user,
      {id: dto.debtCommissionId},
      ''
    );
    return true;
  }
}
