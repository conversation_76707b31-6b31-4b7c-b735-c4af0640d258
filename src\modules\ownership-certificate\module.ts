import { CqrsModule } from "@nestjs/cqrs";
import { OwnershipCertificateQueryController } from "./controller";
import { OwnershipCertificateQueryService } from "./service";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { forwardRef, Module } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { LoggerModule } from "../.././modules/logger/logger.module";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { ListenerModule } from "../listener/listener.module";
import { OwnershipCertificateQueryRepository } from "./repository/ownership-certificate.query.repository";
import { HandoverScheduleQuerySideModule } from "../handover-schedule.queryside/module";
import { QueryProviders } from "./providers/query.cqrs.providers";

@Module({
  imports: [
    QueryDatabaseModule,
    AuthModule,
    LoggerModule,
    MgsSenderModule,
    forwardRef(() => HandoverScheduleQuerySideModule),
  ],
  controllers: [OwnershipCertificateQueryController],
  providers: [
    OwnershipCertificateQueryService,
    OwnershipCertificateQueryRepository,
    ...QueryProviders,
  ],
  exports: [
    OwnershipCertificateQueryRepository,
    OwnershipCertificateQuerySideModule,
    OwnershipCertificateQueryService,
  ],
})
export class OwnershipCertificateQuerySideModule {}
