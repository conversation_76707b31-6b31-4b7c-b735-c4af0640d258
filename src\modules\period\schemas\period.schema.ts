import * as mongoose from 'mongoose';
import uuid = require('uuid');

export const QuerySchema = new mongoose.Schema({
    _id: { type: String },
    modifiedBy: { type: String },
    modifiedDate: { type: Date, default: () => Date.now() },
    createdBy: { type: String },
    createdDate: { type: Date, default: () => Date.now() },
    reasonDelete: { type: String, default: null },
    softDelete: { type: Boolean, default: false },
    id: { type: String, default: uuid.v4, index: true },

    // Thông tin chi tiết
    name: { type: String, default: null },            
    periodEndDate: { type: Number, default: 1 },
    periodStartDate: { type: Number, default: 1 },
    projects: { type: [Object], default: [] },
    isActive: { type: Number, default: 2 },
});

QuerySchema.pre('save', function (next) {
    this._id = this.get('id');
    next();
});
