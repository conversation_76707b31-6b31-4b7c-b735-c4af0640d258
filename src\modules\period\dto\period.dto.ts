import { IsIn, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ClassBased } from '../../shared/classes/class-based';
import { IPeriod } from '../../shared/interfaces/period.interface';

export class PeriodDto extends ClassBased implements IPeriod {
    @IsOptional()
    id: string;
    @IsOptional()
    name: string;    
    @IsOptional()         
    isActive: number;
    @IsOptional()
    periodEndDate: number;
    @IsOptional()
    periodStartDate: number;
    @IsOptional()
    projects: any[];
    @IsOptional()
    description: string;
    @IsOptional()
    createdBy: string;
}

export class CreatePeriodDto extends PeriodDto {
    @IsNotEmpty()
    name: string;        

    @IsNotEmpty()
    @IsNumber()
    @Min(1)
    @Max(31)
    periodStartDate: number;

    @IsNotEmpty()
    @IsNumber()
    @IsIn([1,2])
    isActive: number
}

export class UpdatePeriodDto extends PeriodDto {
    @IsNotEmpty()
    id: string;

    @IsNotEmpty()
    name: string; 

    @IsOptional()
    @IsNumber()
    @IsIn([1,2])
    isActive: number
}

export class UpdatePeriodStatusDto extends PeriodDto {
    @IsNotEmpty()
    id: string;

    @IsNotEmpty()
    @IsNumber()
    @IsIn([1,2])
    isActive: number;
}

export class DeletePeriodDto extends PeriodDto {
    @IsNotEmpty()
    id: string;

    @IsNotEmpty()
    reasonDelete: string;
}