import * as mongoose from 'mongoose';
import uuid = require('uuid');

export const DebtExpenseListSchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  debtCollector: { type: Object },
  code: { type: String },
  name: { type: String },
  primaryTransaction: { type: Object },
  policyPayment: { type: Object },
  interestCalculations: { type: Object },
  penalty: { type: Object },
  penaltyPerContract: { type: Number },
  createdBy: { type: String },
  modifiedBy: { type: String },
  createdAt: { type: Date },
  updatedAt: { type: Date },
});

DebtExpenseListSchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
