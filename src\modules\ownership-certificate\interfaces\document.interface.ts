import { Document } from "mongoose";
import { IOwnershipCertificate } from "../../shared/services/ownership-certificate/interfaces/interface";

export interface IOwnershipCertificateDocument extends Document, IOwnershipCertificate {
  createdBy: any;
  updatedBy: any;
  id: string;
  description: string;
  emailTitle: string,
  emailFrom: string,
  emailCC: string,
  emailBCC: string,
  emailTemplate: string,
  smsTemplate: string,
  smsBrandName: string,
  hotline: string,
  isActive: number;
  reasonDelete:string
  softDelete: boolean,
}
