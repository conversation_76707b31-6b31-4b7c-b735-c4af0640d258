import {
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";
import { ClassBased } from "../../shared/classes/class-based";
import { DebtCommissionStatus } from "../../shared/enum/commission.enum";
import { Type } from "class-transformer";
import * as _ from "lodash";
import { IDebtCommission } from "../../shared/services/debt-commission/interfaces/debt-commission.interface";

export class DebtCommissionDto extends ClassBased implements IDebtCommission {
  @IsOptional()
  id: string;
  @IsOptional()
  isPublish: boolean;
  @IsOptional()
  publishTo: string[];
  @IsOptional()
  description: string;
  @IsOptional()
  name: string;
  @IsOptional()
  year: string;
  @IsOptional()
  period: string;
  @IsOptional()
  periodFrom: string;
  @IsOptional()
  periodTo: string;
  @IsOptional()
  code: string;
  @IsOptional()
  type: string;
  @IsOptional()
  project: object;
  @IsOptional()
  status: string;
  @IsOptional()
  adjustmentVersions: any;
  @IsOptional()
  commissionPolicy: any;
  @IsOptional()
  modifiedDate: Date;
  @IsOptional()
  createdBy: string;
  @IsOptional()
  createdDate: Date;

  constructor(init?: Partial<DebtCommissionDto>) {
    super();
    Object.assign(this, init);
  }
}

class ProjectDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  code: string;

  @IsNotEmpty()
  @IsString()
  name: string;
}

export class CreateDebtCommissionDto extends DebtCommissionDto {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => ProjectDto)
  project: ProjectDto;

  @IsNotEmpty()
  @IsString()
  year: string;

  @IsNotEmpty()
  @IsString()
  periodName: string;

  @IsNotEmpty()
  @IsString()
  periodFrom: string;

  @IsNotEmpty()
  @IsString()
  periodTo: string;

  @IsNotEmpty()
  @IsString()
  commissionPolicyCode: string;
}

export class UpdateDebtCommissionDto extends DebtCommissionDto {
  @IsNotEmpty()
  id: string;

  name: string;

  @IsOptional()
  project: ProjectDto;
}

export class DeleteDebtCommissionDto {
  @IsOptional()
  @IsString()
  softDeleteReason: string;
}

export class UpdateStatusDebtCommissionDto extends DebtCommissionDto {
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  @IsEnum(DebtCommissionStatus, {
    message: `status must be one of: ${Object.values(
      DebtCommissionStatus
    ).join(", ")}`,
  })
  status: string;

  @IsNotEmpty()
  adjustmentVersionId: string;
}
