import {
  Controller,
  Post,
  Body,
  Headers,
  UseInterceptors,
  Put,
  Delete,
  Param, UploadedFiles,
  UploadedFile,
  HttpStatus,
  HttpException,
} from "@nestjs/common";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { PrimaryContractDomainService } from "./service";
import { Action } from "../shared/enum/action.enum";
import { ValidationPipe } from "@nestjs/common";
import {
  HandoverPrimaryContractDto,
  CreatePrimaryContractDto,
  PrimaryContractStatusDto, SendPrimaryContractDeliveryNotifyDto,
  UpdateInterestCalculationDto, UpdateManyPrimaryContract, UpdatePrimaryContractDeliveryDateDto,
  UpdatePrimaryContractDto,
  UpdatePrimaryContractFileDto,
  UpdateShowReceiptDto,
  OwnershipCertificatePrimaryContractDto,
  UpdatefeeAdvanceAmountDto
} from "./dto/primary-contract.dto";
import { UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "../../common/guards/roles.guard";
import { ACGuard, UseRoles } from "nest-access-control";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { FileInterceptor, FilesInterceptor } from "@nestjs/platform-express";
import { PrimaryContractCustomerDomainService } from "./customer-service";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";

@Controller("v1/primary-contract")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard)
export class PrimaryContractDomainController {
  private actionName: string = Action.NOTIFY;
  private resSuccess = { success: true };
  constructor(
    private readonly primaryContractService: PrimaryContractDomainService,
    private readonly primaryContractCustomerService: PrimaryContractCustomerDomainService
  ) { }
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_CREATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Post()
  async createPrimaryContract(@User() user, @Body(new ValidationPipe()) dto: CreatePrimaryContractDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.createContract(user, dto, this.actionName);
  }
  @Post('purchase')
  async createPurchaseContract(@User() user, @Body(new ValidationPipe()) dto: any, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.createPurchaseContract(user, dto, this.actionName);
  }

  @Put('purchase')
  async updatePurchaseContract(@User() user, @Body(new ValidationPipe()) dto: any, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.updatePurchaseContract(user, dto, this.actionName);
  }
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_REQUEST_APPROVED,
    action: 'read',
    possession: 'own',
  })

  @Post('requestApproveContract')
  async requestApproveContract(
    @User() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.requestApproveContract(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_APPROVED,
    action: 'read',
    possession: 'own',
  })

  @Post('approveContract')
  async approveContract(
    @User() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.approveContract(user, dto, this.actionName);
  }
  @Post('approvePurchaseContract')
  async approvePurchaseContract(
    @User() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.approvePurchaseContract(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Post('updateManyPrimaryContract')
  async updateManyPrimaryContract(@User() user, @Body() dto: UpdateManyPrimaryContract, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.updateManyPrimaryContract(user, dto, this.actionName);
  }

  @Put('approveInterestCalculation')
  async approveInterestCalculation(
    @User() user: any,
    @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.approveInterestCalculation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Put()
  async updatePolicy(@User() user, @Body(new ValidationPipe()) dto: UpdatePrimaryContractDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.updateContract(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Put('files')
  async updateFiles(@User() user, @Body(new ValidationPipe()) dto: UpdatePrimaryContractFileDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.updateContractFiles(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Put('deliveryDate')
  async updateDeliveryDate(@User() user, @Body(new ValidationPipe()) dto: UpdatePrimaryContractDeliveryDateDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.updateDeliveryDate(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Put('interestCalculation')
  async updateInterestCalculation(@User() user, @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.updateInterestCalculation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_DELETE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Delete(':id')
  async deletePolicy(@User() user, @Param('id') id: string, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.deleteContract(user, id, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_DELETE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Put('deleteInterestCalculation')
  async deleteInterestCalculation(@User() user, @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.deleteInterestCalculation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put('sendDeliveryNotify')
  async sendDeliveryNotify(@User() user, @Body(new ValidationPipe()) dto: SendPrimaryContractDeliveryNotifyDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractService.sendDeliveryNotify(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // => feature name
    action: 'read',
    possession: 'own',
  })
  @Put('updateShowReceipt')
  async showReceipt(@User() user, @Body(new ValidationPipe()) dto: UpdateShowReceiptDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    await this.primaryContractService.updateShowReceipt(dto);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_IMPORT, // => feature name
    action: 'read',
    possession: 'own',
  })
  @UseInterceptors(FilesInterceptor('files'))
  @Post('/import')
  async importPropertyPrimary(
    @UploadedFiles() files,
    @User() user,
    @Body() dto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    await this.primaryContractService.importFiles(user, dto, files, this.actionName);
    return this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Put('deliveryConfirm')
  async handover(@User() user, @Body() dto: HandoverPrimaryContractDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractCustomerService.handover(user, dto, this.actionName) || this.resSuccess;
  }
  // @UseGuards(RoleGuard)
  //   @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Put('update-feeAdvanceAmount')
  async updatefeeAdvanceAmount(@User() user, @Body() dto: UpdatefeeAdvanceAmountDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractCustomerService.updatefeeAdvanceAmount(user, dto, this.actionName) || this.resSuccess;
  }
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Put('delivery-confirm-ownership-certificate')
  async ownershipCertificate(@User() user, @Body() dto: OwnershipCertificatePrimaryContractDto, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.primaryContractCustomerService.ownershipCertificate(user, dto, this.actionName) || this.resSuccess;
  }
  @Post('upload-file')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: any
  ): Promise<any> {
    return await this.primaryContractCustomerService.uploadFile(file);
  }
  @Post('upload-image')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: any) {
    const result = await this.primaryContractCustomerService.uploadImageToS3(file.buffer, file.originalname, 'image');
    return result;
  }
  @Post('upload-multiple-files')
  async uploadMultipleFiles(@UploadedFiles() files: any[]) {
    if (!files || files.length === 0) {
      throw new HttpException('No files uploaded', HttpStatus.BAD_REQUEST);
    }

    const result = await this.primaryContractCustomerService.uploadMultipleFiles(
      files.map(file => ({
        buffer: file.buffer,
        originalName: file.originalname,
      })),
    );

    if ('error' in result) {
      throw new HttpException(result.error, HttpStatus.BAD_REQUEST);
    }

    return {
      fileUrl: result, // mảng link string
    };
  }
}
