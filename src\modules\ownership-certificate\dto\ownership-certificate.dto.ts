import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, IsOptional, ValidateNested, Length, IsDate, <PERSON>A<PERSON>y, MaxLength } from "class-validator";
import { ClassBased } from "../../shared/classes/class-based";
import { Transform, Type } from "class-transformer";
import { isDate } from "moment-timezone";
import { IOwnershipCertificate } from "../../shared/services/ownership-certificate/interfaces/interface";
import { StatusEnum } from "../../shared/enum/primary-contract.enum";

export class FrameDto {
  @IsNotEmpty()
  startTime: string;

  @IsNotEmpty()
  endTime: string;

  @IsNotEmpty()
  @IsNumber()
  @Max(9999999999999, { message: 'amount không được vượt quá 13 chữ số' })
  amount: number;
}
export class EmailDto {
  @IsNotEmpty()
  emailTitle: string;

  @IsNotEmpty()
  emailFrom: string;

  emailCC: string;
  emailBCC: string;

  @IsNotEmpty()
  emailTemplate: string;

  @IsNotEmpty()
  smsTemplate: string;

  @IsNotEmpty()
  smsBrandName: string;
}
export class OwnershipCertificateDto extends ClassBased implements IOwnershipCertificate {
  id: string;
  description: string;

  @IsNotEmpty()
  project: any;

  @IsOptional() // => Cho phép không truyền
  isActive: StatusEnum;

  @IsNotEmpty()
  orgCharts: any;

  @IsNotEmpty()
  @MaxLength(13)
  hotline: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  mondayFrame: FrameDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  tuesdayFrame: FrameDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  wednesdayFrame: FrameDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  thursdayFrame: FrameDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  fridayFrame: FrameDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  saturdayFrame: FrameDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FrameDto)
  sundayFrame: FrameDto[];

  @IsOptional() // => Cho phép không truyền
  @IsArray()
  itemsForEligible: any;

  @IsOptional() // => Cho phép không truyền
  @IsArray()
  itemsCerInProcess: any;

  @IsOptional() // => Cho phép không truyền
  @IsArray()
  itemsForCerReadyHandover: any;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => EmailDto)
  emailForEligible: EmailDto;
  
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => EmailDto)
  emailCerHandedOver: EmailDto;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => EmailDto)
  emailForCerReadyHandover: EmailDto;
}
export class CreateOwnershipCertificateDto extends OwnershipCertificateDto {

}
export class UpdateOwnershipCertificateDto extends OwnershipCertificateDto {
  @IsString()
  @IsNotEmpty()
  id: string;
}
export class DeleteOwnershipCertificateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  reasonDelete: string;
}

export class changeStatusOwnershipCertificateDto {
  @IsString()
  @IsNotEmpty()
  id: string;
}