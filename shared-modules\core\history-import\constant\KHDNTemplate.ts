import { XLSXTemplateConst } from "./XLSXTemplateConst";

export class KHDNTemplate extends XLSXTemplateConst {
  readonly HEADER_REQUIRED_ROW = [
    "STT",
    "Tên công ty *",
    "<PERSON>ên ngắn",
    "<PERSON><PERSON> điện thoại *",
    "<PERSON><PERSON> số thuế",
    "Ng<PERSON>y cấp mã số thuế",
    "Nơi cấp mã số thuế",
    "Người đại diện *",
    "Chức vụ",
    "Chỉ năm sinh",
    "<PERSON>ăm sinh",
    "<PERSON>à<PERSON> sinh",
    "Địa chỉ email",
    "Loại giấy tờ",
    "Số giấy tờ",
    "Ngày cấp giấy tờ",
    "Nơi Cấp giấy tờ",
    "<PERSON>ân hàng",
    "Số tài khoản",
    "Người thụ hưởng",
    "Ngành nghề",
    "<PERSON>hu nhập/ tháng",
    "<PERSON><PERSON>ồ<PERSON> thu nhập",
    "Địa chỉ công ty",
    ,
    ,
    ,
    "Địa chỉ thường trú người đại diện",
    ,
    ,
    ,
    "Địa chỉ liên hệ người đại diện",
    ,
    ,
    ,
    "Ghi chú",
  ];

  readonly HEADER_ROW_NUMBER = 3;

  readonly ROW_KEYS_MAPPING = [
    ,
    "{company.name}",
    "{company.shortName}",
    "{personalInfo.phone}",
    "{taxCode}",
    "{company.issueDate}",
    "{company.issueLocation}",
    "{personalInfo.name}",
    "{personalInfo.position}",
    "{info.onlyYear}",
    "{info.birthdayYear}",
    "{info.birthday}",
    "{personalInfo.email}",
    "{identities.0.type}",
    "{identities.0.value}",
    "{identities.0.date}",
    "{identities.0.place}",
    "{bankInfo.0.name}",
    "{bankInfo.0.accountNumber}",
    "{bankInfo.0.beneficiary}",
    "{personalInfo.job}",
    "{personalInfo.income}",
    "{personalInfo.incomeSource}",
    "{company.address.province.name}",
    "{company.address.district.name}",
    "{company.address.ward.name}",
    "{company.address.address}",
    "{info.rootAddress.province.name}",
    "{info.rootAddress.district.name}",
    "{info.rootAddress.ward.name}",
    "{info.rootAddress.address}",
    "{info.address.province.name}",
    "{info.address.district.name}",
    "{info.address.ward.name}",
    "{info.address.address}",
    "{takeNote}",
  ];

  readonly START_VALUE_ROW_AT = 7;

  readonly ERROR_TEMPLATE_FILE_NAME = "KHDN-error-template.xlsx";

  readonly START_VALUE_ROW_AT_ERROR_TEMPLATE = 7;

  readonly ERROR_COLUMN_INDEX = 37;

  readonly HEADER_KEYS_MAPPING = {
    "company.name": "Tên công ty *",
    "company.shortName": "Tên ngắn",
    "personalInfo.phone": "Số điện thoại *",
    "taxCode": "Mã số thuế",
    "company.issueDate": "Ngày cấp mã số thuế",
    "company.issueLocation": "Nơi cấp mã số thuế",
    "personalInfo.name": "Người đại diện *",
    "personalInfo.position": "Chức vụ",
    "info.onlyYear": "Chỉ năm sinh",
    "info.birthdayYear": "Năm sinh",
    "info.birthday": "Ngày sinh",
    "personalInfo.email": "Địa chỉ email",
    "identities.0.type": "Loại giấy tờ",
    "identities.0.value": "Số giấy tờ",
    "identities.0.date": "Ngày cấp",
    "identities.0.place": "Nơi Cấp",
    "bankInfo.0.name": "Ngân hàng",
    "bankInfo.0.accountNumber": "Số tài khoản",
    "bankInfo.0.beneficiary": "Người thụ hưởng",
    "personalInfo.job": "Ngành nghề",
    "personalInfo.income": "Thu nhập/ tháng",
    "personalInfo.incomeSource": "Nguồn thu nhập",
    "company.address.province": "Tỉnh/ Thành phố",
    "company.address.district": "Quận/ Huyện",
    "company.address.ward": "Phường/ Xã",
    "company.address.address": "Địa chỉ công ty",
    "info.rootAdress.province": "Tỉnh/ Thành phố",
    "info.rootAdress.district": "Quận/ Huyện",
    "info.rootAdress.ward": "Phường/ Xã",
    "info.rootAdress.address": "Địa chỉ thường trú người đại diện",
    "info.address.province": "Tỉnh/ Thành phố",
    "info.address.district": "Quận/ Huyện",
    "info.address.ward": "Phường/ Xã",
    "info.address.address": "Địa chỉ liên hệ người đại diện",
    "takeNote": "Ghi chú",
  };
}