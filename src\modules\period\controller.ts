import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Delete,
  UseInterceptors,
  UseGuards,
  Query,
  Param,
} from "@nestjs/common";
import { IUserResquest } from "../shared/services/user/user-by-id.interface";
import { ApiBearerAuth, ApiUseTags } from "@nestjs/swagger";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
import { PeriodService } from "./service";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { CreatePeriodDto, DeletePeriodDto, UpdatePeriodDto, UpdatePeriodStatusDto } from "./dto/period.dto";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";

@ApiBearerAuth()
@Controller("v1/period")
@ApiUseTags("v1/period")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard)
export class PeriodController {
  constructor(private readonly periodService: PeriodService) {}

  @Roles(PermissionEnum.PERIOD_GET_ALL)
  @Get()
  async getAll(
    @Query() query: any, 
  ) {
    return await this.periodService.getAll(query);
  }

  @Get("gen-period")
  async genPeriod(
    @Query() query: any, 
    @User() user: any
  ) {
    return await this.periodService.genPeriod(user, query);
  }

  @Roles(PermissionEnum.PERIOD_CREATE)
  @Post()
  async createPeriod(
    @User() user: IUserResquest,
    @Body() dto: CreatePeriodDto
  ) {
    return this.periodService.createPeriod(dto, user?.id);
  }

  @Roles(PermissionEnum.PERIOD_UPDATE)
  @Put()
  async updatePeriod(
    @User() user: IUserResquest,
    @Body() dto: UpdatePeriodDto,
  ) {
    return this.periodService.updatePeriod(dto, user?.id);
  }

  @Roles(PermissionEnum.PERIOD_UPDATE_STATUS)
  @Put('update-status')
  async updateStatus(
    @User() user: IUserResquest,
    @Body() dto: UpdatePeriodStatusDto
  ) {
    return this.periodService.updateStatus(dto, user?.id);
  }

  @Roles(PermissionEnum.PERIOD_DELETE)
  @Delete()
  async deletePeriod(
    @Body() dto: DeletePeriodDto,
    @User() user: IUserResquest
  ) {
    return this.periodService.deletePeriod(dto, user?.id);
  }

  @Roles(PermissionEnum.PERIOD_GET)
  @Get("/:id")
  async get(@Param() query: any) {
    return await this.periodService.get(query);
  }
}
