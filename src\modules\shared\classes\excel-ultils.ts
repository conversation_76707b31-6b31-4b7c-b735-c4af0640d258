import { isNullOrUndefined } from 'util';
import ExcelJS = require('exceljs');
import _ = require('lodash');
import { Worksheet } from 'exceljs';

export class ExcelUtils {
  private workbook: any;
  private worksheet: Worksheet;

  constructor() {

    this.workbook = new ExcelJS.Workbook();

  }
  public async loadFile(filename: string) {
    await this.workbook.xlsx.readFile(filename);
    this.worksheet = this.workbook.getWorksheet(1);
  }

  // insert row data to table name
  public insertDataTable(rows: any[], tablename: string, removefirstrow: boolean): number {
    let lastRow = 0;
    try {
      const table = this.worksheet.getTable(tablename);

      if (table) {

        // remove first rows
        if (removefirstrow) {
          table.removeRows(0, 1);
        }

        if (!_.isEmpty(rows)) {
          // append new row to bottom of table
          for (let i = 0; i < rows.length; i++) {
            const element = rows[i] as Array<any>;
            element[0] = i + 1;
            table.addRow(element, undefined);
            lastRow = i;
          }
        }
        // commit the table changes into the sheet
        table.commit();

      }

    } catch (ex) {
      console.log('Error: ', ex.message);
    }
    return lastRow;

  }
  // set cell value
  public setCellValue(value: any, cellname: string) {
    let cell = this.worksheet.getCell(cellname);
    // Modify/Add individual cell
    if (cell) {
      cell.value = value;
    }
    return cell;
  }

  // get bufer 
  public async getbuffer() {
    return await this.workbook.xlsx.writeBuffer();
  }

  // Save file to S3
  public async saveToS3() {

    // workbook


    // write to a new buffer
    const buffer = await this.workbook.xlsx.writeBuffer();

  }
  // Save file to S3
  public async saveToFile(filename: string) {
    await this.workbook.xlsx.writeFile(filename);
  }

  // set duplicate Cell value
  public duplicateCell(value: any, srcCell: string, dstCell: string) {
    const cSrc = this.worksheet.getCell(srcCell);
    // Modify/Add individual cell
    const cDst = this.worksheet.getCell(dstCell);

    if (cSrc && cDst) {
      // Set the new value
      cDst.value = value;

      // Copy all style properties
      cDst.style = cSrc.style;

      // Copy number format
      cDst.numFmt = cSrc.numFmt;
    }
  }

  // set merge Cell value
  public mergeCell(srcCell: string, dstCell: string) {
    this.worksheet.mergeCells(`${srcCell}:${dstCell}`);
  }

  public getRow(row: number) {
    return this.worksheet.getRow(row);
  }

  public getColumn(column: number | string) {
    return this.worksheet.getColumn(column);
  }

  public getCellValue(cell) {
    return this.worksheet.getCell(cell).value;
  }

  addRow(rows: any[]) {
    let lastRow = 0;
    for (const row of rows) {
      row[0] = lastRow + 1;
      const rowval = this.worksheet.addRow(row)
        .eachCell((cell, colNumber) => {
          cell.style = {
            alignment: { vertical: 'middle', horizontal: 'center' }, border: {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            }
          };
        });

      lastRow++;
    }
    return lastRow;
  }
  insertRow(rows: any[], lastRow = 0) {
    for (const row of rows) {
      row[0] = lastRow + 1;
      const rowval = this.worksheet.insertRow(lastRow, row)
        .eachCell((cell, colNumber) => {
          // console.log('cell', cell);
          cell.style = {
            alignment: { vertical: 'middle', horizontal: 'center' }, border: {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            }
          };
        });

      lastRow++;
    }
    return lastRow;
  }

  public async rehighlightAsterisks(workbook) {// Process each worksheet
    workbook.eachSheet((worksheet) => {
      // Define header rows range
      const headerRowsCount = 7; // Modify based on your template

      // Only process header rows
      for (let rowNumber = 1; rowNumber <= headerRowsCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);
        row.eachCell((cell) => {
          if (cell.value && typeof cell.value === 'string' && cell.value.includes('*')) {

            const text = cell.value;
            const parts = text.split('*');

            // Create rich text elements
            const richTextParts = [];
            parts.forEach((part, index) => {
              // Add normal text
              if (part) {
                richTextParts.push({
                  text: part,
                  font: { ...cell.style.font } // Keep original font style
                });
              }
              // Add red asterisk (except after the last part)
              if (index < parts.length - 1) {
                richTextParts.push({
                  text: '*',
                  font: {
                    ...cell.style.font,
                    color: { argb: 'FFFF0000' }, // Red color
                    bold: true
                  }
                });
              }
            });

            // Set the rich text value
            cell.value = {
              richText: richTextParts
            };
          }
        });
      }
    });

    return await workbook.xlsx.writeBuffer();
  }

}