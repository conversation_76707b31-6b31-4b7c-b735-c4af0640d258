import * as mongoose from 'mongoose';
import uuid = require('uuid');

const Itemschema = new mongoose.Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  path: { type: String, required: true },
  sizeFile: { type: Number, default: 0 },
}, { _id: false });

export const DepositContractDocumentFolderSchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  depositContractId: { type: String, index: true },
  type: { type: String, index: true },
  name: { type: String, index: true },
  path: { type: String },
  sizeFile: { type: Number, default: 0 },
  items: {
    type: [Itemschema],
    default: []
  },
  softDelete: { type: Boolean, default: false },
  createdDate: { type: Date, default: () => new Date() },
  modifiedDate: { type: Date, default: () => new Date() },
  createdBy: { type: String },
  modifiedBy: { type: String },
}, { _id: false, minimize: true });

DepositContractDocumentFolderSchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
