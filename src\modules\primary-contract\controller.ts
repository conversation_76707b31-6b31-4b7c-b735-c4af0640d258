import {
  Controller,
  Post,
  Body,
  Headers,
  UseInterceptors,
  Put,
  Delete,
  Param,
  UploadedFiles,
  Get,
  Query,
  Res,
  BadRequestException,
  NotFoundException,
  HttpCode,
} from "@nestjs/common";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { Action } from "../shared/enum/action.enum";
import { ValidationPipe } from "@nestjs/common";
import {
  HandoverPrimaryContractDto,
  CreatePrimaryContractDto,
  PrimaryContractStatusDto,
  SendPrimaryContractDeliveryNotifyDto,
  UpdateInterestCalculationDto,
  UpdateManyPrimaryContract,
  UpdatePrimaryContractDeliveryDateDto,
  UpdatePrimaryContractDto,
  UpdatePrimaryContractFileDto,
  UpdateShowReceiptDto,
  AssignDebtCollectorDto,
  DeassignDebtCollectorDto,
  ResendEmailDto,
  InteractionDto,
  UpdateDepositConfirmDto,
} from "./dto/primary-contract.dto";
import { UseGuards } from "@nestjs/common";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { FilesInterceptor } from "@nestjs/platform-express";
import { PrimaryContractService } from "./service";
import { BaseService, ErrorService, JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
import { FileGenerationService } from "./file-generation.service";
import { ScheduleQueryRepository } from "../schedule.queryside/repository/schedule.query.repository";
import { StaticAssetService } from "../config/static-asset.service";
import { join } from "path";
import { existsSync, unlinkSync } from "fs";
import { CommonUtils } from "../shared/classes/class-utils";
import { ErrorConst } from "../shared/constant/error.const";
import { IPrimaryContractDocument } from "./interfaces/document.interface";
import * as _ from "lodash";
import { ApiUseTags } from "@nestjs/swagger";
import moment = require("moment");
import { HttpStatusCode } from "axios";

@Controller("v2/primary-contract")
@ApiUseTags("PrimaryContract")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard)
export class PrimaryContractController extends BaseService {
  private actionName: string = Action.NOTIFY;
  private resSuccess = { success: true };
  constructor(
    private readonly primaryContractService: PrimaryContractService,
    private readonly fileGenerationService: FileGenerationService,
    private readonly scheduleQueryRepository: ScheduleQueryRepository,
    private readonly staticAssetService: StaticAssetService,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get()
  async getContract(@User() user: any, @Query() query: any) {
    query.createdBy = user.id;
    // let isSeeAll: boolean = user.roles.includes(
    //   PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    // );
   
    return await this.primaryContractService.getContract(user, query, true);
  }

  @Get("download")
  async downloadContract(@User() user: any, @Query() query: any, @Res() res) {
    query.createdBy = user.id;
    let isSeeAll: boolean = user.roles.includes(
      PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    );
    const data = await this.primaryContractService.getContract(
      user,
      query,
      isSeeAll
    );
    const fileName = "Danh_sach_contract" + new Date().getTime();
    await this.fileGenerationService.exportContract(user, fileName, data);
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      `${fileName}.xlsx`
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(`${fileName}.xlsx`, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }

  @Get("download/:id")
  async downloadContractById(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any,
    @Headers() headers,
    // @Res() res
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    // let url = null;
    // const scheduleId = data?.policyPayment?.schedule?.id;
    // const schedule: any = scheduleId
    //   ? await this.scheduleQueryRepository.findOne({ id: scheduleId })
    //   : null;
    // if (schedule && schedule.templates && schedule.templates.length) {
    //   url = schedule.templates[0].absoluteUrl;
    // } else if (_.get(project, "setting.templateFileContract", null)) {
    //   const file = project.setting.templateFileContract.find(
    //     (item) => item.type === query.type
    //   );
    //   url = file ? file.file.absoluteUrl : null;
    // }
    // if (!url) {
    //   throw new NotFoundException("File template not found");
    // }
    return await this.primaryContractService.previewForm(data, headers);

    // const fileName = "contract" + id + new Date().getTime();

    // await this.fileGenerationService.downloadContractById(
    //   user,
    //   fileName,
    //   data,
    //   url,
    //   fileName
    // );
    // const filePath = join(
    //   this.staticAssetService.getUploadFolderPath(),
    //   `${fileName}.docx`
    // );
    // const isFileExisted = existsSync(filePath);
    // if (isFileExisted === false) {
    //   throw new NotFoundException("File not found");
    // }
    // res.sendFile(`${fileName}.docx`, {
    //   root: this.staticAssetService.getUploadFolderPath(),
    // });
    // setTimeout(() => {
    //   unlinkSync(filePath);
    // }, 5000);
    // return true;
  }

  @Get("download/:id/confirmPayment")
  async confirmPayment(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any,
    @Res() res
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    const project = await this.primaryContractService.getProjectById(
      data.primaryTransaction.project.id
    );
    let url = _.get(project, "setting.templateFileHasStatus", []).find(
      (e) =>
        e.stage &&
        e.stage.includes(parseInt(query.stage)) &&
        e.fileId === query.fileId
    )?.file?.absoluteUrl;
    if (!url) {
      throw new NotFoundException("File template not found");
    }

    const fileName = "contract" + id + new Date().getTime();

    await this.fileGenerationService.downloadContractById(
      user,
      fileName,
      data,
      url,
      fileName
    );
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      `${fileName}.docx`
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(`${fileName}.docx`, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }

  @Get("download/:id/historyPayment")
  async historyPayment(
    @User() user: any,
    @Param("id") id: string) {
    const data = await this.primaryContractService.getContractById(user, id);
    let url = null;
    if (
      data?.policyPayment &&
      data?.policyPayment?.schedule &&
      data?.policyPayment?.schedule.id
    ) {
      const scheduleId = data?.policyPayment?.schedule?.id;
      const schedule: any = scheduleId
        ? await this.scheduleQueryRepository.findOne({ id: scheduleId })
        : null;
      if (schedule && schedule.templateFiles && schedule.templateFiles.length) {
        url = schedule.templateFiles[0].absoluteUrl;
      }
    } else if (
      data?.policyPayment &&
      data?.policyPayment?.schedule &&
      !data?.policyPayment?.schedule.id
    ) {
      const schedule = data?.policyPayment?.schedule;
      if (schedule && schedule.templateFiles && schedule.templateFiles.length) {
        url = schedule.templateFiles[0].absoluteUrl;
      }
    }
    if (!url) {
      throw new NotFoundException("File template not found");
    }

    const fileName = "Tien_Do_Thanh_Toan";
    const fileNameUnique =
      "Tien_Do_Thanh_Toan_" + id + "_" + new Date().getTime();

    const result = await this.fileGenerationService.downloadContractById(
      user,
      fileNameUnique,
      data,
      url,
      fileName,
      true,
      true
    );
    return result;
  }

  @Get("/contract/download-query")
  async downloadTicketByQuery(
    @User() user: any,
    @Query() query: any,
    @Res() res
  ) {
    query.createdBy = user.id;
    let isSeeAll: boolean = user.roles.includes(
      PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    );
    return await this.primaryContractService.printTicketByQuery(
      user,
      query,
      res,
      isSeeAll
    );
  }

  @Get("/getContractReminder")
  findContractReminder() {
    return this.primaryContractService.getContractReminder();
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ALL)
  @Get("/getDepositContract")
  async findDepositForPurchaseContract(@User() user: any, @Query() query: any) {
    // let isSeeAll: boolean = user.roles.includes(
    //   PermissionEnum.PRIMARY_CONTRACT_GET_ALL
    // );
    return await this.primaryContractService.getDepositForPurchaseContract(
      user,
      query,
      true
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_EMPLOYEE_F1)
  @Get("/employee-f1/:id")
  async getAllF1Employee(
    @Param("id") id: string,
    @Query() query: any
  ): Promise<any> {
    return await this.primaryContractService.getAllF1Employee(id, query);
  }

  @Get("/debtReportContract/projects/dropdown")
  async getDropdownProjects(@User() userLogged, @Query() query: any): Promise<any> {
    return await this.primaryContractService.getDropdownProjects(userLogged, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_EMP)
  @Get("/debtReportContract")
  async getDebtReportContract(@User() user: any, @Query() query: any) {
    return await this.primaryContractService.getDebtReportContract(user, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_DEBT_COLLECTOR_ASSIGN)
  @Put("/debtReportContract/assign")
  async assignDebtCollector(@User() user: any, @Body() body: AssignDebtCollectorDto) {
    return await this.primaryContractService.assignDebtCollector(user, body);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_DEBT_COLLECTOR_ASSIGN)
  @Put("/debtReportContract/deassign")
  async deassignDebtCollector(@User() user: any, @Body() body: DeassignDebtCollectorDto) {
    return await this.primaryContractService.deassignDebtCollector(user, body);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_EXPORT_DEBT_REPORT)
  @Get("/debtReportContract/export")
  async exportDebtReportContract(@User() user: any, @Query() query: any, @Res() res) {
    // check xem user login có được cấu hình template thống kê công nợ không
    // Lấy data từ db
    let data = await this.primaryContractService.getDebtReportContract(user, query);
    if (data?.length === 0) {
      return res.json(this.getResponse('COME0002'));
    }

    data = data.map((item, idx) => {
      item.stt = idx + 1;
      item.receiptDate = item.receiptDate?.length > 0 ? item.receiptDate.toString() : '';
      item.countPaymentDueDate = item.countPaymentDueDate ? item.countPaymentDueDate.toString() : '';
      return item;
    });

    // generate debt report file
    const now = moment();
    const date = now.format('YYYYMMDD');
    const time = now.format('HHmm');
    const fileName = `CongNo_${date}_${time}.xlsx`;
    // đẩy data vào file
    await this.fileGenerationService.generateDebtReport(data, fileName);

    // download file
    const filePath = join(this.staticAssetService.getUploadFolderPath(), fileName);
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(fileName, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);

    return this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_RESEND_EMAIL_DEBT_REPORT)
  @Put("/debtReportContract/resendEmail")
  async resendEmailDebtReport(@User() user: any, @Body() body: ResendEmailDto) {
    return await this.primaryContractService.resendEmail(user, body);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_EXPORT_DEBT_REPORT)
  @Put("/debtReportContract/callRecord")
  async addCallRecord(@User() user: any, @Body() body: InteractionDto) {
    return await this.primaryContractService.addInteractHistory(user, body, true);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_EXPORT_DEBT_REPORT)
  @Put("/debtReportContract/addNote")
  async addNote(@User() user: any, @Body() body: InteractionDto) {
    return await this.primaryContractService.addInteractHistory(user, body);
  }

  @Get("downloadInterestCalculation/:contractId")
  async downloadInterestCalculation(
    @User() user: any,
    @Param("contractId") id: string,
    @Query() query: any,
    @Res() res
  ) {
    const data = await this.primaryContractService.getContractById(user, id);
    let projectInfo: any = {};
    if (
      data &&
      data.primaryTransaction &&
      data.primaryTransaction.project &&
      data.primaryTransaction.project.id
    ) {
      projectInfo = await this.primaryContractService.getProjectById(
        data.primaryTransaction.project.id
      );
    }
    let url = this.staticAssetService.getTemplateFileDownload(
      "Template_Interest_Calculation.docx"
    );
    if (!url) {
      throw new NotFoundException("File template not found");
    }
    let interestCalculation: any = {};
    if (query.interestId) {
      interestCalculation = data.interestCalculations.find(
        (interset) => interset.id === query.interestId
      );
    }
    interestCalculation.customerName =
      data?.primaryTransaction?.customer?.personalInfo?.name;
    interestCalculation.address =
      data?.primaryTransaction?.customer?.info?.address?.fullAddress;
    interestCalculation.productCode =
      data?.primaryTransaction?.propertyUnit?.code;
    interestCalculation.projectName = data?.primaryTransaction?.project?.name;
    interestCalculation.contractName = data?.name;
    interestCalculation.signedDate = data?.signedDate;
    interestCalculation.companyName = projectInfo?.investor;
    interestCalculation.banks = projectInfo?.banks;
    const fileName =
      "interestCalculation" + query.interestId + new Date().getTime() + ".docx";

    await this.fileGenerationService.generateAndSaveInterestCalculationFile(
      user.name,
      interestCalculation,
      url,
      fileName
    );
    const filePath = join(
      this.staticAssetService.getUploadFolderPath(),
      fileName
    );
    const isFileExisted = existsSync(filePath);
    if (isFileExisted === false) {
      throw new NotFoundException("File not found");
    }
    res.sendFile(fileName, {
      root: this.staticAssetService.getUploadFolderPath(),
    });
    setTimeout(() => {
      unlinkSync(filePath);
    }, 5000);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ID)
  @Get("byPropertyUnitId/:propertyUnitId")
  findByUnitId(
    @User() user: any,
    @Param("propertyUnitId") propertyUnitId: string
  ): Promise<IPrimaryContractDocument> {
    if (!propertyUnitId) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.getContractByPropertyUnitId(
      user,
      propertyUnitId
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/getByDiscountPolicy/ids")
  async findContractByDiscountPolicyByIds(
    @User() user: any,
    @Query("ids") ids: string
  ): Promise<IPrimaryContractDocument[]> {
    if (!ids) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "discount-policy",
          "ID",
          "null"
        ),
      });
    }
    const arrId = ids.split(",");
    return await Promise.all(
      arrId.map((id) =>
        this.primaryContractService.getContractByDiscountPolicy(user, id)
      )
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/getByDiscountPolicy/:id")
  findContractByDiscountPolicy(
    @User() user: any,
    @Param("id") id: string
  ): Promise<IPrimaryContractDocument> {
    if (!id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "discount-policy",
          "ID",
          "null"
        ),
      });
    }
    return this.primaryContractService.getContractByDiscountPolicy(user, id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ID)
  @Get("calculateInterest/:id")
  calculateInterestById(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any
  ): Promise<any> {
    if (!id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.calculateInterestById(user, id, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/getByCareCustomer/:id")
  getByCareCustomer(
    @Param("id") id: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getByCustomer({ id: id }, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/handover/:handoverId")
  getHandoverByProject(
    @Param("handoverId") handoverId: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getHandoverByProject(handoverId, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/handover/report/:handoverId")
  reportHandoverByProject(
    @Param("handoverId") handoverId: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.reportHandoverByProject(
      handoverId,
      query
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET)
  @Get("/handover-by-status/:handoverId")
  getHandoverByStatus(
    @Param("handoverId") handoverId: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    return this.primaryContractService.getHandoverByStatus(handoverId, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_CREATE)
  @Post()
  async createPrimaryContract(
    @User() user,
    @Body() dto: CreatePrimaryContractDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.createContract(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_CREATE)
  @Post("purchase")
  async createPurchaseContract(
    @User() user,
    @Body(new ValidationPipe()) dto: any,
    @Headers() headers: any,
  ) {
    return await this.primaryContractService.createPurchaseContract(
      user,
      dto,
      headers
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put("purchase")
  async updatePurchaseContract(
    @User() user,
    @Body(new ValidationPipe()) dto: any,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.updatePurchaseContract(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_REQUEST_APPROVED)
  @Post("requestApproveContract")
  async requestApproveContract(
    @User() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.requestApproveContract(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_APPROVED)
  @HttpCode(HttpStatusCode.Ok)
  @Post("approveContract")
  async approveContract(
    @User() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.approveContract(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_APPROVED)
  @Post("send-sap")
  async sendSap(
    @Body(new ValidationPipe()) dto: UpdateDepositConfirmDto,
  ) {
    return await this.primaryContractService.sendSap(
      dto,
    );
  }
  @Post("approvePurchaseContract")
  async approvePurchaseContract(
    @User() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.approvePurchaseContract(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Post("updateManyPrimaryContract")
  async updateManyPrimaryContract(
    @User() user,
    @Body() dto: UpdateManyPrimaryContract,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.updateManyPrimaryContract(
      user,
      dto,
      this.actionName
    );
  }

  @Put("approveInterestCalculation")
  async approveInterestCalculation(
    @User() user: any,
    @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return (
      (await this.primaryContractService.approveInterestCalculation(
        user,
        dto,
        this.actionName
      )) || this.resSuccess
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put()
  async update(
    @User() user,
    @Body() dto: UpdatePrimaryContractDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.updateContract(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put("files")
  async updateFiles(
    @User() user,
    @Body(new ValidationPipe()) dto: UpdatePrimaryContractFileDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.updateContractFiles(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put("deliveryDate")
  async updateDeliveryDate(
    @User() user,
    @Body(new ValidationPipe()) dto: UpdatePrimaryContractDeliveryDateDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.updateDeliveryDate(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put("interestCalculation")
  async updateInterestCalculation(
    @User() user,
    @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto,
  ) {
    return (
      (await this.primaryContractService.updateInterestCalculation(
        dto,
      )) || this.resSuccess
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_DELETE)
  @Delete(":id")
  async delete(
    @User() user,
    @Param("id") id: string,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.deleteContract(
      user,
      id,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_DELETE)
  @Put("deleteInterestCalculation")
  async deleteInterestCalculation(
    @User() user,
    @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return (
      (await this.primaryContractService.deleteInterestCalculation(
        user,
        dto,
        this.actionName
      )) || this.resSuccess
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put("sendDeliveryNotify")
  async sendDeliveryNotify(
    @User() user,
    @Body(new ValidationPipe()) dto: SendPrimaryContractDeliveryNotifyDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return await this.primaryContractService.sendDeliveryNotify(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_UPDATE)
  @Put("updateShowReceipt")
  async showReceipt(
    @Body(new ValidationPipe()) dto: UpdateShowReceiptDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    await this.primaryContractService.updateShowReceipt(dto);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_IMPORT)
  @UseInterceptors(FilesInterceptor("files"))
  @Post("/import")
  async importPropertyPrimary(
    @UploadedFiles() files,
    @User() user,
    @Body() dto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    await this.primaryContractService.importFiles(
      user,
      dto,
      files,
      this.actionName
    );
    return this.resSuccess;
  }

  @Put("deliveryConfirm")
  async handover(
    @User() user,
    @Body(new ValidationPipe()) dto: HandoverPrimaryContractDto,
    @Headers("act") actionName?: string
  ) {
    this.actionName = actionName || this.actionName;
    return (
      (await this.primaryContractService.handover(
        user,
        dto,
        this.actionName
      )) || this.resSuccess
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ID)
  @Get("interest/:contractId")
  getInterest(
    @Param("contractId") contractId: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    if (!contractId) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.getInterest(contractId, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ID)
  @Get("/:id/list-interest-calculations")
  listInterestCalculations(
    @User() user: any,
    @Param("id") id: string,
    @Query() query: any
  ): Promise<IPrimaryContractDocument> {
    if (!id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.listInterestCalculations(user, id, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.PRIMARY_CONTRACT_GET_ID)
  @Get("/:id")
  findById(
    @User() user: any,
    @Param("id") id: string
  ): Promise<IPrimaryContractDocument> {
    if (!id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", "null"),
      });
    }
    return this.primaryContractService.getContractById(user, id);
  }
}
