import { Document } from 'mongoose';

interface Bank {
  bankName: string;
  id: string;
}

export interface IInternalOrgchart {
  id: string;
  code: string;
  nameVN: string;
  nameEN: string;
  level: number;
  status: number;
  lineManager: string;
  managerId: string;
  source: string;
  bank: Bank;
}
export interface IInternalOrgchartDocument extends Document, IInternalOrgchart {
  id: string;
  name: string;
  bpID: string;
}
