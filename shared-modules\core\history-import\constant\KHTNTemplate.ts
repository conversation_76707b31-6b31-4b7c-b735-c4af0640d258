import { XLSXTemplateConst } from "./XLSXTemplateConst";
export class KHTNTemplate extends XLSXTemplateConst {
  readonly HEADER_REQUIRED_ROW = [
    "STT",
    "<PERSON>ọ và tên *",
    "<PERSON><PERSON><PERSON> ngắn",
    "<PERSON><PERSON> điện thoại *",
    "Giớ<PERSON> tính *",
    "Chỉ năm sinh",
    "<PERSON>ăm sinh",
    "<PERSON><PERSON><PERSON> sinh",
    "Địa chỉ email",
    "Mã số thuế ",
    "Loại giấy tờ",
    "Số giấy tờ",
    "<PERSON><PERSON><PERSON> cấp",
    "<PERSON>ơi Cấp",
    "<PERSON><PERSON> hàng",
    "<PERSON><PERSON> tài khoản",
    "Người thụ hưởng",
    "<PERSON><PERSON><PERSON> nghề",
    "Thu nhập/ tháng",
    "<PERSON>uồn thu nhập",
    "Tình trạng hôn nhân",
    "Địa chỉ thường trú",
    ,
    ,
    ,
    "Địa chỉ liên hệ",
    ,
    ,
    ,
    "<PERSON><PERSON> chú",
  ];

  readonly HEADER_ROW_NUMBER = 3;

  readonly ROW_KEYS_MAPPING = [
    ,
    "{personalInfo.name}",
    "{personalInfo.shortName}",
    "{personalInfo.phone}",
    "{info.gender}",
    "{info.onlyYear}",
    "{info.birthdayYear}",
    "{info.birthday}",
    "{personalInfo.email}",
    "{taxCode}",
    "{personalInfo.identities.0.type}",
    "{personalInfo.identities.0.value}",
    "{personalInfo.identities.0.date}",
    "{personalInfo.identities.0.place}",
    "{bankInfo.0.name}",
    "{bankInfo.0.accountNumber}",
    "{bankInfo.0.beneficiary}",
    "{personalInfo.job}",
    "{personalInfo.income}",
    "{personalInfo.incomeSource}",
    "{personalInfo.relationshipStatus}",
    "{info.rootAddress.province.name}",
    "{info.rootAddress.district.name}",
    "{info.rootAddress.ward.name}",
    "{info.rootAddress.address}",
    "{info.address.province.name}",
    "{info.address.district.name}",
    "{info.address.ward.name}",
    "{info.address.address}",
    "{takeNote}",
  ];

  readonly HEADER_KEYS_MAPPING = {
    "personalInfo.name": "Họ và tên",
    "personalInfo.shortName": "Tên ngắn",
    "personalInfo.phone": "Số điện thoại",
    "info.gender": "Giới tính",
    "info.onlyYear": "Chỉ năm sinh",
    "info.birthdayYear": "Năm sinh",
    "info.birthday": "Ngày sinh",
    "personalInfo.email": "Địa chỉ email",
    "taxCode": "Mã số thuế ",
    "personalInfo.identities.0.type": "Loại giấy tờ",
    "personalInfo.identities.0.value": "Số giấy tờ",
    "personalInfo.identities.0.date": "Ngày cấp",
    "personalInfo.identities.0.place": "Nơi Cấp",
    "bankInfo.0.name": "Ngân hàng",
    "bankInfo.0.accountNumber": "Số tài khoản",
    "bankInfo.0.beneficiary": "Người thụ hưởng",
    "personalInfo.job": "Ngành nghề",
    "personalInfo.income": "Thu nhập/ tháng",
    "personalInfo.incomeSource": "Nguồn thu nhập",
    "personalInfo.relationshipStatus": "Tình trạng hôn nhân",
    "info.rootAddress.province": "Tỉnh/ Thành phố",
    "info.rootAddress.district": "Quận/ Huyện",
    "info.rootAddress.ward": "Phường/ Xã",
    "info.rootAddress.address": "Địa chỉ thường trú",
    "info.address.province": "Tỉnh/ Thành phố",
    "info.address.district": "Quận/ Huyện",
    "info.address.ward": "Phường/ Xã",
    "info.address.address": "Địa chỉ liên hệ",
    "takeNote": "Ghi chú",
  }

  readonly START_VALUE_ROW_AT = 7;

  readonly ERROR_TEMPLATE_FILE_NAME = "KHTN-error-template.xlsx";

  readonly START_VALUE_ROW_AT_ERROR_TEMPLATE = 7;

  readonly ERROR_COLUMN_INDEX = 31;
}