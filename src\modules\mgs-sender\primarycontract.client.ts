import { MsgSenderService } from './mgs.sender.service';
import { ClientProxy } from '@nestjs/microservices';
import { Inject, Injectable } from '@nestjs/common';
import { QueueConst } from '../../../shared-modules';

@Injectable()
export class PrimaryContractClient {
    constructor(
        @Inject(QueueConst.PRIMARY_CONTRACT_QUEUE) private readonly client: ClientProxy,
        private readonly msgSenderService: MsgSenderService
    ) { }

    sendData(content: any, cmdPattern) {
        this.msgSenderService.subscribe(this.client, content, cmdPattern);
    }
    sendDataPromise(content: any, pattern: string) {
        return this.msgSenderService.promise(this.client, content, pattern);
    }
}
