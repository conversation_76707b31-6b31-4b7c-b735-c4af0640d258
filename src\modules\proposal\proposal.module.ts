import { Modu<PERSON> } from "@nestjs/common";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { SharedModule } from "../../../shared-modules";
import { QueryProviders } from "../proposal.queryside/providers/query.cqrs.providers";
import { ProposalController } from "./controllers/proposal.controller";
import { ProposalService } from "./services/proposal.service";
import { ProposalRepository } from "./repositories/proposal.repository";
import { CodeGenerateModule } from "../code-generate/module";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { CqrsModule } from "@nestjs/cqrs";
import { LoggerModule } from "../logger/logger.module";
import { PrimaryContractModule } from "../primary-contract/module";
@Module({
  imports: [CqrsModule, QueryDatabaseModule, MgsSenderModule, LoggerModule, SharedModule, CodeGenerateModule, PrimaryContractModule],
  controllers: [ProposalController],
  providers: [...QueryProviders, ProposalService, ProposalRepository],
  exports: [ProposalService, ProposalRepository],
})
export class ProposalModule { }
