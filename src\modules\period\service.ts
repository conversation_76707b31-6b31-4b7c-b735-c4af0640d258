import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import {
  AwesomeLogger,
  BaseService,
  CmdPatternConst,
  ErrorService,
} from "../../../shared-modules";
import { PeriodRepository } from "./repository/period.repository";
import _ = require('lodash');
import { StsClient } from "../mgs-sender/sts.client";
import { ReAssignAccountInforForMultiple, ReAssignAccountInforForSingle } from "../shared/classes/class-utils";

@Injectable()
export class PeriodService extends BaseService {
  private readonly context = PeriodService.name;
  private readonly isActive = 1; /// trạng thái của cấu hình kỳ là đang hoạt động
  private readonly logger = new AwesomeLogger(PeriodService.name);
  constructor(
    private readonly queryRepository: PeriodRepository,
    public readonly errorService: ErrorService,
    public readonly stsClient: StsClient
  ) {
    super(errorService);
  }

  async getAll(query: any) {
    const dataQuery: any = {};
    const page: number = Number(query.page) || 1;
    const pageSize: number = Number(query.pageSize) || 10;
    // Check search
    if (query.search) {
      dataQuery.$or = [
        { name: { $regex: new RegExp(query.search), $options: "i" } },
        { periodStartDate: Number(query.search) },
        { periodEndDate: Number(query.search) },
      ];
    }

    if (query.createdBy) {
      dataQuery.createdBy = { $in: query.createdBy.split(',') };
    }

    if (query.isActive) {
      dataQuery.isActive = Number(query.isActive);
    }

    if (query.forProject === 'true') {
      dataQuery.$and = [
        { projects: { $exists: true } },
        { projects: { $ne: [] } },
      ];
    }
    else if (query.forProject === 'false'){
      dataQuery.$and = [
        { projects: [] },
      ];
    }

    if (query.projects && query.projects !== "") {
      dataQuery["projects.id"] = { $in: query.projects.split(",") }
    }
    else if (query.projects === ""){
      dataQuery.projects = [];
    }

    //filter startDate && endDate
    if (query.startDate || query.endDate) {
      const startDate = new Date(query.startDate || new Date("1900-01-01T00:00:00Z")); // Mặc định startDate là ngày quá khứ cũ nhất nếu không có
      const endDate = new Date(query.endDate || new Date()); // Mặc định endDate là ngày hôm nay nếu không có
    
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
    
      dataQuery.createdDate = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    const result: any = await this.queryRepository.findAll(page, pageSize, dataQuery);
    let clones = JSON.parse(JSON.stringify(result));
    clones.rows = await ReAssignAccountInforForMultiple(result?.rows, this.stsClient);
    return clones
  }

  async genPeriod(user: any, query: any) {
    query.isActive = this.isActive;
    query.projects = query.projects || "";
    let data: any = await this.getAll(query);
    if (!data || !data.rows[0] || !query.year){
      query.isActive = this.isActive;
      query.projects = "";
      data = await this.getAll(query);
      if (!data || !data.rows[0] || !query.year) 
        throw new HttpException(
          { errorCode: "PERIOD0008" },
          HttpStatus.OK
        );
    }
    
    return this.GenPeriodArray(data.rows[0], query.year);
  }

  async get(query: any) {
    if (!query.name && !query.id) return "";
    let period = await this.queryRepository.findOne(query);
    if (!period) {
      return this.getResponse("PERIOD0001");
    }
    period = await ReAssignAccountInforForSingle(period, this.stsClient);
    return period;
  }

  async createPeriod(record: any, userId: string) {
    this.logger.info(this.context, "createPeriod");

    if (record.isActive === this.isActive){
      const activeRecord = await this.queryRepository.findOne({
        isActive: this.isActive,
        projects: record.projects?.length > 0 ? { $elemMatch: { id: { $in: record.projects.map(item => item.id) } } } : [],
      })

      if (activeRecord){
        const sameProject = activeRecord.projects.filter(x => record.projects.map(item => item.id).includes(x.id));
        if (sameProject)
          return this.getResponse("PERIOD0006", {projects: sameProject});
        else return this.getResponse("PERIOD0002");
      }
    }

    record.createdBy = userId;
    record.modifiedBy = userId;
    record.periodEndDate = record.periodStartDate - 1 == 0 ? 31 : record.periodStartDate - 1;
    return await this.queryRepository.create(record);
  }

  async updatePeriod(dto: any, userId: string) {
    this.logger.info(this.context, "updatePeriodMain");

    const id = dto.id;
    const period = await this.queryRepository.findOne({ id });
    if (!period) {
      return this.getResponse("PERIOD0001");
    }

    if (dto.isActive == null || dto.isActive == undefined) dto.isActive = period.isActive;

    if (dto.isActive === this.isActive){
      const activeRecord = await this.queryRepository.findOne({
        isActive: this.isActive,
        projects: dto.projects?.length > 0 ? { $elemMatch: { id: { $in: dto.projects.map(item => item.id) } } } : [],
        id: { $ne: id }
      })

      if (activeRecord){
        const sameProject = activeRecord.projects.filter(x => dto.projects.map(item => item.id).includes(x.id));
        if (sameProject)
          return this.getResponse("PERIOD0006", {projects: sameProject});
        else return this.getResponse("PERIOD0002");
      }
    }

    dto.modifiedBy = userId;
    dto.modifiedDate = Date.now();
    dto.periodEndDate = dto.periodStartDate ? (dto.periodStartDate - 1 == 0 ? 31 : dto.periodStartDate - 1) : period.periodEndDate;
    return await this.queryRepository.updateOne(dto);
  }

  async updateStatus(dto: any, userId: string) {
    this.logger.info(this.context, "updatePeriodMain");

    const id = dto.id;
    const period = await this.queryRepository.findOne({ id });
    if (!period) {
      return this.getResponse("PERIOD0001");
    }

    if (dto.isActive === this.isActive){
      const activeRecord = await this.queryRepository.findOne({
        isActive: this.isActive,
        projects: period.projects?.length > 0 ? { $elemMatch: { id: { $in: period.projects.map(item => item.id) } } } : [],
        id: { $ne: id }
      })

      if (activeRecord){
        const sameProject = activeRecord.projects.filter(x => period.projects.map(item => item.id).includes(x.id));
        if (sameProject)
          return this.getResponse("PERIOD0006", {projects: sameProject});
        else return this.getResponse("PERIOD0002");
      }
    }

    period.modifiedBy = userId;
    period.isActive = dto.isActive;
    return await this.queryRepository.updateOne(period);
  }

  async deletePeriod(dto: any, userId: string) {
    this.logger.info(this.context, "deleteConfig");
    var id = dto.id;

    const period = await this.queryRepository.findOne({ id });

    if (!period) {
      return this.getResponse("PERIOD0001");
    }

    if (period.isActive === 1 && period.projects.length === 0) {
      return this.getResponse("PERIOD0009");
    }

    period.softDelete = true;
    period.reasonDelete = dto.reasonDelete;
    await this.queryRepository.updateOne(period);
    return this.getResponse(0);
  }

  GenPeriodArray(dto, currentYear) {
    const periods = [];
    for (let month = 0; month < 12; month++) {
      const start = this.getValidDate(currentYear, month, dto.periodStartDate, true);
      const end = dto.periodStartDate < dto.periodEndDate ? this.getValidDate(currentYear, month, dto.periodEndDate) : this.getValidDate(currentYear, month + 1, dto.periodEndDate);

      // // Format yyyy-mm-dd theo UTC
      // const format = (date) =>
      //   `${date.getUTCFullYear()}-${this.pad(date.getUTCMonth() + 1)}-${this.pad(date.getUTCDate())}`;

      periods.push({
        periodName: `Kỳ ${month + 1}`,
        periodStartDate: start,
        periodEndDate: end,
      });
    }
    return periods;
  }

  // getMax = true thì truyền vào là 31-2 sẽ thành 1-3 còn false thì 31-2 sẽ thành 28-2 or 29/2
  getValidDate(year, month, day, getMax: boolean = false) { 
    year = Number(year);
    const lastDayOfMonth = new Date(Date.UTC(year, month + 1, 0)).getUTCDate();
    const safeDay = Math.min(day, lastDayOfMonth);
    if (getMax){
      if (!this.isValidDate(day, month + 1, year)){
        return new Date(Date.UTC(year, month + 1, 1));
      }
    }
    return new Date(Date.UTC(year, month, safeDay)); 
  }

  isValidDate(day, month, year) {
    const date = new Date(year, month - 1, day);
    return (
      date.getFullYear() === year &&
      date.getMonth() === month - 1 &&
      date.getDate() === day
    );
  }

  pad(num) {
    return num.toString().padStart(2, '0');
  }
}
