import { Document } from 'mongoose';
import { IDebtCommissionList } from '../../shared/services/debt-commission-list/interfaces/debt-commission-list.interface';
import { IDebtCommissionRate } from '../../shared/services/debt-commission-policy/interfaces/commission-policy.interface';

export interface IDebtCommissionListDocument extends Document, IDebtCommissionList {
  id: string;
  name: string;
  description: string;
}

export interface IDebtPolicy {
  code: string;
  rate: string;
  isProgressive: boolean;
  isVAT: boolean;
  type: string; // all/base/interest
  listRate: IDebtCommissionRate[];    // Tỷ lệ hoa hồng
}
