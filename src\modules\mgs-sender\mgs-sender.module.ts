import { TransactionClient } from "./transaction.client";
import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ClientProxyFactory, Transport } from "@nestjs/microservices";
import { LoggerClient } from "./logger.client";
import { MsgSenderService } from "./mgs.sender.service";
import { NotifierClient } from "./notifier.client";
import { StsClient } from "./sts.client";
import { MailerClient } from "./mailer.client";
import { NotificationClient } from "./notification.client";
import { EmployeeClient } from "./employee.client";
import { CustomerClient } from "./customer.client";
import { PropertyClient } from "./property.client";
import { OrgchartClient } from "./orgchart.client";
import { CareClient } from "./care.client";
import { UploadClient } from "./uploader.client";
import { SyncErpClient } from "./syncErp.client";
import { SocialClient } from "./social.client";
import { ConfigService } from "@nestjs/config";
import { ProposalClient } from "./proposal.client";
import { CommissionClient } from "./commission.client";
import { DemandClient } from "./demand.client";
import { PrimaryContractClient } from "./primarycontract.client";
import { QueueConst } from "../../../shared-modules";

const queues = [
  { queue: QueueConst.LOGGER_QUEUE, client: LoggerClient },
  { queue: QueueConst.NOTIFIER_QUEUE, client: NotifierClient },
  { queue: QueueConst.NOTIFICATION_QUEUE, client: NotificationClient },
  { queue: QueueConst.STS_QUEUE, client: StsClient },
  { queue: QueueConst.MAILER_QUEUE, client: MailerClient },
  { queue: QueueConst.EMPLOYEE_QUEUE, client: EmployeeClient },
  { queue: QueueConst.CUSTOMER_QUEUE, client: CustomerClient },
  { queue: QueueConst.PROPERTY_QUEUE, client: PropertyClient },
  { queue: QueueConst.ORGCHART_QUEUE, client: OrgchartClient },
  { queue: QueueConst.CARE_QUEUE, client: CareClient },
  { queue: QueueConst.TRANSACTION_QUEUE, client: TransactionClient },
  { queue: QueueConst.UPLOADER_QUEUE, client: UploadClient },
  { queue: QueueConst.SYNCERP_QUEUE, client: SyncErpClient },
  { queue: QueueConst.SOCIAL_QUEUE, client: SocialClient },
  { queue: QueueConst.PROPOSAL_QUEUE, client: ProposalClient },
  { queue: QueueConst.COMMISSION_QUEUE, client: CommissionClient },
  { queue: QueueConst.DEMAND_QUEUE, client: DemandClient },
  { queue: QueueConst.PRIMARY_CONTRACT_QUEUE, client: PrimaryContractClient },
];

@Module({
  imports: [],
  providers: [
    MsgSenderService,
    ...queues.map(({ queue, client }) => {
      return {
        provide: queue,
        useFactory: async (configService: ConfigService) => {
          console.log(
            "test load config in msg-sender ",
            configService.get("RABBITMQ_URL")
          );
          return ClientProxyFactory.create({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get("RABBITMQ_URL")],
              queue: queue,
              queueOptions: { durable: false },
            },
          });
        },
        inject: [ConfigService],
      };
    }),
    ...queues.map(({ client }) => {
      return client;
    }),
  ],
  exports: [
    MsgSenderService,
    ...queues.map(({ client }) => {
      return client;
    }),
  ],
})
export class MgsSenderModule {}
