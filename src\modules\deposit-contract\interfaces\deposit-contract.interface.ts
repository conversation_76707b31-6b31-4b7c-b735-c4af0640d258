import { Document } from 'mongoose';

export interface IDepositContract extends Document {
  id: string;
  code: string;
  projectId: string;
  orgchart: any;
  orgchartPartnerId: string;
  bank: any;
  depositSignedDate: Date;
  depositForm: string;
  depositTime: number;
  depositAmount: number;
  salePolicyId: string;
  distributionChannel: number;
  division: number;
  softDelete: boolean;
  createdDate: Date;
  modifiedDate: Date;
  createdBy: string;
  modifiedBy: string;
  propertyUnitIds: any[];
  installments: any[];
  propertyQuantity: number;
  propertyUnits: any;
}