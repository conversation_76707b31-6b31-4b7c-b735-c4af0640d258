import { Document } from "mongoose";
import { IPrimaryContract } from "../../shared/services/primary-contract/interfaces/interface";

export interface IPrimaryContractDocument extends Document, IPrimaryContract {
  paymentPercent: number;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  purchase: any;
  id: string;
  description: string;

  primaryTransaction: any;
  // có đang chậm thanh toán hay không
  hasInterest: boolean;
  // thông tin lãi tạm tính
  interest: any;
  tradeHistory: [];
  eapStatus: string;
  eapCode: string;
  interestReductionAmountEap: number;
  interestReductionReason: string;
}
