import { InterestCalculationStatusEnum } from "src/modules/shared/enum/status.enum";
import { IBaseInterface } from "../../../interfaces/base.interface";

export interface IPrimaryContract extends IBaseInterface {
  id: string;
  name: string;
  code: string;
  status: string;
  eapStatus: string;
  interestExemptionReason: string;
  restoreinterestReason: string;
  startDate: Date;
  expiredDate: Date;
  signedDate:  Date;
  primaryTransactionId: string;
  policyPaymentId: string;
  policyDiscountId: string;
  policyPayment: any;
  proposal: any;
  feeAdvanceAmount: number;
  interestCalculations: IInterestCalculation[];
  type: string;
  description: string;
  deposit: any;
  companyInformation: any;
  liquidation: any;
  liquidate: Date;
  debtCollector: Object;
  assignHistories: any;
  assignStatus: string;
  debtHistory: any;
  callRecords: any;
  notes: any;
  isInterestExemption: boolean;
}
export interface IInterestCalculation extends IBaseInterface {
  id: string;
  name: string;
  title: string;
  code: string;
  status: InterestCalculationStatusEnum;
  principalAmount: number; // tiền gốc
  interestRate: number; // lãi suất
  interestAmount: number; // số tiền lãi
  interestAmountTransferred: number; // số tiền lãi đã thanh toán
  interestReductionAmount: number; // số tiền giảm lãi
  remainingAmount: number; // số tiền còn lại
  createdDate: Date; // ngày tạo phiếu
  startDate: Date; // ngày bắt đầu tính lãi
  endDate: Date; // tính lãi đến ngày
  dayOfLatePayment: number; // số ngày trễ hạn thanh toán
  installmentName: string; // đợt thanh toán
  receipts: any; // thông tin phiếu thu
  description: string;
  eapStatus: string;
  eapCode: string;
  proposal: any;
  interestReductionAmountEap: number;
  interestReductionReason: string;
}

export interface IPrimaryContractResponse {
  id?: string;
  name: string;
}

export interface IPrimaryContractByRequest {
  posId: string;
}
