import { Model } from "mongoose";
import { Inject, Injectable } from "@nestjs/common";
import _ = require("lodash");
import { CommonConst } from "../../shared/constant/index";
import {
  ContractEnum,
  StatusContractEnum,
  StatusEnum,
} from "../../shared/enum/primary-contract.enum";
import { CommonUtils } from "../../shared/classes/class-utils";
import { IPrimaryContractDocument } from "../interfaces/document.interface";
import { QueryAggregateModel } from "../models/query-aggregate.model";
import { checkPermission } from "../../shared/utils/checkPermission";
import { PermissionEnum } from "../../shared/enum/permission.enum";
import { BaseService, CmdPatternConst, ErrorService } from "../../../../shared-modules";
import { EmployeeClient } from "../../mgs-sender/employee.client";
import { OrgchartClient } from "../../mgs-sender/orgchart.client";
import { InterestCalculationStatusEnum } from "../../shared/enum/status.enum";
import { ProposalStatusEnum } from "../../shared/enum/proposal.enum";

@Injectable()
export class PrimaryContractRepository extends BaseService {

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IPrimaryContractDocument>,
    private readonly employeeClient: EmployeeClient,
    private readonly orgchartClient: OrgchartClient,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }

  async findAll(): Promise<IPrimaryContractDocument[]> {
    return await this.readModel
      .find()
      .exec()
      .then((result) => {
        return result;
      });
  }

  /**
   * find One
   */
  async findOne(query, projection?: any): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .findOne(query, projection)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async findAggregateModelById(id: string): Promise<QueryAggregateModel> {
    return await this.readModel
      .findOne({ id })
      .exec()
      .then((response) => {
        return new QueryAggregateModel(id);
      })
      .catch((exp) => {
        return exp;
      });
  }

  async create(readmodel): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .create(readmodel)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async update(model): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .update({ id: model.id }, model)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async updateOne(query, model): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .updateOne(query, model)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  
  async updateDeleteFieldPurchase(id: string): Promise<IPrimaryContractDocument> {
    try {
      const updated = await this.readModel.findByIdAndUpdate(
        id,
        { $unset: { purchase: "" } },
        { new: true } // trả về document sau khi cập nhật
      );

      return updated;
    } catch (error) {
      throw error;
    }
  }
  
  async delete(model): Promise<any> {
    return await this.readModel
      .deleteOne({ id: model.id })
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async contractFindAll(
    isPaging: boolean = false,
    isSeeAll,
    query?: any,
    page?,
    pageSize?
  ): Promise<IPrimaryContractDocument[]> {
    let [_query, _queryKeywords]: any = this.getContractQuery(isSeeAll, query);
    let sort: any = {
      createdDate: -1,
    };
    if (query.id) {
      _query = {
        id: query.id,
      };
    }

    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort) || {
        createdDate: -1,
      };
    }

    const aggregate: any[] = [
      {
        $match: _queryKeywords,
      },
      {
        $match: _query,
      },
      { $sort: sort },
    ];
    if (!_.isEmpty(query.status)) {
      if (Array.isArray(query.status)) {
        aggregate.push({
          $match: { status: { $in: query.status } },
        });
      } else {
        aggregate.push({
          $match: { status: query.status },
        });
      }
    }
    if (!_.isEmpty(query["projectIds"])) {
      if (query.projectIds.includes(",")) {
        query.projectIds = query.projectIds.split(",");
      }

      if (Array.isArray(query.projectIds)) {
        aggregate.push({
          $match: {
            "primaryTransaction.project.id": { $in: query.projectIds },
          },
        });
      } else {
        aggregate.push({
          $match: { "primaryTransaction.project.id": query.projectIds },
        });
      }
    }

    if (!_.isEmpty(query["type"])) {
      query.type = query["type"].split(",");
      aggregate.push({
        $match: { type: { $in: query.type } },
      });

      // Với HĐC chỉ hiển thị những HĐ chưa chuyển sang HDMB
      if (
        query.type == ContractEnum.DEPOSIT &&
        (!_.isEmpty(query["searchInLiquidation"]) || !_.isEmpty(query["havePurchase"]))
      ) {
        aggregate.push({
          $match: { purchase: null },
        });
      }
    }

    if (!_.isEmpty(query["isTransferred"])) {
      aggregate.push({
        $match: { isTransferred: true },
      });
    }

    if (!_.isEmpty(query.startDate) && !_.isEmpty(query.endDate)) {
      const startDate: any = new Date(+query.startDate);
      const endDate: any = new Date(+query.endDate);
      aggregate.push({
        $match: {
          createdDate: {
            $exists: true,
            $gte: new Date(`'${startDate}'`),
            $lte: new Date(`'${endDate}'`),
          },
        },
      });
    }
    if (!_.isEmpty(query._fields)) {
      const fields = query._fields.split(",");
      const project: any = {};
      fields.forEach((f) => {
        project[f.trim()] = 1;
      });
      // project._id = 0;
      aggregate.push({ $project: project });
    }
    if (isPaging) {
      aggregate.push({
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [
            {
              $count: "count",
            },
          ],
        },
      });
    }

    return await this.readModel
      .aggregate(aggregate)
      .allowDiskUse(true)
      .exec()
      .then((res) => {
        return res;
      });
  }

  private getContractQuery(isSeeAll, query: any = {}): any {
    const match: any = {};
    const matchKeywords: any = {};
    if (query) {
      if (!_.isEmpty(query["primaryTransaction.project.id"])) {
        match["primaryTransaction.project.id"] =
          query["primaryTransaction.project.id"];
      }
      if (!_.isEmpty(query["salesProgramId"])) {
        match["primaryTransaction.propertyUnit.salesProgram.id"] =
          query["salesProgramId"];
      }
      if (!_.isEmpty(query.search)) {
        matchKeywords.$or = [
          { code: { $regex: query.search, $options: "i" } },
          { name: { $regex: query.search, $options: "i" } },
          // {
          //   "primaryTransaction.bookingTicketCode": {
          //     $regex: query.search,
          //     $options: "i",
          //   },
          // },
          // {
          //   "primaryTransaction.escrowTicketCode": {
          //     $regex: query.search,
          //     $options: "i",
          //   },
          // },
          // {
          //   "primaryTransaction.propertyUnit.code": {
          //     $regex: query.search,
          //     $options: "i",
          //   },
          // },
          // { createdDate: { $regex: query.search, $options: "i" } },
          // { status: { $regex: query.search, $options: "i" } },
          // {
          //   "primaryTransaction.customer.personalInfo.name": {
          //     $regex: query.search,
          //     $options: "i",
          //   },
          // },
          // { "policyPayment.code": { $regex: query.search, $options: "i" } },
          // { "policyPayment.name": { $regex: query.search, $options: "i" } },
          // { "policyDiscounts.code": { $regex: query.search, $options: "i" } },
          // { "policyDiscounts.name": { $regex: query.search, $options: "i" } },
        ];
      }

      if (!_.isEmpty(query["status"])) {
        if (query.status.includes(",")) {
          query.status = query.status.split(",");
        }

        if (Array.isArray(query.status)) {
          match["status"] = { $in: query.status };
        } else {
          match["status"] = query["status"];
        }
      } else {
        match["status"] = { $ne: StatusEnum.CANCELLED };
      }

      if (!isSeeAll && query.queryEmpIDs && query.queryEmpIDs.length > 0) {
        match["createdBy"] = { $in: query.queryEmpIDs };
      }

      if (query.forAccountant) {
        // cho màn hình tạo phiếu thu, loại bỏ các HĐC đã chuyển thành HĐMB
        match["purchase"] = { $exists: false };
      }

      if (query.isCheckProject) {
        match["primaryTransaction.project.id"] =
          query["primaryTransaction.project.id"];
      }

      if (query.discountPolicyIds) {
        match["policyDiscounts.id"] = {
          $in: query.discountPolicyIds.split(",") || [],
        };
      }

      if (query.paymentPolicyIds) {
        match["policyPayment.id"] = {
          $in: query.paymentPolicyIds.split(",") || [],
        };
      }
    }

    return [match, matchKeywords];
  }
  protected transformSort(paramSort?: String) {
    let sort: any = paramSort;
    if (_.isString(sort)) {
      sort = sort.split(",");
    }
    if (Array.isArray(sort)) {
      let sortObj = {};
      sort.forEach((s) => {
        if (s.startsWith("-")) sortObj[s.slice(1)] = -1;
        else sortObj[s] = 1;
      });
      return sortObj;
    }

    return sort;
  }

  async findContractByDiscountPolicy(
    discountPolicyId: string
  ): Promise<IPrimaryContractDocument[]> {
    const sort: any = {
      createdDate: -1,
    };
    const _query = {
      "policyDiscount.id": discountPolicyId,
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.WAITING,
          StatusContractEnum.ACCOUNTANT_WAITING,
        ],
      },
    };

    const aggregate: any[] = [
      {
        $match: _query,
      },
      { $sort: sort },
    ];
    return await this.readModel
      .aggregate(aggregate)
      .allowDiskUse(true)
      .exec()
      .then((res) => {
        return res;
      });
  }

  async findPrimaryContractReminder(
    query: any = {}
  ): Promise<IPrimaryContractDocument[]> {
    const sort: any = {
      createdDate: -1,
    };
    const aggregate: any[] = [...query, { $sort: sort }];
    return await this.readModel
      .aggregate(aggregate)
      .allowDiskUse(true)
      .exec()
      .then((res) => {
        return res;
      });
  }

  async findPrimaryContractForCustomer(
    query: any = {},
    isPaging = false,
    page = 1,
    pageSize = 10
  ): Promise<IPrimaryContractDocument[]> {
    let sort: any = {
      createdDate: -1,
    };
    if (!_.isEmpty(query.sort)) {
      sort = CommonUtils.transformSort(query.sort) || {
        createdDate: -1,
      };
      delete query.sort;
    }
    let project: any = { _id: 0 };
    if (!_.isEmpty(query._fields)) {
      const fields = query._fields.split(",");
      fields.forEach((f) => {
        project[f.trim()] = 1;
      });
      delete query._fields;
    }

    const aggregate: any[] = [
      { $match: query },
      { $sort: sort },
      { $project: project },
    ];

    if (isPaging) {
      aggregate.push({
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [
            {
              $count: "count",
            },
          ],
        },
      });
    }

    return await this.readModel
      .aggregate(aggregate)
      .allowDiskUse(true)
      .exec()
      .then((res) => {
        return res;
      });
  }

  async findCustomerIdentitiesPrimaryContractByProjectId(query) {
    const aggregate: any[] = [
      { $match: query },
      {
        $unwind: {
          path: "$customer.identities",
        },
      },
      {
        $unwind: {
          path: "$customer2.identities",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: 0,
          identties: {
            $addToSet: "$customer.identities.value",
          },
          identties2: {
            $addToSet: "$customer2.identities.value",
          },
        },
      },
      {
        $project: {
          identties: {
            $concatArrays: ["$identties", "$identties2"],
          },
        },
      },
    ];

    return await this.readModel
      .aggregate(aggregate)
      .allowDiskUse(true)
      .exec()
      .then((res) => {
        return res;
      });
  }

  async updateMany(query, model): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .updateMany(query, model)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  updatePropertyUnits(units: any[]) {
    return this.readModel.bulkWrite(
      units.map((unit) => ({
        updateMany: {
          filter: { "primaryTransaction.propertyUnit.id": unit.id },
          update: {
            $set: {
              "primaryTransaction.propertyUnit": unit,
            },
          },
        },
      }))
    );
  }

  async find(query, project = {}): Promise<IPrimaryContractDocument[]> {
    return await this.readModel
      .find(query, project)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async findPrimaryContractsAggregate(aggregate: any[]) {
    return this.readModel.aggregate(aggregate).exec();
  }

  async findDebtReportContract(user, query: any) {
    let aggregate: any[] = [];
    let sort: any = {
      createdDate: -1,
    };
    let match: any = {};

    const emp = await this.employeeClient.sendDataPromise({ where: { "account.id": user.id } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
    if (checkPermission(user.roles, PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_ALL)) {
      const org = await this.orgchartClient.sendDataPromise({ code: emp[0].orgCode }, CmdPatternConst.ORGCHART.LISTENER.GET_BY_QUERY);
      if (checkPermission(user.roles, PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_EMP)) {
        match.$or = [
          { 'debtCollector.accountId': user.id },
          { 'orgCode': org.code }
        ];
      } else {
        match['debtCollector.accountId'] = user.id;
      }
    } else if (checkPermission(user.roles, PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_EMP)) {
      match['debtCollector.accountId'] = user.id;
    } else {
      return this.getResponse('COME0003');
    }

    if (query.projectIds) {
      match["primaryTransaction.project.id"] = { $in: query.projectIds.split(',') };
    }

    // check hợp đồng
    aggregate.push({ $match: match });

    // lấy list các đợt trong mỗi hợp đồng ra
    aggregate.push({
      $unwind: {
        path: "$policyPayment.schedule.installments",
        preserveNullAndEmptyArrays: true,
      },
    });

    // filter interest calculations
    aggregate.push({
      $addFields: {
        matchingCalculation: {
          $let: {
            vars: {
              filtered: {
                $filter: {
                  input: { $ifNull: ["$interestCalculations", []] },
                  as: "calc",
                  cond: { $eq: ["$$calc.installmentName", "$policyPayment.schedule.installments.name"] }
                }
              }
            },
            in: {
              $cond: {
                if: { $gt: [{ $size: "$$filtered" }, 0] },
                then: { $arrayElemAt: ["$$filtered", 0] },
                else: null
              }
            }
          }
        }
      }
    });

    // display field data
    aggregate.push({
      $project: {
        id: "$id",
        contractName: "$name",
        project: "$primaryTransaction.project.name",
        contractType: "$type",
        propertyType: "$primaryTransaction.propertyUnit.apartmentType",
        propertyCode: "$primaryTransaction.propertyUnit.code",
        customerCode: "$primaryTransaction.customer.code",
        customerName: "$primaryTransaction.customer.personalInfo.name",
        debtCollector: "$debtCollector.name",
        installmentName: "$policyPayment.schedule.installments.name",
        paymentDueDate: {
          $dateFromString: {
            dateString: "$policyPayment.schedule.installments.paymentDueDate",
            onError: null,
            onNull: null
          }
        },
        receiptDate: {
          $dateFromString: {
            dateString: { $arrayElemAt: ["$policyPayment.schedule.installments.receipts.receiptDate", -1] },
            onError: null,
            onNull: null
          }
        }, // ngày thanh toán
        debtReminderStatus: { // trạng thái nhắc nợ
          $let: {
            vars: {
              paymentDueDate: {
                $dateFromString: {
                  dateString: "$policyPayment.schedule.installments.paymentDueDate",
                  onError: null,
                  onNull: null
                }
              },
              reminderDate: {
                $subtract: [
                  {
                    $dateFromString: {
                      dateString: "$policyPayment.schedule.installments.paymentDueDate",
                      onError: null,
                      onNull: null
                    }
                  },
                  { $multiply: ["$primaryTransaction.project.setting.dueDateReminderDays", 24 * 60 * 60 * 1000] }
                ]
              },
              delayDate: {
                $add: [
                  {
                    $dateFromString: {
                      dateString: "$policyPayment.schedule.installments.paymentDueDate",
                      onError: null,
                      onNull: null
                    }
                  },
                  {
                    $multiply: [{
                      $convert: {
                        input: "$primaryTransaction.project.setting.delayDay",
                        to: "double",
                        onError: 0,  // Use 0 if conversion fails
                        onNull: 0    // Use 0 if the input is null
                      }
                    }, 24 * 60 * 60 * 1000]
                  }
                ]
              },
              today: new Date()
            },
            in: {
              $cond: {
                if: { $eq: ["$$paymentDueDate", null] },
                then: "Chưa đến hạn",
                else: {
                  $switch: {
                    branches: [
                      {
                        case: {
                          $and: [
                            { $gt: ["$$today", "$$paymentDueDate"] },
                            { $lte: ["$$today", "$$delayDate"] },
                          ],
                        },
                        then: "Đang ân hạn"
                      },
                      {
                        case: {
                          $and: [
                            { $lt: ["$$today", "$$paymentDueDate"] },
                            { $gte: ["$$today", "$$reminderDate"] },
                          ],
                        },
                        then: "Sắp đến hạn"
                      },
                      {
                        case: {
                          $eq: [
                            { $dateToString: { format: "%Y-%m-%d", date: "$$paymentDueDate" } },
                            { $dateToString: { format: "%Y-%m-%d", date: "$$today" } }]
                        },
                        then: "Đến hạn"
                      },
                      {
                        case: { $gt: ["$$today", "$$paymentDueDate"] },
                        then: "Trễ hạn"
                      }
                    ],
                    default: "Chưa đến hạn"
                  }
                }
              }
            }
          }
        },
        totalDelayDate: { $ifNull: ["$matchingCalculation.totalDelayDate", 0] }, // Số ngày trễ hạn
        debtage: { $ifNull: ["$matchingCalculation.debtage.name", ""] }, // Tuổi nợ
        needTransfer: { $ifNull: ["$matchingCalculation.needTransfer", 0] }, // Công nợ (VNĐ)
        latePaymentFee: { $ifNull: ["$matchingCalculation.latePaymentFee", 0] }, // Phí phạt (VNĐ)
        interestAmount: { $ifNull: ["$matchingCalculation.amount", 0] }, // Lãi phạt (VNĐ)
        totalSettlementAmount: { $ifNull: ["$matchingCalculation.totalSettlementAmount", 0] }, // Tổng tiền cần thanh toán (VNĐ)
        interestAmountTransferred: { $ifNull: ["$matchingCalculation.interestAmountTransferred", 0] }, // Số tiền đã thanh toán (VNĐ)
        paymentStatus: { // trạng thái thanh toán
          $switch: {
            branches: [
              {
                case: { $eq: ["$policyPayment.schedule.installments.totalTransfered", 0] },
                then: "Chưa thanh toán"
              },
              {
                case: { $eq: [{ $subtract: ["$policyPayment.schedule.installments.totalAmount", "$policyPayment.schedule.installments.totalTransfered"] }, 0] },
                then: "Đã thanh toán"
              },
              // {
              //   case: { $lt: ["$policyPayment.schedule.installments.totalTransfered", "$policyPayment.schedule.installments.totalAmount"] },
              //   then: "Đã thanh toán 1 phần"
              // }
            ],
            default: ""
          }
        },
        expiredDate: "$expiredDate", // ngày hết hạn hđ
        isTransferred: "$isTransferred",
        assignStatus: "$assignStatus",
        assignHistories: "$assignHistories",
        debtHistory: "$debtHistory",
        countPaymentDueDate: { // trạng thái nhắc nợ
          $let: {
            vars: {
              today: new Date(),
              dueDate: {
                $dateFromString: {
                  dateString: "$policyPayment.schedule.installments.paymentDueDate",
                  onError: null,
                  onNull: null
                }
              }
            },
            in: {
              $cond: {
                if: {
                  $and: [
                    { $ne: ["$$dueDate", null] },
                    { $gt: ["$$today", "$$dueDate"] }
                  ]
                },
                then: {
                  $ceil: {
                    $divide: [{ $subtract: ["$$today", "$$dueDate"] }, 1000 * 60 * 60 * 24]
                  }
                },
                else: 0
              }
            }
          }
        },

        statusSortOrder: {
          $switch: {
            branches: [
              { case: { $eq: ["$debtReminderStatus", "Trễ hạn"] }, then: 1 },
              { case: { $eq: ["$debtReminderStatus", "Đến hạn"] }, then: 2 },
              { case: { $eq: ["$debtReminderStatus", "Đang ân hạn"] }, then: 3 },
              { case: { $eq: ["$debtReminderStatus", "Sắp đến hạn"] }, then: 4 },
              { case: { $eq: ["$debtReminderStatus", "Chưa đến hạn"] }, then: 5 }
            ],
            default: 6
          }
        },
      },
    });

    // sort by debtReminderStatus,  payment due date
    aggregate.push({ $sort: { statusSortOrder: 1, paymentDueDate: 1 } });

    // Filter options
    let matchFilter: any = {};

    // like
    if (query.search) {
      matchFilter.$or = [
        { contractName: { $regex: query.search, $options: "i" } },
        { propertyCode: { $regex: query.search, $options: "i" } },
        { customerCode: { $regex: query.search, $options: "i" } },
        { customerName: { $regex: query.search, $options: "i" } },
        { debtCollector: { $regex: query.search, $options: "i" } },
      ];
    }

    // tình trạng nhắc nợ
    if (query.debtReminderStatus) {
      matchFilter['debtReminderStatus'] = { $in: query.debtReminderStatus.split(',') };
    }

    // loại sản phẩm
    if (query.propertyTypes) {
      matchFilter['propertyType'] = { $in: query.propertyTypes.split(',') };
    }

    // nv công nợ
    if (query.employees) {
      matchFilter['debtCollector.id'] = { $in: query.employees.split(',') };
    }

    // filter theo thời hạn thanh toán hợp đồng
    let dueStartDate = 0;
    let dueEndDate = 0;
    if (query.dueFrom && query.dueTo) {
      dueStartDate = Number(new Date(query.dueFrom));
      dueEndDate = Number(new Date(query.dueTo)) + 86399999;
      matchFilter["paymentDueDate"] = { $gte: new Date(dueStartDate), $lte: new Date(dueEndDate) };
    } else if (query.dueFrom) {
      dueStartDate = Number(new Date(query.dueFrom));
      matchFilter["paymentDueDate"] = { $gte: new Date(dueStartDate) };
    } else if (query.dueTo) {
      dueEndDate = Number(new Date(query.dueTo)) + 86399999;
      matchFilter["paymentDueDate"] = { $lte: new Date(dueEndDate) };
    }

    // filter theo ngày thanh toán
    let paidStartDate = 0;
    let paidEndDate = 0;
    if (query.paidFrom && query.paidTo) {
      paidStartDate = Number(new Date(query.paidFrom));
      paidEndDate = Number(new Date(query.paidTo)) + 86399999;
      matchFilter["receiptDate"] = { $gte: new Date(paidStartDate), $lte: new Date(paidEndDate) };
    } else if (query.paidFrom) {
      paidStartDate = Number(new Date(query.paidFrom));
      matchFilter["receiptDate"] = { $gte: new Date(paidStartDate) };
    } else if (query.paidTo) {
      paidEndDate = Number(new Date(query.paidTo)) + 86399999;
      matchFilter["receiptDate"] = { $lte: new Date(paidEndDate) };
    }

    console.log('match filter', matchFilter)
    if (matchFilter) {
      aggregate.push({ $match: matchFilter });
    }

    aggregate.push({
      $project: {
        _id: 0,
        statusSortOrder: 0
      }
    });

    console.log('aggregate', JSON.stringify(aggregate));

    if (query.page || query.pageSize) {
      let pageSize: number = parseInt(query["pageSize"]) || 10;
      let page: number = parseInt(query["page"]) || 1;
      aggregate.push({
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          total: [
            {
              $count: "count",
            },
          ],
        },
      });
      

      return this.readModel
        .aggregate(aggregate)
        .allowDiskUse(true)
        .exec()
        .then((result) => {
          const total = result[0].total[0] ? result[0].total[0].count : 0;
          return {
            rows: result[0].rows,
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
        });
    } else {
      return this.readModel
        .aggregate(aggregate)
        .allowDiskUse(true)
        .exec()
        .then((res) => {
          return res;
        });
    }
  }

  async getCommissionTxs(query): Promise<IPrimaryContractDocument[]> {
    const aggs = [];
    aggs.push({
      $match: query,
    });
    aggs.push({
      $project: {
        _id: 0,
        code: 1,
        transactionSuccess: 1,
        transactionState: 1,
        orgCode: 1,
        salesPolicy: 1,
        project: "$primaryTransaction.project",
        propertyUnit: "$primaryTransaction.propertyUnit",
        customer: "$primaryTransaction.customer",
        employee: "$primaryTransaction.employee",
        employeeRole: 1,
        employeeRevenueRate: 1,
      },
    });

    return await this.readModel
      .aggregate(aggs)
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async findOverdueContracts() {
    //Những hợp đồng có policyPayment đã có đợt thanh toán đến hạn
    const contracts = await this.readModel.aggregate([
      {
        $match: {
          policyPayment: { $exists: true, $ne: null },
          'policyPayment.schedule.installments': { $exists: true, $not: { $size: 0 } },
          isInterestExemption: false,
          eapStatus: { $in: [null, ProposalStatusEnum.APPROVED] },
        }
      },
      {
        $addFields: {
          // Filter installments that are overdue (past due date + 1 day)
          overdueInstallments: {
            $filter: {
              input: "$policyPayment.schedule.installments",
              as: "item",
              cond: {
                $and: [
                  {
                    $ne: [
                      {
                        $dateFromString: {
                          dateString: "$$item.paymentDueDate",
                          onError: null,
                          onNull: null
                        }
                      },
                      null
                    ]
                  },
                  {
                    $lt: [
                      {
                        $add: [
                          {
                            $dateFromString: {
                              dateString: "$$item.paymentDueDate",
                              onError: null,
                              onNull: null
                            }
                          },
                          24 * 60 * 60 * 1000 // Add 1 day in milliseconds
                        ]
                      },
                      new Date()
                    ]
                  }
                ]
              }
            }
          }
        }
      },
      {
        $match: {
          "overdueInstallments.0": { $exists: true } // Only keep documents with at least 1 overdue installment
        }
      },
      {
        $addFields: {
          // Get the nearest past installment (most recent overdue)
          nearestPastInstallment: {
            $arrayElemAt: [
              {
                $sortArray: {
                  input: "$overdueInstallments",
                  sortBy: { paymentDueDate: -1 } // Sort by due date descending
                }
              },
              0 // Get first element (most recent)
            ]
          }
        }
      },
      {
        $addFields: {
          // Check if there's already an interest calculation for this installment
          hasInterestCalculation: {
            $in: [
              "$nearestPastInstallment.name",
              {
                $ifNull: [
                  { $map: { input: "$interestCalculations", as: "calc", in: "$$calc.installmentName" } },
                  []
                ]
              }
            ]
          },
          // Check if installment is fully transferred
          isTransferredAll: {
            $lte: [
              "$nearestPastInstallment.totalAmount",
              "$nearestPastInstallment.totalTransfered"
            ]
          }
        }
      },
      {
        $match: {
          nearestPastInstallment: { $exists: true, $ne: null },
          hasInterestCalculation: false,
          isTransferredAll: false
        }
      },
      {
        $project: {
          // Remove temporary fields if you don't need them in the result
          overdueInstallments: 0,
          nearestPastInstallment: 0,
          hasInterestCalculation: 0,
          isTransferredAll: 0
        }
      }]).exec();

    return contracts;
  }

  async findNotOverdueContracts(projectId) {
    //Những hợp đồng có policyPayment chưa tính lãi 
    const contracts = await this.readModel.aggregate([
      {
        $match: {
          policyPayment: { $exists: true, $ne: null },
          'policyPayment.schedule.installments': { $exists: true, $not: { $size: 0 } },
          'interestCalculations.isStartCalcInterest': false,
          'primaryTransaction.project.id': projectId,

        }
      },
    ]).exec();
    return contracts;
  }

  async getAllInterestTickets(user, query): Promise<IPrimaryContractDocument[]> {
    let aggregate: any[] = [];
    let match: any = {};

    // const emp = await this.employeeClient.sendDataPromise({ where: { "account.id": user.id } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
    // if (checkPermission(user.roles, PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_ALL)) {
    //   const org = await this.orgchartClient.sendDataPromise({ code: emp[0].orgCode }, CmdPatternConst.ORGCHART.LISTENER.GET_BY_QUERY);
    //   match['orgCode'] = org.code;
    // } else if (checkPermission(user.roles, PermissionEnum.PRIMARY_CONTRACT_DEBT_REPORT_EMP)) {
    //   match['debtCollector.accountId'] = user.id;
    // } else {
    //   return this.getResponse('COME0003');
    // }

    if (query.projectId) {
      match["primaryTransaction.project.id"] = query.projectId;
    }

    // check hợp đồng
    aggregate.push({ $match: match });

    // lấy list các đợt thanh toán trong mỗi hợp đồng ra
    aggregate.push({
      $unwind: {
        path: "$policyPayment.schedule.installments",
        preserveNullAndEmptyArrays: true,
      },
    });
    aggregate.push({
      $unwind: {
        path: "$interestCalculations",
        preserveNullAndEmptyArrays: true,
      },
    });

    aggregate.push({
      $match: {
        $expr: {
          $eq: ["$interestCalculations.installmentName", "$policyPayment.schedule.installments.name"],
        },
      },
    });

    // filter interest calculations
    // aggregate.push({
    //   $addFields: {
    //     matchingInterestCalculations: {
    //       $filter: {
    //         input: { $ifNull: ["$interestCalculations", []] },
    //         as: "calc",
    //         cond: { $eq: ["$$calc.installmentName", "$policyPayment.schedule.installments.name"] },
    //       },
    //     },
    //   },
    // });

    aggregate.push({
      $addFields: {
        receiptDate: {
          $dateFromString: {
            dateString: "$interestCalculations.endDate",
            onError: null,
            onNull: null
          }
        }
      }
    });

    // display field data
    aggregate.push({
      $project: {
        id: "$id",
        contractCode: "$code",
        contractName: "$name",
        projectId: "$primaryTransaction.project.id",
        projectName: "$primaryTransaction.project.name",
        projectCode: "$primaryTransaction.project.code",
        orgCode: "$orgCode",
        contractType: "$type",
        propertyView1: {
          $let: {
            vars: {
              filteredAttributes: {
                $filter: {
                  input: { $ifNull: ["$primaryTransaction.propertyUnit.attributes", []] },
                  as: "attribute",
                  cond: { $eq: ["$$attribute.attributeName", "View 1"] } // Loại căn hộ/đất nền
                }
              }
            },
            in: {
              $cond: {
                if: { $gt: [{ $size: "$$filteredAttributes" }, 0] },
                then: { $arrayElemAt: ["$$filteredAttributes.value", 0] },
                else: ""
              }
            }
          }
        },
        propertyType: "$primaryTransaction.propertyUnit.type",
        propertyCode: "$primaryTransaction.propertyUnit.code",
        propertyId: "$primaryTransaction.propertyUnit.id",
        customerCode: "$primaryTransaction.customer.code",
        customerName: "$primaryTransaction.customer.personalInfo.name",
        installmentName: "$interestCalculations.installmentName",
        debtage: "$interestCalculations.debtage",
        status: "$interestCalculations.status",
        debtType: "$interestCalculations.debtType", // PROJECT_DEBT/BAD_DEBT
        receiptDate: 1,
        debtCollector: "$debtCollector",
        principalAmount: "$interestCalculations.principalAmount", // nợ gốc kỳ thanh toán
        needTransfer: "$interestCalculations.needTransfer", // nợ phiếu thu
        interestAmount: "$interestCalculations.remainingAmount", // lãi
        latePaymentFee: "$interestCalculations.latePaymentFee", // phí phạt
      }
    });

    // Filter options
    let matchFilter: any = {};
    matchFilter["status"] = InterestCalculationStatusEnum.transfered;

    // filter theo ngày thanh toán
    let fromNum = 0;
    let toNum = 0;
    if (query.periodFrom && query.periodTo) {
      fromNum = Number(new Date(query.periodFrom));
      toNum = Number(new Date(query.periodTo)) + 86399999;
      matchFilter["receiptDate"] = { $gte: new Date(fromNum), $lte: new Date(toNum) };
    } else if (query.periodFrom) {
      fromNum = Number(new Date(query.periodFrom));
      matchFilter["receiptDate"] = { $gte: new Date(fromNum) };
    } else if (query.periodTo) {
      toNum = Number(new Date(query.periodTo)) + 86399999;
      matchFilter["receiptDate"] = { $lte: new Date(toNum) };
    }

    console.log('match filter', matchFilter)
    if (matchFilter) {
      aggregate.push({ $match: matchFilter });
    }

    aggregate.push({
      $project: {
        _id: 0
      }
    });

    console.log('aggregate', aggregate);

    if (query.page || query.pageSize) {
      let pageSize: number = parseInt(query["pageSize"]) || 10;
      let page: number = parseInt(query["page"]) || 1;
      aggregate.push({
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          total: [
            {
              $count: "count",
            },
          ],
        },
      });

      return this.readModel
        .aggregate(aggregate)
        .allowDiskUse(true)
        .exec()
        .then((result) => {
          const total = result[0].total[0] ? result[0].total[0].count : 0;
          return {
            rows: result[0].rows,
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
        });
    } else {
      return this.readModel
        .aggregate(aggregate)
        .allowDiskUse(true)
        .exec()
        .then((res) => {
          return res;
        });
    }
  }

  async validateContract(query: any) {
    let aggregate: any[] = [];
    let match: any = {};

    if (query.id) {
      match["primaryTransaction.project.id"] = query.id;
    }

    // check hợp đồng
    aggregate.push({ $match: match });

    // filter interest calculations
    aggregate.push({
      $addFields: {
        includeInterestInstallments: {
          $filter: {
            input: { $ifNull: ["$policyPayment.schedule.installments", []] },
            as: "installment",
            cond: {
              $or: [
                { $lt: ["$$installment.totalTransfered", "$$installment.totalAmount"] },
                {
                  $and: [
                    {
                      $ne: [
                        {
                          $dateFromString: {
                            dateString: "$$installment.paymentDueDate",
                            onError: null,
                            onNull: null
                          }
                        },
                        null
                      ]
                    },
                    {
                      $gt: [
                        new Date(),
                        {
                          $dateFromString: {
                            dateString: "$$installment.paymentDueDate",
                            onError: null,
                            onNull: null
                          }
                        }
                      ]
                    }
                  ]
                },
              ],
            },
          },
        },
      },
    });

    aggregate.push({
      $match: {
        $expr: {
          $gt: [{ $size: "$includeInterestInstallments" }, 0], // Ensure there is at least one matching installment
        },
      },
    });

    aggregate.push({
      $addFields: {
        filteredInterestCalculations: {
          $filter: {
            input: { $ifNull: ["$interestCalculations", []] }, // Ensure input is always an array
            as: "calc",
            cond: {
              $ne: ["$$calc.status", InterestCalculationStatusEnum.transfered], // Filter out items with status 'TRANSFERED'
            },
          },
        },
      },
    });

    aggregate.push({
      $match: {
        $expr: {
          $gt: [{ $size: "$filteredInterestCalculations" }, 0], // Ensure there is at least one matching interest calculation
        },
      },
    });

    aggregate.push({
      $project: {
        _id: 0,
        id: 1,
        "code": 1,
        "name": 1,
        "installments": "$includeInterestInstallments",
        interestCalculations: "$filteredInterestCalculations"
      },
    });

    return this.readModel
      .aggregate(aggregate)
      .allowDiskUse(true)
      .exec()
      .then((res) => {
        return res;
      });

  }
}
