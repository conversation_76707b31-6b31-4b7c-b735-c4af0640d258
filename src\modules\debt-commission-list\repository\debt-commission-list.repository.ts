import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { CommonConst } from '../../shared/constant/index';
import { AwesomeLogger } from '../../../../shared-modules';
import { IDebtCommissionDocument } from '../../debt-commission/interfaces/document.interface';
import _ = require('lodash');
import { IDebtCommissionListDocument } from '../interfaces/document.interface';

@Injectable()
export class DebtCommissionListRepository {

  private logger = new AwesomeLogger(DebtCommissionListRepository.name);

  constructor(
    @Inject(CommonConst.DEBT_COMMISSION_LIST)
    private readonly readModel: Model<IDebtCommissionListDocument>
  ) { }

  async findAll(): Promise<any> {
    this.logger.info('findAll');
    return await this.readModel.find({}).sort({ level: 1 })
      .exec()
      .then(result => {
        return result;
      });
  }
  async find(query, exceptQuery?): Promise<any[]> {

    return await this.readModel.find(query, exceptQuery)
      .exec()
      .then(result => {
        return result;
      });
  }

  async findByPeriodAndManageCode(period: string, manageCode: string): Promise<any[]> {
    const query = {
      "commission.name": period,
      $or: [
        { "employees.code": manageCode },
        {
          "employees": {
            $elemMatch: {
              "managers": {
                $elemMatch: { "code": manageCode },
              },
            },
          },
        },
      ],
    };
    return await this.readModel.find(query)
      .exec()
      .then(result => {
        return result;
      });
  }

  /**
   * find One
   */
  async findOne(query): Promise<any> {

    return await this.readModel.findOne(query)
      .exec()
      .then(result => {
        return result;
      });
  }
  async findCommissionById(id: string, exceptQuery?): Promise<IDebtCommissionDocument> {
    return await this.readModel.findOne({ id }, exceptQuery)
      .exec()
      .then((response) => {
        return response;
      })
      .catch((exp) => {
        return exp;
      });
  }

  async create(readmodel): Promise<IDebtCommissionDocument> {
    return await this.readModel.create(readmodel)
      .then((response) => {
        console.log('createEvent Commission at query side');
        return response;
      }).catch((error) => {
        console.error('Error createEvent Commission at query side', error);
        return error;
      });
  }


  async update(model): Promise<IDebtCommissionDocument> {

    this.logger.info('update-repository');

    return await this.readModel.updateOne({ id: model.id }, model)
      .then((response) => {
        this.logger.info('updated Commission at query side');
        return response;
      }).catch((error) => {
        this.logger.info('updated error =>', error);
        return error;
      });
  }

  async updateMany(query, updateQuery): Promise<IDebtCommissionDocument> {

    this.logger.info('update-many-repository');

    return await this.readModel.updateMany(query, updateQuery)
      .then((response) => {
        this.logger.info('updated many Commission at query side');
        return response;
      }).catch((error) => {
        this.logger.info('updated error =>', error);
        return error;
      });
  }

  async delete(model): Promise<any> {

    this.logger.info('Delete Commission at query side');
    // console.log(model);

    return await this.readModel.deleteOne({ id: model.id })
      .then((response) => {
        this.logger.info('Deleted Commission at query side');
        console.log(response);
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteMany(model): Promise<any> {

    this.logger.info('Delete many Commission at query side');
    // console.log(model);

    return await this.readModel.deleteMany({ id: model.ids })
      .then((response) => {
        this.logger.info('Deleted Commission at query side');
        console.log(response);
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteByCommissionId(commissionId: string): Promise<any> {
    this.logger.info('Soft delete many Commission by commissionId at query side');
    return await this.readModel.updateMany({ 'commission.id': commissionId }, { softDelete: true })
      .then((response) => {
        this.logger.info('Deleted Commission tx by commissionId at query side');
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteManyByCommissionId(commissionId: string): Promise<any> {
    this.logger.info('Delete many Commission by commissionId at query side');
    return await this.readModel.deleteMany({ 'commission.id': commissionId })
      .then((response) => {
        this.logger.info('Deleted Commission tx by commissionId at query side');
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async listAll(query: any = {}) {
    return await this.readModel.find(query)
      .sort({ index: 1 })
      .exec();
  }
  async listDebtCommissionByUser(userId: any, query: any = {}) {
    let agg: any = [];
    agg = [
      { $match: query },
      { $unwind: '$employees' },
      { $unwind: '$employees.commissions' },
    ];

    // Sort by index first, then by role order
    agg.push({
      $sort: {
        index: 1
      }
    });

    // agg.push({
    //   $sort: { index: 1 }
    // });
    // if (groupProject) {
    //   agg.push({
    //     $group: {
    //       _id: '$project.id',
    //       projectName: { $first: '$project.name' },
    //       items: { $push: '$$ROOT' }
    //     }
    //   });
    // }

    console.log('agg', agg);
    return await this.readModel.aggregate(agg).allowDiskUse(true)
      .exec()
      .then(rs => {
        return rs;
      });
  }

  async count(where: any): Promise<number> {
    return this.readModel.countDocuments(where).exec();
  }

  async findMany(filter: any, lean = false): Promise<any> {
    const { where, projection, sort, skip, limit } = filter;
    const query = this.readModel.find(
      where,
      projection,
      {
        sort,
        skip,
        limit,
      }
    );
    if (lean) return query.lean();
    return query;
  }

  async findAllCommissionPolicyIds(): Promise<string[]> {
    const commissionLists = await this.readModel.find({}, {
      "commissionPolicy.id": 1,
      _id: 0
    });

    const policyIds = commissionLists.map(item => item.commissionPolicy?.id);

    return [...new Set([...policyIds].filter(id => id))];
  }

  async bulkUpdateAdjustmentData(listComm = [], mapTx = {}) {
    const updates = listComm.map((com) => {
      if (com.code && mapTx[com.code]) {
        return {
          updateOne: {
            filter: { code: com.code },
            update: {
              $set: {
                ...com,
                updatedDate: new Date(),
                adjustmentData: mapTx[com.code].adjustmentData
              }
            },
            upsert: true
          }
        };
      }

      return null;
    }).filter(Boolean);

    if (updates.length > 0) {
      await this.readModel.bulkWrite(updates);
    } else {
      console.log("No updates emp to perform.");
    }
  }
}