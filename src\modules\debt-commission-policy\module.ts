import { CodeGenerateModule } from './../code-generate/module';
import { CqrsModule } from '@nestjs/cqrs';
import { Module } from '@nestjs/common';
import { DebtCommissionPolicyController } from './controller';
import { DebtCommissionPolicyService } from './service';
import { EmployeeQuerySideModule } from '../employee.query/module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LoggerModule } from '../logger/logger.module';
import { DebtCommissionPolicyQueryRepository } from './repository/repository';
import { CqrsProviders } from './providers/cqrs.domain.providers';
import { QueryDatabaseModule } from '../database/query/query.database.module';

@Module({
    imports: [
        CqrsModule,
        QueryDatabaseModule,
        MgsSenderModule,
        EmployeeQuerySideModule,
        LoggerModule,
        CodeGenerateModule,
    ],
    controllers: [
        DebtCommissionPolicyController,
    ],
    providers: [
        DebtCommissionPolicyService,
        DebtCommissionPolicyQueryRepository,
        ...CqrsProviders,
    ],
    exports: [
        DebtCommissionPolicyService,
        DebtCommissionPolicyModule,
        DebtCommissionPolicyQueryRepository,
    ]
})
export class DebtCommissionPolicyModule { }