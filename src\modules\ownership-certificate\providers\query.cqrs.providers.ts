import { Connection } from "mongoose";
import { CommonConst } from "../../shared/constant";
import { ownershipCertificateQuerySchema } from "../schemas/ownership-certificate.query.schema";

export const QueryProviders = [
  {
    provide: CommonConst.OWNERSHIP_CETIFICATE_QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) =>
      connection.model(CommonConst.OWNERSHIP_CETIFICATE_COLLECTION, ownershipCertificateQuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
