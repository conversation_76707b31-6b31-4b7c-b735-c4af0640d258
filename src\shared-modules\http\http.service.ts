import { Injectable, HttpService } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class CustomHttpService {
  constructor(private readonly http: HttpService) { }

  get<T>(url: string, config?: any): Observable<T> {
    return this.http.get<T>(url, config).pipe(
      map(response => response.data)
    );
  }

  post<T, R = any>(url: string, body: T, config?: any): Observable<R> {
    return this.http.post<R>(url, body, config).pipe(
      map(response => response.data)
    );
  }

  put<T, R = any>(url: string, body: T, config?: any): Observable<R> {
    return this.http.put<R>(url, body, config).pipe(
      map(response => response.data)
    );
  }

  delete<T>(url: string, config?: any): Observable<T> {
    return this.http.delete<T>(url, config).pipe(
      map(response => response.data)
    );
  }

  //phương thức async sử dụng Promise
  async getAsync<T>(url: string, config?: any): Promise<T> {
    const response = await this.http.get<T>(url, config).toPromise();
    return response.data;
  }

  async postAsync<T, R = any>(url: string, body: T, config?: any): Promise<R> {
    const response = await this.http.post<R>(url, body, config).toPromise();
    return response.data;
  }

  async putAsync<T, R = any>(url: string, body: T, config?: any): Promise<R> {
    const response = await this.http.put<R>(url, body, config).toPromise();
    return response.data;
  }

  async deleteAsync<T>(url: string, config?: any): Promise<T> {
    const response = await this.http.delete<T>(url, config).toPromise();
    return response.data;
  }
}
