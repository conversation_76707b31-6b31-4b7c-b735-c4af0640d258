import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { CommonConst } from '../../shared/constant/index';
import _ = require('lodash');
import { IDebtCommissionListDocument } from '../interfaces/document.interface';
import { AwesomeLogger } from '../../../../shared-modules';

@Injectable()
export class TempDebtCommissionListRepository {

  private logger = new AwesomeLogger(TempDebtCommissionListRepository.name);

  constructor(
    @Inject(CommonConst.TEMP_DEBT_COMMISSION_LIST)
    private readonly readModel: Model<IDebtCommissionListDocument>
  ) { }

  async findAll(): Promise<any> {
    this.logger.info('findAll');
    return await this.readModel.find({}).sort({ level: 1 })
      .exec()
      .then(result => {
        return result;
      });
  }

  async find(query, exceptQuery?): Promise<IDebtCommissionListDocument[]> {

    return await this.readModel.find(query, exceptQuery)
      .exec()
      .then(result => {
        return result;
      });
  }

  async findByPeriodAndManageCode(period: string, manageCode: string): Promise<any[]> {
    const query = {
      "commission.name": period,
      $or: [
        { "employees.code": manageCode },
        {
          "employees": {
            $elemMatch: {
              "managers": {
                $elemMatch: { "code": manageCode },
              },
            },
          },
        },
      ],
    };
    return await this.readModel.find(query)
      .exec()
      .then(result => {
        return result;
      });
  }

  /**
   * find One
   */
  async findOne(query): Promise<any> {

    return await this.readModel.findOne(query)
      .exec()
      .then(result => {
        return result;
      });
  }
  async findCommissionById(id: string, exceptQuery?): Promise<IDebtCommissionListDocument> {
    return await this.readModel.findOne({ id }, exceptQuery)
      .exec()
      .then((response) => {
        return response;
      })
      .catch((exp) => {
        return exp;
      });
  }

  async create(readmodel): Promise<IDebtCommissionListDocument> {
    return await this.readModel.create(readmodel)
      .then((response) => {
        console.log('create commission list at query side');
        return response;
      }).catch((error) => {
        console.error('Error create commission list at query side', error);
        return error;
      });
  }


  async update(model): Promise<IDebtCommissionListDocument> {

    this.logger.info('update-repository');

    return await this.readModel.updateOne({ id: model.id }, model)
      .then((response) => {
        this.logger.info('updated Commission at query side');
        return response;
      }).catch((error) => {
        this.logger.info('updated error =>', error);
        return error;
      });
  }

  async updateMany(query, updateQuery): Promise<IDebtCommissionListDocument> {

    this.logger.info('update-many-repository');

    return await this.readModel.updateMany(query, updateQuery)
      .then((response) => {
        this.logger.info('updated many Commission at query side');
        return response;
      }).catch((error) => {
        this.logger.info('updated error =>', error);
        return error;
      });
  }

  async delete(model): Promise<any> {

    this.logger.info('Delete Commission at query side');
    // console.log(model);

    return await this.readModel.deleteOne({ id: model.id })
      .then((response) => {
        this.logger.info('Deleted Commission at query side');
        console.log(response);
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteMany(model): Promise<any> {

    this.logger.info('Delete many Commission at query side');
    // console.log(model);

    return await this.readModel.deleteMany({ id: model.ids })
      .then((response) => {
        this.logger.info('Deleted Commission at query side');
        console.log(response);
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteByCommissionId(commissionId: string): Promise<any> {
    this.logger.info('Soft delete many Commission by commissionId at query side');
    return await this.readModel.updateMany({ 'commission.id': commissionId }, { softDelete: true })
      .then((response) => {
        this.logger.info('Deleted Commission tx by commissionId at query side');
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteManyByCommissionId(commissionId: string): Promise<any> {
    this.logger.info('Delete many Commission by commissionId at query side');
    return await this.readModel.deleteMany({ 'commission.id': commissionId })
      .then((response) => {
        this.logger.info('Deleted Commission tx by commissionId at query side');
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async listAll(query: any = {}) {
    return await this.readModel.find(query)
      .sort({ index: 1 })
      .exec();
  }
  async listCommissionByUser(userId: any, query: any = {}, _keySearch: any = {}, isSeeAll: boolean = false, groupProject = false) {
    let match: any = [];
    match = [
      {
        $match: query
      },
      { $unwind: '$employees' },
      { $addFields: { empRevenue: { "$sum": "$employees.commissions.revenue" } } },
      { $addFields: { employees: { $concatArrays: [["$employees"], "$employees.managers"] } } },
      { $unwind: '$employees' },
      { $unwind: '$employees.commissions' },
      {
        $match: _keySearch
      }
    ];
    if (!isSeeAll) {
      match.push({
        $match: {
          'employees.id': userId
        }
      });
    }
    match.push({
      $project: {
        'employees.managers': 0
      }
    });
    match.push({
      $sort: { index: 1 }
    });
    if (groupProject) {
      match.push({
        $group: {
          _id: '$project.id',
          projectName: { $first: '$project.name' },
          items: { $push: '$$ROOT' }
        }
      });
    }
    return await this.readModel.aggregate(match).allowDiskUse(true)
      .exec()
      .then(rs => {
        return rs;
      });
  }

  async listCommissionByGroup(posCodeERP: any, query: any = {}, isSeeAll: boolean = false) {
    let match: any = [];
    match = [
      { $match: query },
    ];
    if (!isSeeAll) {
      match.push({
        $match: {
          "pos.code": posCodeERP
        }
      });
    }
    return await this.readModel.aggregate(match).allowDiskUse(true)
      .exec();
  }

  async count(where: any): Promise<number> {
    return this.readModel.countDocuments(where).exec();
  }

  async findMany(filter: any, lean = false): Promise<any> {
    const { where, projection, sort, skip, limit } = filter;
    const query = this.readModel.find(
      where,
      projection,
      {
        sort,
        skip,
        limit,
      }
    );
    if (lean) return query.lean();
    return query;
  }

  async findAllCommissionPolicyIds(): Promise<string[]> {
    const commissionLists = await this.readModel.find({}, {
      "commissionPolicy.id": 1,
      _id: 0
    });

    const policyIds = commissionLists.map(item => item.commissionPolicy?.id);

    return [...new Set([...policyIds].filter(id => id))];
  }

  async bulkUpdateAdjustmentData(listComm = [], mapTx = {}) {
    const updates = listComm.map((com) => {
      if (com.code && mapTx[com.code]) {
        return {
          updateOne: {
            filter: { code: com.code },
            update: {
              $set: {
                ...com,
                updatedDate: new Date(),
                adjustmentData: mapTx[com.code].adjustmentData
              }
            },
            upsert: true
          }
        };
      }

      return null;
    }).filter(Boolean);

    if (updates.length > 0) {
      await this.readModel.bulkWrite(updates);
    } else {
      console.log("No updates emp to perform.");
    }
  }
}