import { Injectable } from "@nestjs/common";
import * as Bluebird from "bluebird";
import * as path from "path";
import { isNullOrUndefined } from "util";
import { DownloadInterestCalculationDto } from "../primary-contract.domain/dto/primary-contract.dto";
import { CommonUtils } from "../shared/classes/class-utils";
import { NumberToMoneyVN } from "../shared/classes/number-to-money-vn";
import { CmdPatternConst } from "../shared/constant";
import { PaymentConst } from "../shared/constant/payment.const";
import { STATUS_MAPPING } from "../shared/constant/status.mapping";
import { AttributeEnum } from "../shared/enum/property-common.enum";
import {
  ContractEnum,
  ScheduleInstallmentEnum,
} from "../shared/enum/primary-contract.enum";
import { PropertyClient } from "../mgs-sender/property.client";
import { UploadClient } from "../mgs-sender/uploader.client";
import { existsSync, unlink, unlinkSync } from "fs";
import { StaticAssetService } from "../config/static-asset.service";
import { ConfigService } from "@nestjs/config";
const fs = Bluebird.promisifyAll(require("fs"));
const Docxtemplater = require("docxtemplater");
const ImageModule = require("docxtemplater-image-module");
const expressions = require("angular-expressions");
const JSZip = require("jszip");
const XlsxTemplate = require("xlsx-template");
const _ = require("lodash");
const momentTz = require("moment-timezone");
import moment = require("moment");
const QRCode = require("qrcode");
const md5 = require("md5");
const axios = require("axios");
const PDFMerger = require("pdf-merger-js");
const libre = require("libreoffice-convert");

@Injectable()
export class FileGenerationService {
  private readonly context = FileGenerationService.name;
  private angularParser: any;
  constructor(
    private readonly propertyClient: PropertyClient,
    private readonly staticAssetService: StaticAssetService,
    private readonly uploadClient: UploadClient,
    private readonly configService: ConfigService
  ) { }
  async getTemplateFilePath(url) {
    // define your filter functions here, for example, to be able to write {clientname | lower}

    expressions.filters.dot = function (input) {
      if (!input) return input;
      return `${input}`.replace(/,/g, ".");
    };
    expressions.filters.money = function (input) {
      if (!input) return input;
      return CommonUtils.parseVND(input);
    };
    expressions.filters.moneyWord = function (input) {
      if (!input) return input;
      return NumberToMoneyVN.parse(input);
    };
    expressions.filters.comma = function (input) {
      if (!input) return input;
      return `${input}`.replace(/\./g, ",");
    };
    expressions.filters.lower = (input) => {
      // This condition should be used to make sure that if your input is undefined, your output will be undefined as well and will not throw an error
      if (!input) return input;
      return input.toLowerCase();
    };
    expressions.filters.size = function (input, width, height) {
      return {
        data: input,
        size: [width, height],
      };
    };
    this.angularParser = (tag) => {
      return {
        // tslint:disable-next-line:arrow-return-shorthand
        get:
          tag === "."
            ? (s) => {
              return s;
            }
            : (s) => {
              return expressions.compile(tag.replace(/(’|“|”)/g, "'"))(s);
            },
      };
    };
    return await this.staticAssetService.getTemplateFile(url);
  }
  async exportContract(userLogged, file, data) {
    // Load an XLSX file into memory
    const templateFile = await fs.readFileAsync(
      this.staticAssetService.getTemplateFileDownload("Template_Download.xlsx"),
      "binary"
    );
    // Create a template
    var template = new XlsxTemplate(templateFile);
    data.rows.map((i, index) => {
      i.stt = index + 1;
      i.statusString = STATUS_MAPPING[i.status];
    });
    // Replacements take place on first sheet
    var sheetNumber = 1;
    // Set up some placeholder values matching the placeholders in the template
    var values = {
      ...data,
    };
    // Perform substitution
    template.substitute(sheetNumber, values);
    // var sheetNumber = 2;
    // template.substitute(sheetNumber, values);
    // Get binary data
    var nodebuffer = template.generate({ type: "nodebuffer" });
    try {
      const fileName = `${file}.xlsx`;
      await fs.writeFileAsync(
        path.resolve(this.staticAssetService.getUploadFolderPath(), fileName),
        nodebuffer,
        function (err) {
          if (err) {
            return console.log(err);
          }
          // console.log(`Wrote data in file, check please!`);
          return;
        }
      );
      return;
    } catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      throw error;
    }
  }
  async downloadContractById(
    userLogged,
    file,
    data,
    url,
    fileName,
    isUpload?: boolean,
    isOverride: boolean = false
  ) {
    // Load an docx file into memory
    var filePath = path.resolve(
      this.staticAssetService.getUploadFolderPath(),
      `${file}.docx`
    );
    await this.download(url, filePath);

    const templateFile = await fs.readFileAsync(filePath, "binary");

    let project: any;
    if ([ContractEnum.PURCHASE, ContractEnum.RENT].indexOf(data.type)) {
      project = await this.propertyClient.sendDataPromise(
        { id: data.primaryTransaction.project.id },
        CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
      );
    }

    const qrCodeParams = `${data?.code
      }|${data?.primaryTransaction?.customer?.personalInfo?.name?.toUpperCase()}|${data?.primaryTransaction?.propertyUnit?.code
      }|`;
    const checkSumSecret =
      this.configService.get("QR_CHECKSUM_SECRET") || "o2oqrsecretzM8}AV";
    const qrChecksumLength = parseInt(
      this.configService.get("QR_CHECKSUM_LENGTH") || "10",
      10
    );
    const checkSum = md5(`${qrCodeParams}${checkSumSecret}`);
    const randInd =
      Math.floor(Math.random() * (checkSum.length - qrChecksumLength - 10)) +
      10;
    const signature = checkSum.substr(randInd, qrChecksumLength);
    const qrCodeFile = path.resolve(
      this.staticAssetService.getUploadFolderPath(),
      `${data?.code}_${new Date().getTime()}.png`
    );
    const stream = fs.createWriteStream(qrCodeFile);
    await QRCode.toFileStream(stream, `${qrCodeParams}${randInd}${signature}`);

    let contractData: any = await this.extractFileDataFromDto(
      data,
      qrCodeFile,
      project
    );
    contractData.productPrice = 0;
    contractData.productPriceNum = 0;
    contractData.productPriceRemaining = 0;
    contractData.productPriceRemainingNum = 0;
    contractData.productPriceTransfered = 0;
    contractData.productPriceTransferedNum = 0;
    contractData.installments = (
      data?.policyPayment?.schedule?.installments || []
    ).map((installment) => {
      installment.remaining =
        (installment.totalAmount || 0) - (installment.totalTransfered || 0);
      contractData.productPriceRemaining += installment.remaining;
      installment.remainingWord = NumberToMoneyVN.parse(
        installment.remaining || 0
      );
      installment.remainingNum = installment.remaining || 0;
      installment.remaining = CommonUtils.parseVND(
        (installment.totalAmount || 0) - (installment.totalTransfered || 0)
      );

      contractData.productPrice += installment.totalAmount;

      installment.totalAmountWord = NumberToMoneyVN.parse(
        installment.totalAmount || 0
      );
      installment.totalAmountNum = installment.totalAmount || 0;
      installment.totalAmount = CommonUtils.parseVND(
        installment.totalAmount || 0
      );

      contractData.productPriceTransfered += installment.totalTransfered;
      installment.totalTransferedWord = NumberToMoneyVN.parse(
        installment.totalTransfered || 0
      );
      installment.totalTransferedNum = installment.totalTransfered || 0;
      installment.totalTransfered = CommonUtils.parseVND(
        installment.totalTransfered || 0
      );

      installment.paymentDateString = "";
      installment.receipts = (installment.receipts || []).map((receipt) => {
        receipt.amountWord = NumberToMoneyVN.parse(receipt.amount || 0);
        receipt.amountNum = receipt.amount || 0;
        receipt.amount = CommonUtils.parseVND(receipt.amount || 0);
        installment.paymentDateString = receipt.receiptDate
          ? moment(receipt.receiptDate, "YYYY-MM-DD").format("DD/MM/YYYY")
          : "";
        return receipt;
      });

      let ratio = "";
      if (installment.type2) {
        ratio =
          installment.type === ScheduleInstallmentEnum.CURRENCY
            ? "- GTN"
            : installment.value + "% GTN";
        ratio +=
          "/" +
          (installment.type2 === ScheduleInstallmentEnum.CURRENCY
            ? "- GTĐ"
            : installment.value2 + "% GTĐ");
      } else {
        ratio =
          installment.type === ScheduleInstallmentEnum.CURRENCY
            ? "-"
            : installment.value + "%";
      }
      installment.ratio = ratio;
      return installment;
    });
    contractData.productPriceWord = NumberToMoneyVN.parse(
      contractData.productPrice || 0
    );
    contractData.productPriceNum = contractData.productPrice || 0;
    contractData.productPrice = CommonUtils.parseVND(
      contractData.productPrice || 0
    );
    contractData.productPriceRemainingNum =
      contractData.productPriceRemaining || 0;
    contractData.productPriceRemaining = CommonUtils.parseVND(
      contractData.productPriceRemaining || 0
    );
    contractData.productPriceTransferedNum =
      contractData.productPriceTransfered || 0;
    contractData.productPriceTransfered = CommonUtils.parseVND(
      contractData.productPriceTransfered || 0
    );
    contractData.propertyArea =
      contractData.property && contractData.property.area;
    const opts: any = {};
    opts.centered = false;
    opts.getImage = (tagValue, tagName) => {
      if (tagValue.data) {
        return fs.readFileSync(tagValue.data);
      }
      try {
        return fs.readFileSync(tagValue);
      } catch (error) {
        return null;
      }
    };

    opts.getSize = (img, tagValue, tagName) => {
      if (tagValue.size && tagValue.data) {
        return tagValue.size;
      }

      return [150, 150];
    };
    const imageModule = new ImageModule(opts);

    const zip = new JSZip(templateFile);
    const doc = new Docxtemplater();
    doc.attachModule(imageModule);
    doc.loadZip(zip);
    expressions.filters.dot = function (input) {
      if (!input) return input;
      return `${input}`.replace(/,/g, ".");
    };
    expressions.filters.comma = function (input) {
      if (!input) return input;
      return `${input}`.replace(/\./g, ",");
    };
    expressions.filters.lower = function (input) {
      if (!input) return input;
      return input.toLowerCase();
    };
    expressions.filters.size = function (input, width, height) {
      return {
        data: input,
        size: [width, height],
      };
    };
    const angularParser = function (tag) {
      return {
        get:
          tag === "."
            ? function (s) {
              return s;
            }
            : function (s) {
              return expressions.compile(tag.replace(/(’|“|”)/g, "'"))(s);
            },
      };
    };
    doc.setOptions({
      parser: angularParser,
    });
    doc.setData({ documents: [contractData], ...contractData });
    try {
      doc.render();
      const nodebuffer = doc.getZip().generate({ type: "nodebuffer" });
      if (!isUpload) {
        await fs.writeFileAsync(
          path.resolve(
            this.staticAssetService.getUploadFolderPath(),
            `${fileName}.docx`
          ),
          nodebuffer
        );
      } else {
        const fileUpload = await this.uploadClient.sendData(
          `${fileName}.docx`,
          nodebuffer,
          isOverride
        );
        if (fileUpload) {
          return fileUpload;
        }
      }

      return;
    } catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      console.log(JSON.stringify({ error: e }));
      throw error;
    }
  }
  async generateAndSaveTicketFile(
    url,
    dtos: any,
    fileName,
    user,
    isGetFilePath = false
  ): Promise<any> {
    let datas = [];
    const qrCodeFiles = [];
    for (const data of dtos.rows) {
      const qrCodeParams = `${data?.code
        }|${data?.primaryTransaction?.customer?.personalInfo?.name?.toUpperCase()}|${data?.primaryTransaction?.propertyUnit?.code
        }|`;
      const checkSumSecret =
        this.configService.get("QR_CHECKSUM_SECRET") || "o2oqrsecretzM8}AV";
      const qrChecksumLength = parseInt(
        this.configService.get("QR_CHECKSUM_LENGTH") || "10",
        10
      );
      const checkSum = md5(`${qrCodeParams}${checkSumSecret}`);
      const randInd =
        Math.floor(Math.random() * (checkSum.length - qrChecksumLength - 10)) +
        10;
      const signature = checkSum.substr(randInd, qrChecksumLength);
      const qrCodeFile = path.resolve(
        this.staticAssetService.getUploadFolderPath(),
        `${data?.code}_${new Date().getTime()}.png`
      );
      const stream = fs.createWriteStream(qrCodeFile);
      await QRCode.toFileStream(
        stream,
        `${qrCodeParams}${randInd}${signature}`
      );
      qrCodeFiles.push(qrCodeFile);
      let contractData: any = await this.extractFileDataFromDto(
        data,
        qrCodeFile
      );
      contractData.productPrice = 0;
      contractData.productPriceRemaining = 0;
      contractData.productPriceTransfered = 0;
      contractData.installments = (
        data?.policyPayment?.schedule?.installments || []
      ).map((installment) => {
        installment.remaining =
          (installment.totalAmount || 0) - (installment.totalTransfered || 0);
        contractData.productPriceRemaining += installment.remaining;
        installment.remainingWord = NumberToMoneyVN.parse(
          installment.remaining || 0
        );
        installment.remainingNum = installment.remaining || 0;
        installment.remaining = CommonUtils.parseVND(
          (installment.totalAmount || 0) - (installment.totalTransfered || 0)
        );

        contractData.productPrice += installment.totalAmount;

        installment.totalAmountWord = NumberToMoneyVN.parse(
          installment.totalAmount || 0
        );
        installment.totalAmountNum = installment.totalAmount || 0;
        installment.totalAmount = CommonUtils.parseVND(
          installment.totalAmount || 0
        );

        contractData.productPriceTransfered += installment.totalTransfered;
        installment.totalTransferedWord = NumberToMoneyVN.parse(
          installment.totalTransfered || 0
        );
        installment.totalTransferedNum = installment.totalTransfered || 0;
        installment.totalTransfered = CommonUtils.parseVND(
          installment.totalTransfered || 0
        );

        installment.paymentDateString = "";
        installment.receipts = (installment.receipts || []).map((receipt) => {
          receipt.amountWord = NumberToMoneyVN.parse(receipt.amount || 0);
          receipt.amountNum = receipt.amount || 0;
          receipt.amount = CommonUtils.parseVND(receipt.amount || 0);
          installment.paymentDateString = receipt.receiptDate
            ? moment(receipt.receiptDate, "YYYY-MM-DD").format("DD/MM/YYYY")
            : "";
          return receipt;
        });

        let ratio = "";
        if (installment.type2) {
          ratio =
            installment.type === ScheduleInstallmentEnum.CURRENCY
              ? "- GTN"
              : installment.value + "% GTN";
          ratio +=
            "/" +
            (installment.type2 === ScheduleInstallmentEnum.CURRENCY
              ? "- GTĐ"
              : installment.value2 + "% GTĐ");
        } else {
          ratio =
            installment.type === ScheduleInstallmentEnum.CURRENCY
              ? "-"
              : installment.value + "%";
        }
        installment.ratio = ratio;
        return installment;
      });
      contractData.productPriceWord = NumberToMoneyVN.parse(
        contractData.productPrice || 0
      );
      contractData.productPriceNum = contractData.productPrice || 0;
      contractData.productPrice = CommonUtils.parseVND(
        contractData.productPrice || 0
      );
      contractData.productPriceRemainingNum =
        contractData.productPriceRemaining;
      contractData.productPriceRemaining = CommonUtils.parseVND(
        contractData.productPriceRemaining || 0
      );
      contractData.productPriceTransferedNum =
        contractData.productPriceTransfered || 0;
      contractData.productPriceTransfered = CommonUtils.parseVND(
        contractData.productPriceTransfered || 0
      );
      contractData.propertyArea =
        contractData.property && contractData.property.area;
      datas.push(contractData);
    }
    const pathPdfFiles = [];

    // generate separate pdf file
    for (let index = 0; index < datas.length; index++) {
      const opts: any = {};
      opts.centered = false;
      opts.getImage = (tagValue, tagName) => {
        if (tagValue.data) {
          return fs.readFileSync(tagValue.data);
        }
        return fs.readFileSync(tagValue);
      };

      opts.getSize = (img, tagValue, tagName) => {
        if (tagValue.size && tagValue.data) {
          return tagValue.size;
        }

        return [80, 80];
      };
      const imageModule = new ImageModule(opts);
      const templateFilePath = await this.getTemplateFilePath(url);
      const templateFile = await fs.readFileAsync(templateFilePath, "binary");
      const item = datas[index];

      const zip = new JSZip(templateFile);
      const doc = new Docxtemplater();
      doc.attachModule(imageModule);
      doc.loadZip(zip);
      doc.setOptions({
        parser: this.angularParser,
        paragraphLoop: true,
        linebreaks: true,
      });
      doc.setData({ documents: [item], ...item });
      try {
        doc.render();

        // await new Promise((resolve, reject) => {
        //     unlink(qrCodeFiles[index], err => {
        //         resolve(true);
        //     });
        // });

        const nodebuffer = doc.getZip().generate({ type: "nodebuffer" });
        const pathDoc = path.resolve(
          this.staticAssetService.getUploadFolderPath(),
          `${fileName}-${index}.docx`
        );
        const pathPdf = path.resolve(
          this.staticAssetService.getUploadFolderPath(),
          `${fileName}-${index}.pdf`
        );
        await fs.writeFileAsync(pathDoc, nodebuffer);
        await new Promise(async (resolve, reject) => {
          const extend = ".pdf";
          // Read file
          const enterPath = await fs.readFileSync(pathDoc);
          // Convert it to pdf format with undefined filter (see Libreoffice doc about filter)
          return await libre.convert(
            enterPath,
            extend,
            undefined,
            async (err, done) => {
              if (err) {
                if (existsSync(pathDoc)) {
                  unlinkSync(pathDoc);
                }
                // data.forEach(property => {
                // if (property.image && existsSync(property.image))
                // unlinkSync(property.image);
                // });
                reject(err);
              }
              // Here in done you have pdf file which you can save or transfer in another stream
              await fs.writeFileSync(pathPdf, done);
              pathPdfFiles.push(pathPdf);
              if (existsSync(pathDoc)) {
                unlinkSync(pathDoc);
              }
              // data.forEach(property => {
              //     if (property.image && existsSync(property.image))
              //         unlinkSync(property.image);
              // });
              resolve(dtos[0]);
            }
          );
        });
      } catch (error) {
        console.log(this.context, "has error => ", error);
        throw error;
      }
    }

    const merger = new PDFMerger();

    pathPdfFiles.forEach(async (path) => await merger.add(path));

    await merger.save(
      path.resolve(
        this.staticAssetService.getUploadFolderPath(),
        `${fileName}.pdf`
      )
    );

    await Promise.all(
      pathPdfFiles.map(
        (path) =>
          new Promise((resolve, reject) => {
            unlink(path, (err) => {
              resolve(true);
            });
          }),
        qrCodeFiles.map(
          (path) =>
            new Promise((resolve, reject) => {
              unlink(path, (err) => {
                resolve(true);
              });
            })
        )
      )
    );
  }
  async generateAndSaveInterestCalculationFile(creator, dto, url, fileName) {
    const templateFile = await fs.readFileAsync(url, "binary");
    const interestData = this.extractFileDataFromInterestCalculationDto(
      dto,
      creator
    );
    const zip = new JSZip(templateFile);
    const doc = new Docxtemplater();
    doc.loadZip(zip);
    expressions.filters.lower = function (input) {
      if (!input) return input;
      return input.toLowerCase();
    };
    const angularParser = function (tag) {
      return {
        get:
          tag === "."
            ? function (s) {
              return s;
            }
            : function (s) {
              return expressions.compile(tag.replace(/(’|“|”)/g, "'"))(s);
            },
      };
    };
    doc.setOptions({
      parser: angularParser,
    });
    doc.setData(interestData);
    try {
      doc.render();
      const nodebuffer = doc.getZip().generate({ type: "nodebuffer" });
      await fs.writeFileAsync(
        path.resolve(this.staticAssetService.getUploadFolderPath(), fileName),
        nodebuffer
      );
      return;
    } catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      console.log(JSON.stringify({ error: e }));
      throw error;
    }
  }
  async download(url, dest) {
    const http = require("https");
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(dest, { flags: "wx" });

      const request = http.get(url, (response) => {
        if (response.statusCode === 200) {
          response.pipe(file);
        } else {
          file.close();
          fs.unlink(dest, () => { }); // Delete temp file
          reject(
            `Server responded with ${response.statusCode}: ${response.statusMessage}`
          );
        }
      });

      request.on("error", (err) => {
        file.close();
        fs.unlink(dest, () => { }); // Delete temp file
        reject(err.message);
      });

      file.on("finish", () => {
        resolve(true);
      });

      file.on("error", (err) => {
        file.close();

        if (err.code === "EEXIST") {
          reject("File already exists");
        } else {
          fs.unlink(dest, () => { }); // Delete temp file
          reject(err.message);
        }
      });
    });
  }
  private async extractFileDataFromDto(
    contractDto: any,
    qrCodeFile: string,
    project?: any
  ) {
    const moment = require("moment");
    const customer = contractDto.primaryTransaction.customer;
    const createdDate = new Date(
      momentTz(contractDto.createdDate).tz("Asia/Ho_Chi_Minh")
    );
    const birthday = contractDto.isTransferred
      ? customer.birthday && moment(new Date(customer.birthday)).isValid()
        ? moment(customer.birthday, "YYYY-MM-DD").format("DD/MM/YYYY")
        : ""
      : customer.info.birthday &&
        moment(new Date(customer.info.birthday)).isValid()
        ? moment(new Date(customer.info.birthday)).format("DD/MM/YYYY")
        : customer.info.onlyYear
          ? customer.info.birthdayYear
          : "";
    const identityIssueDate = contractDto.isTransferred
      ? customer.identityDate &&
        moment(new Date(customer.identityDate)).isValid()
        ? moment(customer.identityDate).format("DD/MM/YYYY")
        : ""
      : customer.personalInfo.identities[0].date &&
        moment(new Date(customer.personalInfo.identities[0].date)).isValid()
        ? moment(customer.personalInfo.identities[0].date).format("DD/MM/YYYY")
        : "";
    const propertyUnit = contractDto.primaryTransaction.propertyUnit;
    let atBlock = null;
    let floor = null;
    let buildUpArea = null;
    let carpetArea = null;

    let apartmentType = null;
    let bedroom = null;
    let view1 = null;
    let view2 = null;
    let view3 = null;
    let corner = null;
    let expireValue = null;
    let discount = 0;

    const customer2 =
      contractDto.primaryTransaction.customer2 &&
        contractDto.primaryTransaction.customer2.personalInfo &&
        contractDto.primaryTransaction.customer2.personalInfo.name
        ? contractDto.primaryTransaction.customer2
        : null;
    const birthday2 =
      customer2 &&
        customer2.info &&
        customer2.info.birthday &&
        moment(new Date(customer2.info.birthday)).isValid()
        ? moment(customer2.info.birthday).format("DD/MM/YYYY")
        : customer2 && customer2.info.onlyYear
          ? customer2.info.birthdayYear
          : "";
    const identityIssueDate2 =
      customer2 &&
        customer2.identities &&
        customer2.identities[0] &&
        customer2.identities[0].date &&
        moment(new Date(customer2.identities[0].date)).isValid()
        ? moment(customer2.identities[0].date).format("DD/MM/YYYY")
        : "";
    const installments: [] = _.get(
      contractDto,
      "policyPayment.schedule.installments",
      []
    );
    if (installments && installments.length) {
      installments.map((item: any, index) => {
        item.stt = index + 1;
        if (item.type !== "percent") {
          item.value = "";
        }
        if (
          [ContractEnum.PURCHASE, ContractEnum.RENT].indexOf(contractDto.type)
        ) {
          item.paymentDueDate =
            item.paymentDueDate &&
              moment(new Date(item.paymentDueDate)).isValid()
              ? moment(item.paymentDueDate).format("DD/MM/YYYY")
              : "";
        }
        return item;
      });
    }
    if (!isNullOrUndefined(propertyUnit)) {
      atBlock = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.block;
      });
      floor = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.tang;
      });
      buildUpArea = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.dien_tich_tim_tuong;
      });
      carpetArea = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.dien_tich_thong_thuy;
      });
      bedroom = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.phong_ngu;
      });
      view1 = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.view1;
      });
      view2 = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.view2;
      });
      view3 = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.view3;
      });
      corner = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.can_goc;
      });
      expireValue = propertyUnit.attributes.find(function (element) {
        return element.attributeId === AttributeEnum.quyen_su_dung_dat;
      });

      const salesProgram: any = propertyUnit.salesProgram
        ? await this.propertyClient.sendDataPromise(
          {
            id: propertyUnit.salesProgram.id,
          },
          CmdPatternConst.SALES_PROGRAM.GET_SALES_PROGRAM_BY_ID
        )
        : null;
      discount =
        salesProgram && salesProgram.discount ? salesProgram.discount : 0;
    }
    const customerBankCode = customer.bankInfo ? customer.bankInfo.code : "";
    const customerBankName = PaymentConst.BANK_LIST.find(
      (bank) => bank.CODE == customerBankCode
    )?.NAME;
    const signedDate = moment(contractDto.signedDate).format("DD");
    const signedMonth = moment(contractDto.signedDate).format("MM");
    const signedYear = moment(contractDto.signedDate).format("YYYY");
    const signedDay = moment(contractDto.signedDate).format("DD/MM/YYYY");
    const amount =
      contractDto.primaryTransaction.project.setting.amountRegistration || 0;
    const totalAmount = contractDto.isTransferred
      ? amount
      : contractDto.policyPayment.schedule.installments[0].totalAmount;
    const remainingAmount = totalAmount - amount;
    const posName = customer?.employee?.pos?.name;

    let ownership: string = "";
    if ([ContractEnum.PURCHASE, ContractEnum.RENT].indexOf(contractDto.type)) {
      ownership = project?.ownership;
    }

    const result = {
      newLine: "\n",
      image: qrCodeFile,
      code: contractDto.code,
      createdDate: {
        day: ("0" + createdDate.getDate()).slice(-2),
        month: ("0" + (createdDate.getMonth() + 1)).slice(-2),
        year: createdDate.getFullYear(),
      },
      id: {
        value: (customer.identities && customer.personalInfo.identities[0].value) || "",
        date: identityIssueDate,
        place: (customer.identities && customer.personalInfo.identities[0].place) || "",
      },

      customer: {
        fullName: customer?.personalInfo?.name?.toUpperCase() || "",
        birthday: birthday,
        identity: {
          value: (customer.identities && customer.personalInfo.identities[0].value) || "",
          date: identityIssueDate,
          place: (customer.identities && customer.personalInfo.identities[0].place) || "",
        },
        rootAddress: customer?.info?.rootAddress?.fullAddress || "",
        address: customer?.info?.address?.fullAddress || "",
        email: customer?.personalInfo?.email || "",
        phone: customer?.personalInfo?.phone || "",
        taxNo: _.get(
          customer,
          "personalInfo.taxCode ",
          "………………………………………………………………………………………"
        ),
        bank: {
          name: customerBankName || "………………………………………",
          number: _.get(customer, "bankInfo.value", "………………………………………"),
        },
      },
      property: Object.assign(
        {
          propertyCode: propertyUnit ? propertyUnit.code : "",
          propertyNum: propertyUnit ? propertyUnit.shortCode : "",
          direction: propertyUnit ? propertyUnit.direction : "",
          apartmentType: apartmentType ? apartmentType.value : "",
          bedroom: bedroom ? bedroom.value : "",
          view1: view1 ? view1.value : "",
          view2: view2 ? view2.value : "",
          view3: view3 ? view3.value : "",
          expireValue: expireValue ? expireValue.value : "",
          corner: corner ? corner.value : "",
          outsideArea: propertyUnit ? propertyUnit.outsideArea : "",

          block: atBlock ? atBlock.value : "",
          blockShortCode:
            atBlock && atBlock.value ? atBlock.value.slice(-3) : "....",
          floor: floor ? floor.value : "",
          buildUpArea: buildUpArea ? buildUpArea.value : "",
          carpetArea: carpetArea ? carpetArea.value : "",
          priceNum: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.price)
            : "",
          priceWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.price)
            : "",
          priceVat: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.priceVat)
            : "",
          priceVatWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.priceVat)
            : "",
          priceAbove: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.priceAbove)
            : "",
          priceAboveWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.priceAbove)
            : "",
          priceAboveVat: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.priceAboveVat)
            : "",
          priceAboveVatWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.priceAboveVat)
            : "",
          ownership,

          housePrice: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.housePrice)
            : "",
          housePriceWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.housePrice)
            : "",
          housePriceVat: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.housePriceVat)
            : "",
          housePriceVatWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.housePriceVat)
            : "",

          landPrice: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.landPrice)
            : "",
          landPriceWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.landPrice)
            : "",
          landPriceVat: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.landPriceVat)
            : "",
          landPriceVatWord: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.landPriceVat)
            : "",

          priceNumDiscount: propertyUnit
            ? CommonUtils.parseVND(propertyUnit.price - discount)
            : "",
          priceWordDiscount: propertyUnit
            ? NumberToMoneyVN.parse(propertyUnit.price - discount)
            : "",
          floorImages:
            propertyUnit &&
              propertyUnit.floorImages &&
              propertyUnit.floorImages.length
              ? propertyUnit.floorImages
              : [],
        },
        propertyUnit
      ),
      customer2Value: customer2 ? true : false,
      customer2: {
        fullName:
          customer2 && customer2.personalInfo.name
            ? customer2.personalInfo.name.toUpperCase()
            : "Không có",
        birthday: customer2 && birthday2 ? birthday2 : "Không có",
        rootAddress:
          customer2 && customer2.info.rootAddress.fullAddress
            ? customer2.info.rootAddress.fullAddress
            : "Không có",
        address:
          customer2 && customer2.info.address.fullAddress
            ? customer2.info.address.fullAddress
            : "Không có",
        email:
          customer2 && customer2.personalInfo.email
            ? customer2.personalInfo.email
            : "Không có",
        phone:
          customer2 && customer2.personalInfo.phone
            ? customer2.personalInfo.phone
            : "Không có",
        identity: {
          value:
            customer2 && customer2.personalInfo.identities[0].value
              ? customer2.personalInfo.identities[0].value
              : "Không có",
          date:
            customer2 && identityIssueDate2 ? identityIssueDate2 : "Không có",
          place:
            customer2 && customer2.identities[0].place
              ? customer2.identities[0].place
              : "Không có",
        },
      },
      companyInformation: {
        ...contractDto.companyInformation,
        dateOfIssue:
          contractDto.companyInformation &&
            contractDto.companyInformation.dateOfIssue
            ? moment(
              contractDto.companyInformation.dateOfIssue,
              "YYYY-MM-DD"
            ).format("DD/MM/YYYY")
            : "",
      },
      policyDiscount: contractDto.policyDiscount,
      installments,
      primaryTransaction: contractDto.primaryTransaction,
      signedDate,
      signedMonth,
      signedYear,
      signedDay,
      nowTime: {
        day: moment().format("DD"),
        month: moment().format("MM"),
        year: moment().format("YYYY"),
      },
      amount: amount.toLocaleString(),
      totalAmount: totalAmount.toLocaleString(),
      remainingAmount: remainingAmount.toLocaleString(),
      amountWord: NumberToMoneyVN.parse(amount),
      totalAmountWord: NumberToMoneyVN.parse(totalAmount),
      remainingAmountWord: NumberToMoneyVN.parse(remainingAmount),
      posName,
    };

    if (result.primaryTransaction?.propertyUnit?.extData) {
      let extData = result.primaryTransaction.propertyUnit.extData;
      (this.objectPaths(extData) || []).forEach((e) => {
        if (+extData[e] && typeof +extData[e] === "number") {
          extData[e + "Word"] = NumberToMoneyVN.parse(extData[e] || 0);
          extData[e + "Num"] = CommonUtils.parseVND(extData[e] || 0);
        }
      });
      result.primaryTransaction.propertyUnit.extData = extData;
      result.property["extData"] = extData;
    }

    await Promise.all(
      result.property.floorImages.map((e) =>
        this.staticAssetService.getTemplateFile(e.url).then((path) => {
          e.path = path;
          const index = e.originalName.split("_").pop().split(".").shift();
          result[`MB_${index}`] = path;
        })
      )
    );

    return result;
  }

  private objectPaths(o) {
    return this.rKeys(o).toString().split(",");
  }

  private rKeys(o, path = "") {
    if (!o || typeof o !== "object") return path;
    return Object.keys(o).map((key) =>
      this.rKeys(o[key], path ? [path, key].join(".") : key)
    );
  }

  private extractFileDataFromInterestCalculationDto(
    intersetDto: DownloadInterestCalculationDto,
    creator: string
  ) {
    let startDate = isNullOrUndefined(intersetDto.startDate)
      ? new Date()
      : intersetDto.startDate;
    let endDate = isNullOrUndefined(intersetDto.endDate)
      ? new Date()
      : intersetDto.endDate;
    let date = new Date();
    return {
      customerName: intersetDto.customerName,
      productCode: intersetDto.productCode,
      projectName: intersetDto.projectName,
      address: intersetDto.address,
      contractName: intersetDto.contractName,
      signedDate: moment(intersetDto.signedDate).format("DD/MM/YYYY"),
      installmentName: intersetDto.installmentName,
      principalAmount: this.parseVND(intersetDto.principalAmount),
      startDate: moment(startDate).format("DD/MM/YYYY"),
      endDate: moment(endDate).format("DD/MM/YYYY"),
      dayOfLatePayment: intersetDto.dayOfLatePayment,
      interestRate: intersetDto.interestRate,
      interestAmount: this.parseVND(intersetDto.interestAmount),
      interestReductionAmount: this.parseVND(
        intersetDto.interestReductionAmount
      ),
      remainingAmount: this.parseVND(intersetDto.remainingAmount),
      accountOwner: intersetDto?.companyName?.toUpperCase(),
      accountNumber:
        intersetDto?.banks?.length > 0
          ? intersetDto.banks[0].accountNumber
          : "",
      bankName: intersetDto?.banks?.length > 0 ? intersetDto.banks[0].name : "",
      branch: "",
      day: ("0" + date.getDate()).slice(-2),
      month: ("0" + (date.getMonth() + 1)).slice(-2),
      year: date.getFullYear(),
      companyName: intersetDto.companyName,
      creator: creator.toUpperCase() || "",
    };
  }
  private parseVND(number) {
    return new Intl.NumberFormat("vi-VN").format(number);
  }

  async generateDebtReport(
    data: any[],
    fileName: string
  ) {
    const templateFile = await fs.readFileAsync(this.staticAssetService.getDebtReportTemplateFile(), 'binary');
    const template = new XlsxTemplate(templateFile);
    template.substitute(1, { rows: data });
    const buffer = template.generate({ type: "nodebuffer" });
    try {
      await fs.writeFileAsync(path.resolve(this.staticAssetService.getUploadFolderPath(), fileName), buffer, (err: any) => {
        if (err) {
          return console.log(err);
        }
        return;
      });
      return;
    }
    catch (error) {
      throw error;
    }
  }

  async exportDebtReport(userLogged, file, data) {
    // Load an XLSX file into memory
    const templateFile = await fs.readFileAsync(
      this.staticAssetService.getTemplateFileDownload("Template_DebtReport.xlsx"),
      "binary"
    );
    // Create a template
    var template = new XlsxTemplate(templateFile);
    data.rows.map((i, index) => {
      i.stt = index + 1;
      i.statusString = STATUS_MAPPING[i.status];
    });
    // Replacements take place on first sheet
    var sheetNumber = 1;
    // Set up some placeholder values matching the placeholders in the template
    var values = {
      ...data,
    };
    // Perform substitution
    template.substitute(sheetNumber, values);
    // var sheetNumber = 2;
    // template.substitute(sheetNumber, values);
    // Get binary data
    var nodebuffer = template.generate({ type: "nodebuffer" });
    try {
      const fileName = `${file}.xlsx`;
      await fs.writeFileAsync(
        path.resolve(this.staticAssetService.getUploadFolderPath(), fileName),
        nodebuffer,
        function (err) {
          if (err) {
            return console.log(err);
          }
          // console.log(`Wrote data in file, check please!`);
          return;
        }
      );
      return;
    } catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      throw error;
    }
  }
}
