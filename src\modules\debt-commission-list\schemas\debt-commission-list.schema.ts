import * as mongoose from 'mongoose';
import { CommissionSchema, ContractSchema, CustomerSchema, EmployeeSchema, PosSchema, ProjectSchema, PropertyUnitSchema } from '../../shared/schemas/common.schema';
const uuid = require('uuid');

const Data = new mongoose.Schema({
  adjust: { type: Number, default: null },
  final: { type: Number, default: null },
}, { _id: false });

export const AdjustmentData = new mongoose.Schema({
  id: { type: Number },
  version: { type: String },
  debtRevenue: { type: Data, default: null }, // Doanh thu thu hồi
  debtCommissionRevenue: { type: Data, default: null }, // Hoa hồng công nợ
}, { _id: false });

export const DebtCommissionListSchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  index: { type: Number, default: null },
  code: { type: String, index: true },
  installmentName: { type: String, index: true },
  debtType: { type: String, index: true },
  commission: { type: CommissionSchema, default: null },
  vatRate: { type: Number, default: null },
  project: { type: ProjectSchema, default: null },
  propertyUnit: { type: PropertyUnitSchema, default: null },
  contract: { type: ContractSchema, default: null },
  customer: { type: CustomerSchema, default: null },
  debtCollector: { type: Object, default: null },
  pos: { type: PosSchema, default: null },
  isPublish: { type: Boolean, default: false },
  commissionPolicy: { type: Object }, // kpi
  debtage: { type: Object },
  employees: { type: [EmployeeSchema], default: null },

  modifiedDate: { type: Date, default: () => Date.now() },
  modifiedBy: { type: String },
  createdBy: { type: String },
  createdDate: { type: Date, default: () => Date.now() },
  softDelete: { type: Boolean, default: false },
  softDeleteReason: { type: String, default: '' },

  adjustmentData: { type: [AdjustmentData], default: null },
});

DebtCommissionListSchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
