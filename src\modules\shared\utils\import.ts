export function validateExcelHeaders(worksheet) {
  // Get the header row (assuming it's row 1)
  const headerRow5 = worksheet.getRow(5);
  const headerRow6 = worksheet.getRow(6);

  // Define expected headers and their columns
  const expectedHeaders = [
    // Row 5 headers
    { row: 5, col: 'A', value: 'STT' },
    { row: 5, col: 'B', value: 'Thông tin chung' },
    { row: 5, col: 'C', value: 'Thông tin chung' },
    { row: 5, col: 'D', value: 'Thông tin chung' },
    { row: 5, col: 'E', value: 'Thông tin chung' },
    { row: 5, col: 'F', value: 'Thông tin chung' },
    { row: 5, col: 'G', value: 'Thông tin chung' },
    { row: 5, col: 'H', value: 'Thông tin chung' },
    { row: 5, col: 'I', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'J', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'K', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'L', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'M', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'N', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'O', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'P', value: 'Thông tin hoa hồng công nợ' },
    { row: 5, col: 'Q', value: 'Doanh thu thu hồi điều chỉnh' },
    { row: 5, col: 'R', value: 'Doanh thu thu hồi điều chỉnh' },
    { row: 5, col: 'S', value: 'Hoa hồng công nợ điều chỉnh' },
    { row: 5, col: 'T', value: 'Hoa hồng công nợ điều chỉnh' },

    // Row 6 headers
    { row: 6, col: 'A', value: 'STT' },
    { row: 6, col: 'B', value: 'Số hợp đồng' },
    { row: 6, col: 'C', value: 'Hợp đồng' },
    { row: 6, col: 'D', value: 'Mã khách hàng' },
    { row: 6, col: 'E', value: 'Khách hàng' },
    { row: 6, col: 'F', value: 'Mã nhân viên công nợ' },
    { row: 6, col: 'G', value: 'Nhân viên công nợ' },
    { row: 6, col: 'H', value: 'Đợt thanh toán' },
    { row: 6, col: 'I', value: 'Tuổi nợ' },
    { row: 6, col: 'J', value: 'Loại nợ' },
    { row: 6, col: 'K', value: 'Doanh thu thu hồi (VNĐ)' },
    { row: 6, col: 'L', value: 'Tính theo' },
    { row: 6, col: 'M', value: 'Hệ số thù lao (%)' },
    { row: 6, col: 'N', value: 'Tỉ lệ hoa hồng (%)' },
    { row: 6, col: 'O', value: 'Tiền hoa hồng (VNĐ)' },
    { row: 6, col: 'P', value: 'Hoa hồng công nợ (VNĐ)' },
    { row: 6, col: 'Q', value: 'Số tiền điều chỉnh *' },
    { row: 6, col: 'R', value: 'Số tiền sau điều chỉnh' },
    { row: 6, col: 'S', value: 'Số tiền điều chỉnh *' },
    { row: 6, col: 'T', value: 'Số tiền sau điều chỉnh' },
  ];

  // Validate each header
  const errors = [];
  expectedHeaders.forEach(header => {
    const row = header.row === 5 ? headerRow5 : headerRow6;
    const cell = row.getCell(header.col);
    if (typeof cell.value == 'string') {
      if (cell.value !== header.value) {
        errors.push(`Expected row ${header.row}, column ${header.col} to be "${header.value}" but found "${cell.value || 'empty'}"`);
      }
    } else {
      const cellValue = richTextToString(cell.value);
      // console.log('cellValue', cellValue);
      if (cellValue !== header.value) {
        errors.push(`Expected row ${header.row}, column ${header.col} to be "${header.value}" but found "${cellValue || 'empty'}"`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

function richTextToString(richTextObject: any): string {
  if (richTextObject && Array.isArray(richTextObject.richText)) {
    return richTextObject.richText.map(rt => rt.text).join('');
  }
  return '';
}

export function validateBody(unwindComList, worksheet) {
  const errors = [];
  // console.log('unwindComList', unwindComList);
  let tx;
  let i = 0;

  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber > 7) {
      console.log('i', i);
      tx = unwindComList[i];

      const contractCode = row.getCell('B');
      if (!validateCellValue(contractCode, tx.contract.code)) {
        errors.push({
          line: rowNumber,
          error: `Số hợp đồng không được chỉnh sửa`
        });
      }
      const contract = row.getCell('C');
      if (!validateCellValue(contract, tx.contract.name)) {
        errors.push({
          line: rowNumber,
          error: `Hợp đồng không được chỉnh sửa`
        });
      }
      const customerCode = row.getCell('D');
      if (!validateCellValue(customerCode, tx.customer.code)) {
        errors.push({
          line: rowNumber,
          error: `Mã khách hàng không được chỉnh sửa`
        });
      }
      const customerName = row.getCell('E');
      if (!validateCellValue(customerName, tx.customer.name)) {
        errors.push({
          line: rowNumber,
          error: `Khách hàng không được chỉnh sửa`
        });
      }
      const debtCollectorCode = row.getCell('F');
      if (!validateCellValue(debtCollectorCode, tx.debtCollector.code)) {
        errors.push({
          line: rowNumber,
          error: `Mã nhân viên công nợ không được chỉnh sửa`
        });
      }
      const debtCollectorName = row.getCell('G');
      if (!validateCellValue(debtCollectorName, tx.debtCollector.name)) {
        errors.push({
          line: rowNumber,
          error: `Nhân viên công nợ không được chỉnh sửa`
        });
      }
      const installmentName = row.getCell('H');
      if (!validateCellValue(installmentName, tx.installmentName)) {
        errors.push({
          line: rowNumber,
          error: `Đợt thanh toán không được chỉnh sửa`
        });
      }
      const debtage = row.getCell('I');
      if (!validateCellValue(debtage, tx.debtage?.name)) {
        errors.push({
          line: rowNumber,
          error: `Tuổi nợ không được chỉnh sửa`
        });
      }
      const debtType = row.getCell('J');
      if (!validateCellValue(debtType, tx.debtType)) {
        errors.push({
          line: rowNumber,
          error: `Loại nợ không được chỉnh sửa`
        });
      }
      const debtRevenue = row.getCell('K');
      if (!validateCellValue(debtRevenue, tx.employees.commissions.debtRevenue)) {
        errors.push({
          line: rowNumber,
          error: `Doanh thu thu hồi không được chỉnh sửa`
        });
      }
      const calculateType = row.getCell('L');
      if (!validateCellValue(calculateType, tx.commissionPolicy.type)) {
        errors.push({
          line: rowNumber,
          error: `Tính theo không được chỉnh sửa`
        });
      }
      const debtRate = row.getCell('M');
      if (!validateCellValue(debtRate, tx.commissionPolicy.rate)) {
        errors.push({
          line: rowNumber,
          error: `Hệ số thù lao không được chỉnh sửa`
        });
      }

      if (tx.employees.commissions.unit == '%') {
        const recordedCommissionP = row.getCell('N');
        if (!validateCellValue(recordedCommissionP, tx.employees.commissions.recordedCommission)) {
          errors.push({
            line: rowNumber,
            error: `Tỉ lệ hoa hồng không được chỉnh sửa`
          });
        }
      } else {
        const recordedCommissionC = row.getCell('O');
        if (!validateCellValue(recordedCommissionC, tx.employees.commissions.recordedCommission)) {
          errors.push({
            line: rowNumber,
            error: `Tiền hoa hồng không được chỉnh sửa`
          });
        }
      }

      const debtCommissionRevenue = row.getCell('P');
      if (!validateCellValue(debtCommissionRevenue, tx.employees.commissions.debtCommissionRevenue)) {
        errors.push({
          line: rowNumber,
          error: `Hoa hồng công nợ không được chỉnh sửa`
        });
      }

      // Q - Doanh thu thu hồi điều chỉnh
      const debtRevenueAdjust = row.getCell('Q');
      if (!validateNumber(debtRevenueAdjust)) {
        errors.push({
          line: rowNumber,
          error: `Doanh thu thu hồi điều chỉnh sai định dạng`
        });
      }
      if (!validateNumberLength(debtRevenueAdjust)) {
        errors.push({
          line: rowNumber,
          error: `Doanh thu thu hồi điều chỉnh vượt quá ký tự tối đa`
        });
      }
      // R - Doanh thu thu hồi sau điều chỉnh
      const debtRevenueFinal = row.getCell('R');
      if (!validateCellNotEmpty(debtRevenueFinal)) {
        errors.push({
          line: rowNumber,
          error: `Doanh thu thu hồi sau điều chỉnh không được chỉnh sửa`
        });
      }

      // S - Hoa hồng công nợ điều chỉnh
      const debtComRevenueAdjust = row.getCell('S');
      if (!validateNumber(debtComRevenueAdjust)) {
        errors.push({
          line: rowNumber,
          error: `Hoa hồng công nợ điều chỉnh thu sai định dạng`
        });
      }
      if (!validateNumberLength(debtRevenueAdjust)) {
        errors.push({
          line: rowNumber,
          error: `Hoa hồng công nợ điều chỉnh thu vượt quá ký tự tối đa`
        });
      }
      // T - Hoa hồng công nợ sau điều chỉnh
      const debtComRevenueFinal = row.getCell('T');
      if (!validateCellNotEmpty(debtComRevenueFinal)) {
        errors.push({
          line: rowNumber,
          error: `Hoa hồng công nợ sau điều chỉnh không được chỉnh sửa`
        });
      }

      i++;
    }
  });

  return { isValid: errors.length === 0, errors };
}

function validateNumber(cell) {
  if (cell.value === null || cell.value === undefined) {
    return false;
  }
  if (cell.numFmt != '#,##0') {
    return false;
  }

  return true;
}

function validateNumberLength(cell) {
  const cellValue = cell.value ? cell.value.toString().trim() : '0';
  const digitsOnly = cellValue.replace(/,/g, '');
  if (digitsOnly.length > 13) {
    return false;
  }

  return true;
}

function validateCellNotEmpty(cell) {
  const cellValue = cell.value?.toString().trim() || '';
  if (cellValue != '') {
    return false;
  }

  return true;
}

function validateCellValue(cell, field, isCommissionRate = false) {

  if (!cell || cell.value === undefined) return false;

  const cellValue = cell.value?.toString().trim() || '';

  if (isCommissionRate) {
    const totalRate = field.reduce((acc, cur) => acc + cur.rate, 0);
    // console.log('cellValue', cellValue);
    // console.log('totalRate', totalRate);
    return cellValue == totalRate;
  }

  return cellValue == field;
}

export function printExcelHeaders(worksheet) {
  const headerRow5 = worksheet.getRow(5);
  const headerRow6 = worksheet.getRow(6);
  const headers = [];

  // Assuming columns A through AZ
  for (let i = 1; i <= 52; i++) {
    const colAddress = worksheet.getColumn(i).letter;
    const value5 = headerRow5.getCell(i).value;
    const value6 = headerRow6.getCell(i).value;

    headers.push({
      column: colAddress,
      row5Value: value5,
      row6Value: value6
    });
  }

  console.log('Excel headers:', headers);
  return headers;
}
