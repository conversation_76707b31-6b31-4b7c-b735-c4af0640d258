import { IBaseInterface } from '../../../interfaces/base.interface';

export interface IDebtExpenseList extends IBaseInterface {
    id: string;
    debtCollector: object;                   
    code: string;         
    name: string;                  
    primaryTransaction: object;          
    policyPayment: object;        
    interestCalculations: object;
    penalty: object;
    penaltyPerContract: number;
    createdBy: string;
    modifiedBy: string;
    createdAt: Date;
    updatedAt: Date;
}