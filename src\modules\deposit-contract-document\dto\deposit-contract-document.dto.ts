import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateIf } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { trim } from 'lodash';

export class CreateDepositContractDocumentDto {
  @IsString()
  @IsNotEmpty()
  @Transform((value) => trim(value))
  @IsEnum(['folder', 'file'])
  type: string;

  @IsString()
  @IsNotEmpty()
  @Transform((value) => trim(value))
  name: string;

  @ValidateIf((o) => o.type === 'file') // Chỉ validate nếu type === 'file'
  @IsString()
  @IsNotEmpty()
  @Transform((value) => trim(value))
  path: string;

  @ValidateIf((o) => o.type === 'file') // Chỉ validate nếu type === 'file'
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number) // Ép kiểu số
  sizeFile: number;

  @Transform((value) => trim(value))
  @IsNotEmpty()
  @IsString()
  depositContractId: string;
}

export class UpdateDepositContractDocumentDto {
  @Transform((value) => trim(value))
  @IsNotEmpty()
  @IsString()
  id: string;

  @Transform((value) => trim(value))
  @IsNotEmpty()
  @IsString()
  depositContractId: string;

  @Transform((value) => trim(value))
  @IsNotEmpty()
  @IsString()
  name: string;
}

export class AddItemDepositContractDocumentDto {
  @IsString()
  @IsNotEmpty()
  @Transform((value) => trim(value))
  name: string;

  @IsString()
  @IsNotEmpty()
  @Transform((value) => trim(value))
  path: string;

  @IsNumber()
  @IsNotEmpty()
  sizeFile: number;

  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  id: string;
}

export class UpdateItemDepositContractDocumentDto {
  @IsString()
  @IsNotEmpty()
  @Transform((value) => trim(value))
  name: string;
}