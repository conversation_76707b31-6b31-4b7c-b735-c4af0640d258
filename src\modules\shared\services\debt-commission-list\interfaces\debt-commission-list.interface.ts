import { IBaseInterface } from '../../../interfaces/base.interface';

export interface IDebtCommissionList extends IBaseInterface {
  id: string;
  index: number;
  code: string;
  commission: object;                     // Chi tiết kỳ tính phí/hoa hồng
  commissionPolicy: any;                  // chính sách hoa hồng nhân viên
  project: object;                        // Dự án
  pos: object;
  debtage: object;
  propertyUnit: ICommissionListPropertyUnit;
  customer: any;
  contract: ICommissionListContract;
  debtType: string;                         // Loại nợ
  debtRate: number;                         // tỉ lệ thu hồi
  vatRate: number;                          // VAT
  debtRevenue: number;                      // Doanh thu thu hồi
  employees: any[];

  adjustmentData?: any[];
}

export interface ICommissionListPropertyUnit {
  id: string;                // id sản phẩm
  type: string;              // Loại sản phẩm
  view1: string;             // Mã sản phẩm
}

export interface ICommissionListContract {
  code: string;               //
  name: string;               //
  type: string;               //
}

export interface ICommissionListCustomer {
  name: string;               // Tên khách hàng
}

export interface ICommissionListPos {
  code: string;               // Mã pos (ERP)
  name: string;
}
