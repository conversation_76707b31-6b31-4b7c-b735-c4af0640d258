import { CqrsModule } from "@nestjs/cqrs";
import { Module } from "@nestjs/common";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { LoggerModule } from "../logger/logger.module";
import { CodeGenerateModule } from "../code-generate/module";
import { PolicyQuerySideModule } from "../policy.queryside/module";
import { HandoverQuerySideModule } from "../handover.queryside/module";
import { LiquidationQuerySideModule } from "../liquidation.queryside/module";
import { TransferHistoryModule } from "../transfer-history/module";
import { HistoryImportQuerySideModule } from "../import-history.queryside/module";
import { HandoverScheduleQuerySideModule } from "../handover-schedule.queryside/module";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { AuthModule } from "../auth/auth.module";
import { EmployeeQuerySideModule } from "../employee.query/module";
import { ScheduleQuerySideModule } from "../schedule.queryside/module";
import { StatushistoryModule } from "../statusHistory/module";
import { StaticAssetModule } from "../config/static-asset.module";
import { PrimaryContractController } from "./controller";
import { PrimaryContractService } from "./service";
import { FileGenerationService } from "./file-generation.service";
import { PrimaryContractRepository } from "./repository/primary-contract-query.repository";
import { HandoverScheduleDomainModule } from "../handover-schedule.domain/module";
import { QueryProviders } from "../primary-contract.queryside/providers/query.cqrs.providers";
import { PrimaryContractQueryCommandHandlers } from "../primary-contract.queryside/commands/handlers";
import { PrimaryContractQueryEventHandlers } from "../primary-contract.queryside/events";
import { PrimaryContractQueryRepository } from "../primary-contract.queryside/repository/primary-contract-query.repository";

@Module({
  imports: [
    CqrsModule,
    MgsSenderModule,
    LoggerModule,
    CodeGenerateModule,
    PolicyQuerySideModule,
    HandoverQuerySideModule,
    LiquidationQuerySideModule,
    TransferHistoryModule,
    HistoryImportQuerySideModule,
    HandoverScheduleQuerySideModule,
    HandoverScheduleDomainModule,
    QueryDatabaseModule,
    AuthModule,
    EmployeeQuerySideModule,
    ScheduleQuerySideModule,
    StatushistoryModule,
    StaticAssetModule,
  ],
  controllers: [PrimaryContractController],
  providers: [
    PrimaryContractQueryRepository,
    PrimaryContractService,
    FileGenerationService,
    PrimaryContractRepository,
    ...QueryProviders,
    ...PrimaryContractQueryCommandHandlers,
    ...PrimaryContractQueryEventHandlers,
  ],
  exports: [
    PrimaryContractQueryRepository,
    PrimaryContractService,
    PrimaryContractModule,
    FileGenerationService,
    PrimaryContractRepository,
  ],
})
export class PrimaryContractModule {}
