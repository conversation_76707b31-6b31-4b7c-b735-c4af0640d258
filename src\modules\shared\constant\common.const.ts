export class CommonConst {

  static REGEX_EMAIL = /^[0-9-A-z][A-z0-9_\.-]{1,32}@[A-z0-9-_]{2,}(\.[A-z0-9]{2,}){1,2}$/;
  static REGEX_VN_PHONE = /[0-9\+]{9,15}/gm;
  static DEBT_COMMISSION_POLICY_PREFIX = '203';
  static INTEREST_PREFIX = '205';
  static CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN = 'ContractImportHistory-Query-ModelToken';
  static CONTRACT_IMPORT_HISTORY_COLLECTION = 'contract-import-histories';

  static DATABASE_SERVICE_READ_MODEL = "msx-primary-contract-readmodel";
  static DATABASE_SERVICE_EVENTS_MODEL = "msx-primary-contract-eventstore";
  static HEADER_PARTERN_STR = "pattern";

  static DOMAIN_MODEL_TOKEN = "Domain-ModelToken";
  static QUERY_MODEL_TOKEN = "Query-ModelToken";
  static DOMAIN_CONNECTION_TOKEN = "PrimaryContract-Domain-DbConnectionToken";
  static QUERY_CONNECTION_TOKEN = "PrimaryContract-Query-DbConnectionToken";

  static POLICY_EVENTS = "events-policy";
  static POLICY_COLLECTION = "policies";
  static DISCOUNT_EVENTS = "events-discount";
  static DISCOUNT_COLLECTION = "discounts";
  static SCHEDULE_EVENTS = "events-schedule";
  static SCHEDULE_COLLECTION = "schedules";
  static TRANSFER_HISTORY_COLLECTION = "transfer-histories";
  static CONTRACT_EVENTS = "events-contract";
  static CONTRACT_COLLECTION = "contracts";
  static CODE_COLLECTION = "code-generates";
  static EMPLOYEE_COLLECTION = "employees";

  static PROPOSAL_DOMAIN_MODEL_TOKEN = "Proposal-Domain-ModelToken";
  static PROPOSAL_QUERY_MODEL_TOKEN = "Proposal-Query-ModelToken";
  static PROPOSAL_EVENTS = "events-proposal";
  static PROPOSAL_AGGREGATE_NAME = "proposal";
  static PROPOSAL_COLLECTION = "proposals";

  static OWNERSHIP_CETIFICATE_DOMAIN_MODEL_TOKEN = "OwnershipCertificate-Domain-ModelToken";
  static OWNERSHIP_CETIFICATE_QUERY_MODEL_TOKEN = "OwnershipCertificate-Query-ModelToken";
  static OWNERSHIP_CETIFICATE_EVENTS = "events-ownership-certificate";
  static HOWNERSHIP_CETIFICATE_AGGREGATE_NAME = "ownership-certificate";
  static OWNERSHIP_CETIFICATE_COLLECTION = "ownership-certificates";

  static DEBT_COMMISION_POLICY_DOMAIN_MODEL_TOKEN = "debtCommissionPolicy-Domain-ModelToken";
  static DEBT_COMMISION_POLICY_QUERY_MODEL_TOKEN = "debtCommissionPolicy-Query-ModelToken";
  static DEBT_COMMISION_POLICY_COLLECTION = "debt-commission-policy";

  static HANDOVER_DOMAIN_MODEL_TOKEN = "Handover-Domain-ModelToken";
  static HANDOVER_QUERY_MODEL_TOKEN = "Handover-Query-ModelToken";
  static HANDOVER_EVENTS = "events-handover";
  static HANDOVER_AGGREGATE_NAME = "handover";
  static HANDOVER_COLLECTION = "handovers";

  static HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN = "Handover-Domain-ModelToken";
  static HANDOVER_REQUEST_QUERY_MODEL_TOKEN = "Handover-Query-ModelToken";
  static HANDOVER_REQUEST_EVENTS = "events-handover-request";
  static HANDOVER_REQUEST_AGGREGATE_NAME = "handover-request";
  static HANDOVER_REQUEST_COLLECTION = "handovers-request";

  static HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN = "Handover-Domain-ModelToken";
  static HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN = "Handover-Query-ModelToken";
  static HANDOVER_SCHEDULE_EVENTS = "events-handover-schedule";
  static HANDOVER_SCHEDULE_AGGREGATE_NAME = "handover-schedule";
  static HANDOVER_SCHEDULE_COLLECTION = "handovers-schedule";

  static STATUSHISTORY_QUERY_MODEL_TOKEN = "Statushistory-Query-ModelToken";
  static STATUSHISTORY_AGGREGATE_NAME = "statusHistory";
  static STATUSHISTORY_COLLECTION = "statusHistorys";

  static DEPOSIT_CONTRACT_DOCUMENT_COLLECTION = 'deposit-contract-document';
  static COMM_PERIOD_PREFIX = '109';

  // aggregate
  static AGGREGATE_NAME = "primary-contract";
  static POLICY_AGGREGATE_NAME = "policy";
  static PERIOD = 'period';
  static DEBT_COMMISSION = 'debt-commission';
  static DEBT_COMMISSION_LIST = 'debt-commission-list';
  static DEBT_EXPENSE_LIST= 'debt-expense-list';
  static TEMP_DEBT_COMMISSION_LIST = 'temp-debt-commission-list';
  static UNIT_AGGREGATE_NAME = "unit";
  static DISCOUNT_AGGREGATE_NAME = "discount";
  static SCHEDULE_AGGREGATE_NAME = "schedule";
  static EMAIL_THANH_TOAN_DOT_1 = 'EMAIL_THANH_TOAN_DOT_1';
  static EMAIL_THANH_TOAN_DOT_TIEP_THEO = 'EMAIL_THANH_TOAN_DOT_TIEP_THEO';
  static SMS_NHAC_NO_THANH_TOAN_DOT_1 = 'SMS_NHAC_NO_THANH_TOAN_DOT_1';
  static SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO = 'SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO';
  static HINH_THUC = {
    SO_HUU: {
      SU_DUNG_VINH_VIEN: 'Sử dụng vĩnh viễn',
      SU_DUNG_50_NAN: 'Sử dụng 50 năm',
    }
  }
  static LIQUIDATION_DOMAIN_MODEL_TOKEN = "Liquidation-Domain-ModelToken";
  static LIQUIDATION_QUERY_MODEL_TOKEN = "Liquidation-Query-ModelToken";
  static LIQUIDATION_EVENTS = "events-liquidation";
  static LIQUIDATION_AGGREGATE_NAME = "liquidation";
  static LIQUIDATION_COLLECTION = "liquidations";
  static AGGREGATES_LISTENER = {
    EMPLOYEE: {
      NAME: 'employee',
      CREATED: 'employeeCreated',
      UPDATED: 'employeeUpdated',
      LIST_UPDATED: 'employeeListUpdated',
      DELETED: 'employeeDeleted',
    }
  }
  static DEPOSIT_CONTRACT_AGGREGATE_NAME = "deposit-contract";
  static AGGREGATE_NAMES(): Object[] {
    return Object.keys(this.AGGREGATES).map((key) => this.AGGREGATES[key].NAME);
  }

  static AGGREGATES = {
    PROPOSAL: {
      NAME: CommonConst.PROPOSAL_AGGREGATE_NAME,
      CREATED: CommonConst.PROPOSAL_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.PROPOSAL_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.PROPOSAL_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.PROPOSAL_AGGREGATE_NAME,
      COLLECTION: CommonConst.PROPOSAL_AGGREGATE_NAME,
    },
    PRIMARY_CONTRACT: {
      NAME: CommonConst.AGGREGATE_NAME,
      CREATED: CommonConst.AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.AGGREGATE_NAME,
      COLLECTION: CommonConst.AGGREGATE_NAME,
      CODE_PREFIX_DEPOSIT_CONTRACT: "HĐC-",// 
      CODE_PREFIX_TRANSFER_CONTRACT: "HĐCN-",// 
      CODE_PREFIX_PURCHASE_CONTRACT: "HDMB-",//
      CODE_PREFIX_PROPOSAL: "ĐNTL-",//
      CODE_PREFIX_LIQUIDATION: "BBTL-",//
      CODE_PREFIX_INTEREST: "TL-",//
    },
    POLICY: {
      NAME: CommonConst.POLICY_AGGREGATE_NAME,
      CREATED: CommonConst.POLICY_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.POLICY_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.POLICY_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.POLICY_AGGREGATE_NAME,
      COLLECTION: CommonConst.POLICY_AGGREGATE_NAME,
      CODE_PREFIX_DISCOUNT: "CSCK-",//Chính sách chiết khấu
      CODE_PREFIX_PAYMENT: "CSTT-",//Chính sách thanh toán
    },
    DISCOUNT: {
      NAME: CommonConst.DISCOUNT_AGGREGATE_NAME,
      CREATED: CommonConst.DISCOUNT_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.DISCOUNT_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.DISCOUNT_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.DISCOUNT_AGGREGATE_NAME,
      COLLECTION: CommonConst.DISCOUNT_AGGREGATE_NAME,
      CODE_PREFIX: "CK-",//Chiết khấu
    },
    SCHEDULE: {
      NAME: CommonConst.SCHEDULE_AGGREGATE_NAME,
      CREATED: CommonConst.SCHEDULE_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.SCHEDULE_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.SCHEDULE_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.SCHEDULE_AGGREGATE_NAME,
      COLLECTION: CommonConst.SCHEDULE_AGGREGATE_NAME,
      CODE_PREFIX_PURCHASE: "TDTT-",//Tiến độ thanh toán
      CODE_PREFIX_LOAN: "TDGV-",//Tiến độ góp vốn
    },
    LIQUIDATION: {
      NAME: CommonConst.LIQUIDATION_AGGREGATE_NAME,
      CREATED: CommonConst.LIQUIDATION_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.LIQUIDATION_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.LIQUIDATION_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.LIQUIDATION_AGGREGATE_NAME,
      COLLECTION: CommonConst.LIQUIDATION_AGGREGATE_NAME,
    },
    HANDOVER: {
      NAME: CommonConst.HANDOVER_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.HANDOVER_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.HANDOVER_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.HANDOVER_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_AGGREGATE_NAME,
    },
    HANDOVER_REQUEST: {
      NAME: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
    },
    HANDOVER_SCHEDULE: {
      NAME: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
    },
    STATUSHISTORY: {
      NAME: CommonConst.STATUSHISTORY_AGGREGATE_NAME,
      CREATED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.STATUSHISTORY_AGGREGATE_NAME,
      COLLECTION: CommonConst.STATUSHISTORY_AGGREGATE_NAME,
    },
    PERIOD: {
      NAME: CommonConst.PERIOD,
      CREATED: CommonConst.PERIOD + 'Created',
      UPDATED: CommonConst.PERIOD + 'Updated',
      DELETED: CommonConst.PERIOD + 'Deleted',
      EVENTS: 'events-' + CommonConst.PERIOD,
    },
    DEPOSIT_CONTRACT: {
      NAME: CommonConst.DEPOSIT_CONTRACT_AGGREGATE_NAME,
      CREATED: CommonConst.DEPOSIT_CONTRACT_AGGREGATE_NAME + "Created",
      UPDATED: CommonConst.DEPOSIT_CONTRACT_AGGREGATE_NAME + "Updated",
      DELETED: CommonConst.DEPOSIT_CONTRACT_AGGREGATE_NAME + "Deleted",
      EVENTS: "events-" + CommonConst.DEPOSIT_CONTRACT_AGGREGATE_NAME,
      COLLECTION: CommonConst.DEPOSIT_CONTRACT_AGGREGATE_NAME,
      CODE_PREFIX_DEPOSIT_CONTRACT: "HDKQ-",// 
      // CODE_PREFIX_TRANSFER_CONTRACT: "HĐCN-",// 
      // CODE_PREFIX_PURCHASE_CONTRACT: "HDMB-",//
      // CODE_PREFIX_PROPOSAL: "ĐNTL-",//
      // CODE_PREFIX_LIQUIDATION: "BBTL-",//
      // CODE_PREFIX_INTEREST: "TL-",//
    },
    COMMISSION_DEBT: {
      NAME: CommonConst.DEBT_COMMISSION,
      CREATED: CommonConst.DEBT_COMMISSION + 'Created',
      UPDATED: CommonConst.DEBT_COMMISSION + 'Updated',
      DELETED: CommonConst.DEBT_COMMISSION + 'Deleted',
    },
    DEBT_COMMISSION_LIST: {
      NAME: CommonConst.DEBT_COMMISSION_LIST,
      CREATED: CommonConst.DEBT_COMMISSION_LIST + 'Created',
      UPDATED: CommonConst.DEBT_COMMISSION_LIST + 'Updated',
      DELETED: CommonConst.DEBT_COMMISSION_LIST + 'Deleted',
      TEMP: CommonConst.TEMP_DEBT_COMMISSION_LIST,
    },
    DEBT_EXPENSE_LIST: {
      NAME: CommonConst.DEBT_EXPENSE_LIST,
      CREATED: CommonConst.DEBT_EXPENSE_LIST + 'Created',
      UPDATED: CommonConst.DEBT_EXPENSE_LIST + 'Updated',
      DELETED: CommonConst.DEBT_EXPENSE_LIST + 'Deleted',
      TEMP: CommonConst.DEBT_EXPENSE_LIST,
    }
  };
  static PRIMARY_CONTRACT = "primaryContract_";
  static BIEN_BAN_THANH_LY = "Biên bản thanh lý";
}
