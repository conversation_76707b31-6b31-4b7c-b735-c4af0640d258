import { IBaseInterface } from "../../../interfaces/base.interface";

export interface IDebtCommission extends IBaseInterface {
    id: string;
    isPublish: boolean;
    publishTo: string[];
    name: string; // tên kỳ
    period: string;                   // <PERSON>ỳ hạn
    periodFrom: string;                   // Kỳ hạn từ
    periodTo: string;                     // Kỳ hạn đến
    year: string;                     // năm
    code: string;                       // Mã đợt tính phí / hoa hồng
    project: object;                        // Đơn vị
    status: string;                     // Trạng thái NEW/CONFIRMED
    adjustmentVersions: any;
    commissionPolicy: any;
    modifiedDate: Date;
    createdBy: string;
    createdDate: Date;
}
