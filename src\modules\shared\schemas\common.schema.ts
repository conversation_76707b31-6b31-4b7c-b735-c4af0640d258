import * as mongoose from 'mongoose';

const businessAreaSchema = new mongoose.Schema(
  {
    code: { type: String },
    name: { type: String },
  },
  {
    _id: false
  }
);

export const ProjectSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  code: { type: String },
  setting: { type: Object },
  imageUrl: {type: String},
  businessArea: { type: businessAreaSchema, default: null },
  companyCode: { type: String, default: null }
}, { _id: false });

export const TransactionSchema = new mongoose.Schema({
  id: { type: String },
  bookingTicketCode: { type: String },
  escrowTicketCode: { type: String },
  employee: { type: Object },
  customer: { type: Object },
  customer2: { type: Object },
  project: { type: ProjectSchema },
  propertyUnit: { type: Object },
  ticketType: { type: String },
}, { _id: false });

export const PosSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  code: { type: String },
}, { _id: false });

export const ContractSchema = new mongoose.Schema({
  id: { type: String },
  code: { type: String },
  name: { type: String },
  type: { type: String },
}, { _id: false });

export const CommissionSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  code: { type: String },
  period: { type: String },
  type: { type: String },
}, { _id: false });

export const PropertyUnitSchema = new mongoose.Schema({
  id: { type: String },
  view1: { type: String },
  price: { type: Number },
}, { _id: false });

export const CommissionEmpSchema = new mongoose.Schema({
  debtRevenue: { type: Number },
  totalDebtRevenue: { type: Number },
  recordedCommission: { type: Number },
  unit: { type: String },
  debtRate: { type: Number },
  commissionVatRate: { type: Number },
  debtCommissionRevenue: { type: Number },
}, { _id: false });

export const EmployeeSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  code: { type: String },
  commissions: { type: [CommissionEmpSchema] },
}, { _id: false });

export const CustomerSchema = new mongoose.Schema({
  id: { type: String },
  code: { type: String },
  name: { type: String },
}, { _id: false });

export const EmployeeInfoSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  code: { type: String },
  email: { type: String },
  pos: { type: PosSchema }
}, { _id: false });

export const AdjustmentVersion = new mongoose.Schema({
  id: { type: String },
  version: { type: String },
  fileName: { type: String },
  fileUrl: { type: String },
  filePath: { type: String },
  status: { type: String }, // trạng thái công bố/phê duyệt CommissionStatusEnum2
  uploadDate: { type: Date },
  uploadBy: { type: String },
}, { _id: false });

