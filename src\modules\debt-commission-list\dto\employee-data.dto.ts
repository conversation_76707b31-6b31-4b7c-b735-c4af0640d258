import { IDebtPolicy } from "../interfaces/document.interface";

export class EmployeeDataDto {
    index: number;
    code: string;
    name: string;
    installmentName: string;
    contract: ContractDto;
    project: ProjectDataDto;
    propertyUnit: PropertyUnitDataDto;
    pos: PosDataDto;
    customer: CustomerDataDto;
    debtCollector: DebtCollector;
    commissionPolicy: IDebtPolicy;                      // KPI policy
    debtage: DebtAge;

    // Calculate
    vatRate: number;                            // [Tính toán] VAT
    debtType: string;                           // [Tính toán] Loại nợ
    debtRate: number;                           // [Tính toán] Tỷ lệ thu hồi
    debtRevenue: number;                        // [Tính toán] Doanh thu thu hồi
    commissions: CommissionDataDto[];           // [Tính toán] Hoa hồng công nợ

    constructor(init?: Partial<EmployeeDataDto>) {
        Object.assign(this, init);
    }
}

export class ContractDto {
    id: string;
    type: string;
    name: string;
    code: string;

    constructor(init?: Partial<ContractDto>) {
        Object.assign(this, init);
    }
}

export class ProjectDataDto {
    id: string;
    name: string;
    code: string;                               // [Tải nhập] Mã dự án

    constructor(init?: Partial<ProjectDataDto>) {
        Object.assign(this, init);
    }
}

export class PosDataDto {
    id: string;
    name: string;
    code: string;                               // Mã Pos

    constructor(init?: Partial<PosDataDto>) {
        Object.assign(this, init);
    }
}

export class CustomerDataDto {
    code: string;
    name: string;                               // Tên khách hàng

    constructor(init?: Partial<CustomerDataDto>) {
        Object.assign(this, init);
    }
}

export class PropertyUnitDataDto {
    id: string;                                 // id sản phẩm
    type: string;                               // Loại sản phẩm
    view1: string;                              // Mã sản phẩm

    constructor(init?: Partial<PropertyUnitDataDto>) {
        Object.assign(this, init);
    }
}

export class CommissionDataDto {
    debtRevenue: number;
    totalDebtRevenue: number;
    recordedCommission: number;
    unit: string;
    debtRate: number;
    commissionVatRate: number;
    debtCommissionRevenue: number;

    constructor(init?: Partial<CommissionDataDto>) {
        Object.assign(this, init);
    }
}

export class DebtCollector {
    id: string;
    accountId: string;
    code: string;
    name: string;

    constructor(init?: Partial<DebtCollector>) {
        Object.assign(this, init);
    }
}

export class DebtAge {
    name: string;

    constructor(init?: Partial<DebtAge>) {
        Object.assign(this, init);
    }
}