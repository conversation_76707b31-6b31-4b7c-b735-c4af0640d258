import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { CmdPatternConst, CommonConst } from '../shared/constant';
import { EmployeeService } from '../employee.query/service';
import { PrimaryContractQueryService } from '../primary-contract.queryside/service';
import { PrimaryContractQueryRepository } from '../primary-contract.queryside/repository/primary-contract-query.repository';
import { PrimaryContractDomainService } from '../primary-contract.domain/service';
import { MsxLoggerService } from '../logger/logger.service';
import { LiquidationDomainService } from "../liquidation.domain/service";
import { HandoverScheduleQueryService } from '../handover-schedule.queryside/service';
import { StatushistoryService } from "../statusHistory/application/service";
import { Action } from "../shared/enum/action.enum";
import { PolicyQueryService } from "../policy.queryside/service";
import { ScheduleQueryService } from "../schedule.queryside/service";
import { ErpStatusEnum, TransactionStatusEnum } from "../shared/enum/status.enum";
import { HandoverScheduleDomainService } from '../handover-schedule.domain/service';
import { HandoverQueryRepository } from '../handover.queryside/repository/handover.query.repository';
const moment = require('moment');
import { CmdPatternConst as cmdPatternConstVersion2 } from '../../../shared-modules';
import { OwnershipCertificateQueryRepository } from '../ownership-certificate/repository/ownership-certificate.query.repository';
import { PrimaryContractService } from '../primary-contract/service';
import { DebtCommissionPolicyService } from '../debt-commission-policy/service';

@Controller('listener')
export class ListenerController {
    private readonly context = ListenerController.name;

    constructor(
        private readonly employeeService: EmployeeService,
        private readonly primaryContractDomainService: PrimaryContractDomainService,
        private readonly primaryContractQueryService: PrimaryContractQueryService,
        private readonly liquidationDomainService: LiquidationDomainService,
        private readonly primaryContractQueryRepository: PrimaryContractQueryRepository,
        private readonly ownershipCertificateQueryRepository: OwnershipCertificateQueryRepository,
        private readonly loggerService: MsxLoggerService,
        private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
        private readonly statushistoryService: StatushistoryService,
        private readonly policyQueryService: PolicyQueryService,
        private readonly scheduleQueryService: ScheduleQueryService,
        private readonly handoverScheduleDomainService: HandoverScheduleDomainService,
        private readonly handoverQueryRepository: HandoverQueryRepository,
        private readonly primaryContractService: PrimaryContractService,
        private readonly debtCommissionPolicyService: DebtCommissionPolicyService,
    ) { }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.EMPLOYEE })
    listenerEmployee(pattern: any) {
        const data = pattern.data;
        const action = data.action;
        const model = data.model;
        switch (action) {
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.CREATED:
                this.employeeService.create(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.UPDATED:
                this.employeeService.update(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.DELETED:
                this.employeeService.delete(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.LIST_UPDATED:
                this.employeeService.updateList(model);
                break;
            default:
                break;
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT })
    async listenerPrimaryContract(pattern: any) {
        const data = pattern.data;
        if (data.id) {
            return this.primaryContractQueryService.getContractById(null, data.id);
        } else if (data.oldContractId) {
            return this.primaryContractQueryRepository.findOne({ "oldContract.id": data.oldContractId })
        } else if (data.transaction) {
            await this.primaryContractService.syncContractTransaction(data.transaction);
        } else {
            return null;
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_ONE_HANDOVER_SETTING })
    async listenerGetHandoverSetting(pattern: any) {
        this.loggerService.log(this.context, 'GET_HANDOVER_SETTING');
        const data = pattern.data;
        let handover = await this.handoverQueryRepository.findOne(data)
        return handover;
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.GET_ONE_WNERSHIP_CETIFICATE_SETTING })
    async listenerGetOwnershipCertificateSetting(pattern: any) {
        this.loggerService.log(this.context, 'GET_ONE_WNERSHIP_CETIFICATE_SETTING');
        const data = pattern.data;
        let handover = await this.ownershipCertificateQueryRepository.findOne(data)
        return handover;
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.GET_CONTRACT_BY_PROPERTY_UNIT_ID })
    async getContractByPropertyUnitId(pattern: any) {
        const data = pattern.data;
        let contract = await this.primaryContractQueryRepository.findOne(data)
        return contract;
    }
    @MessagePattern({ cmd: CmdPatternConst.PRIMARY_CONTRACT.CREATE_CONTRACT_SYNC_ERP })
    async createContractSyncErp(pattern: any) {
        this.loggerService.log(this.context, 'createContractSyncErp');
        const data = pattern.data;
        return await this.primaryContractDomainService.createdContractSyncErp(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_ONE_BY_QUERY })
    async listenerPrimaryContractByQuery(pattern: any) {
        this.loggerService.log(this.context, 'listenerPrimaryContractTimout');
        const data = pattern.data;
        return this.primaryContractQueryRepository.findOne(data)
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_PATHDTT })
    async listenerPrimaryContractUpdatePathDtt(pattern: any) {
        this.loggerService.log(this.context, 'listenerPrimaryContractUpdatePathDtt');
        const data = pattern.data;

        const contract = await this.primaryContractQueryRepository.findOne({ "syncErpData.contractid": data.contractid });

        if (!contract) {
            return;
        }

        const installments = contract.policyPayment.schedule.installments.map(i => {
            const erpInstallments = data.pathDtt.filter(p => p.value0 === i.name);
            if (erpInstallments.length === 0) {
                return i;
            }
            const totalAmount = erpInstallments.reduce(function (prev, cur) {
                return prev + parseInt(cur.amount);
            }, 0);
            const erpInstallment = erpInstallments.pop();
            i.duedate = erpInstallment.transdate ? moment(erpInstallment.transdate, 'DD/MM/YYYY HH:mm:ss A').toDate() : '';
            i.totalAmount = totalAmount;
            return i;
        });

        const $set: any = {
            changeInstallment: true,
            'policyPayment.schedule.installments': installments,
        };

        const maintenance = data.pathDtt.find(p => p.value0 === '99');
        if (maintenance) {
            $set.maintenanceFee = {
                type: 'currency',
                value: parseInt(maintenance.amount),
                stagePayment: '99',
            }
        }

        const q = {
            query: {
                "syncErpData.contractid": data.contractid,
            },
            model: {
                $set,
                $push: { "pathDtt": data.pathDtt }
            }
        };
        return await this.primaryContractQueryRepository.updateOne(q.query, q.model);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_RECEIPT_AMOUNT })
    async listenerPrimaryContractUpdateReceipt(pattern: any) {
        this.loggerService.log(this.context, 'listenerPrimaryContractUpdateReceipt');
        const data = pattern.data;
        const id = data.id;
        const transaction = data.transaction;
        const contract: any = await this.primaryContractQueryRepository.findOne({ id });
        const instalment = transaction.syncErpData.value0;
        let receipt = null;
        let existReceiptId = null;
        contract.policyPayment.schedule.installments = contract.policyPayment.schedule.installments.map(i => {
            let amountDiff = 0;
            i.receipts = i.receipts.map(r => {
                if (r.id === transaction.id) {
                    existReceiptId = r.id;
                    amountDiff = (instalment !== i.name) ? 0 - r.amount : transaction.money - r.amount;
                    r.amount = transaction.money;
                    // thay đổi đợt thanh toán
                    if (instalment !== i.name) {
                        receipt = r;
                    }
                }
                return r;
            })
            i.totalTransfered = i.totalTransfered + amountDiff;
            return i;
        })

        if (receipt) {
            contract.policyPayment.schedule.installments = contract.policyPayment.schedule.installments.map(i => {
                // nhét phiếu vào đợt mới, remove phiếu khỏi đợt cũ
                if (instalment === i.name) {
                    i.receipts.push(receipt);
                    i.totalTransfered = i.totalTransfered + receipt.amount;
                } else {
                    const existReceipt = i.receipts.find(r => r.id === transaction.id);
                    if (existReceipt) {
                        i.totalTransfered = i.totalTransfered - receipt.amount;
                    }
                    i.receipts = i.receipts.filter(r => r.id !== transaction.id);
                }
                return i;
            })
        }

        if (!existReceiptId) {
            // transform lại data
            transaction.amount = transaction.money;
            transaction.receiptDate = transaction.collectMoneyDate;
            transaction.status = TransactionStatusEnum.transfered;
            const simpleTransaction: any = (({ id, amount, code, status, type, receiptNum, receiptDate }) => ({ id, amount, code, status, type, receiptNum, receiptDate }))(transaction);

            contract.policyPayment.schedule.installments = contract.policyPayment.schedule.installments.map(i => {
                // bổ sung phiếu bị miss
                if (instalment === i.name) {
                    i.receipts.push(simpleTransaction);
                    i.totalTransfered = i.totalTransfered + simpleTransaction.amount;
                }
                return i;
            })
        }

        if (!existReceiptId) {
            // transform lại data
            transaction.amount = transaction.money;
            transaction.receiptDate = transaction.collectMoneyDate;
            transaction.status = TransactionStatusEnum.transfered;
            const simpleTransaction: any = (({ id, amount, code, status, type, receiptNum, receiptDate }) => ({ id, amount, code, status, type, receiptNum, receiptDate }))(transaction);

            contract.policyPayment.schedule.installments = contract.policyPayment.schedule.installments.map(i => {
                // bổ sung phiếu bị miss
                if (instalment === i.name) {
                    i.receipts.push(simpleTransaction);
                    i.totalTransfered = i.totalTransfered + simpleTransaction.amount;
                }
                return i;
            })
        }

        contract.paymentPercent = this.primaryContractDomainService.calPaymentPercentage({
            installments: contract.policyPayment.schedule.installments
        });

        return await this.primaryContractQueryRepository.updateOne({ id: contract.id }, contract);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.LIST_EMPLOYEE_HAND_OVER_BUSY })
    async listenerEmployeeHandOverBusy(pattern: any) {
        this.loggerService.log(this.context, 'listenerPrimaryContractTimout');
        const data = pattern.data;
        return await this.handoverScheduleDomainService.getEmployeeHandoverBusy(data.handoverConfigId, data.startTime, data.endTime);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_TIMEOUT })
    async listenerPrimaryContractTimout(pattern: any) {
        this.loggerService.log(this.context, 'listenerPrimaryContractTimout');
        this.primaryContractQueryService.getContractReminder();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM })
    async listenerPrimaryContractUpdateDepositConfirm(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM');
        this.primaryContractDomainService.updateDepositConfirm(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM })
    async listenerPrimaryContractUpdateTransferConfirm(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM');
        this.primaryContractDomainService.updateTransferConfirm(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_PRIMARY_CONTRACT_BY_CUSTOMER })
    async listenerGetPrimaryContractByCustomer(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'GET_PRIMARY_CONTRACT_BY_CUSTOMER');
        return await this.primaryContractQueryService.getByCustomer(data.user, data.query, data.getDetail);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID })
    async listenerGetEmployeeIdentitiesByProjectId(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID');
        return await this.primaryContractQueryService.getAllCustomerByProjectId(data);
    }
    @MessagePattern({ cmd: CmdPatternConst.LISTENER.REQUEST_APPROVE })
    async requestApproveContract(pattern: any) {
        const data = pattern.data;
        return await this.primaryContractDomainService.approveLiquidationContract(data.user, data.dto, 'requestApproveContract');
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.UPDATE_PROPERTY_UNITS })
    async updatePropertyUnits(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'UPDATE_PROPERTY_UNITS');
        return await this.primaryContractDomainService.updatePropertyUnits(data.units);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SYNC_PROJECT })
    async syncProject(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'SYNC_PROJECT');
        return await this.primaryContractQueryService.syncProject(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SYNC_PRIMARY_TRANSACTION })
    async syncPrimaryTransaction(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'SYNC_PRIMARY_TRANSACTION');
        return await this.primaryContractQueryService.syncPrimaryTransaction(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_PRIMARY_CONTRACT_BY_PROPERTY_UNIT_CODE_AND_PROJECT_ID })
    async getPrimaryContractsByPropertyUnitCodeProjectId(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'SYNC_GET_PRIMARY_CONTRACTS');
        return this.primaryContractQueryService.getPrimaryContractsByPropertyUnitCodeAndProjectId(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_PRIMARY_CONTRACTS_BY_PROPERTY_UNIT_CODES_AND_PROJECT_IDS })
    async getPrimaryContractsByPropertyUnitCodesProjectIds(pattern: any) {
        const data = pattern.data;
        this.loggerService.log(this.context, 'SYNC_GET_PRIMARY_CONTRACTS');
        return this.primaryContractQueryService.getPrimaryContractsByPropertyUnitCodesAndProjectIds(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.HANDOVER_SCHEDULE_NOTIFY_CUSTOMER })
    async handoverScheduleNotifyCustomer(pattern: any) {
        this.loggerService.log(this.context, 'SYNC_HANDOVER_SCHEDULE_NOTIFY');
        return this.handoverScheduleQueryService.handoverScheduleNotifyCustomer();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.CHECK_DELIVERY_SCHEDULE_END_DATE })
    async checkDeliveryScheduleEndDate(pattern: any) {
        this.loggerService.log(this.context, 'CHECK_DELIVERY_SCHEDULE_END_DATE');
        return this.handoverScheduleQueryService.checkDeliveryScheduleEndDate();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SEND_NOTICE_DELIVERY_BEFORE_DEADLINE })
    async sendNoticeDeliveryBeforeDeadline(pattern: any) {
        this.loggerService.log(this.context, 'SEND_NOTICE_DELIVERY_BEFORE_DEADLINE');
        // return this.handoverScheduleQueryService.sendNoticeDeliveryBeforeDeadline();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.UPDATE_TRADEHISTORY_SYNC_ERP })
    async updateTradeHistorySyncErp(pattern: any) {
        this.loggerService.log(this.context, 'UPDATE_TRADEHISTORY_SYNC_ERP');
        const data = pattern.data;
        return await this.statushistoryService.updateTradeHistory(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.CREATE_PAYMENT_POLICY_SYNC_ERP })
    async createPaymentPolicySyncErp(pattern: any) {
        this.loggerService.log(this.context, 'CREATE_PAYMENT_POLICY_SYNC_ERP');
        const data = pattern.data;
        const syncErpData = data.syncErpData;
        const project = data.project;
        const userId = data.userId;

        // check tồn tại
        const oldPolicy: any = await this.policyQueryService.findOne({ pymtterm: syncErpData[0].pymtterm });
        const scheduleId = oldPolicy ? oldPolicy.schedule.id : null;
        const policyId = oldPolicy ? oldPolicy.id : null;

        const schedule = await this.scheduleQueryService.createScheduleSyncErp(syncErpData, userId, scheduleId);
        await this.policyQueryService.createPaymentPolicySyncErp(syncErpData, project, schedule, userId, policyId);

        return {
            isSuccess: true
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_HANDED_CONTRACT_BY_PROJECT_ID })
    async handedContractByProjectId(pattern: any) {
        const projectId = pattern.data;
        return await this.primaryContractQueryService.handedContractByProjectId(projectId);
    }

    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.GET_TRANSACTION_HISTORY })
    async getTransactionHistory(pattern: any) {
        const data = pattern.data;
        const query = data.query || {};
        return this.primaryContractQueryService.getTransactionHistory(data.customer, query);
    }

    // get transaction to calculate commission
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.GET_BY_QUERY })
    async getPrimaryTxs(pattern: any) {
        const data = pattern.data;
        console.log('data', data);
        return await this.primaryContractQueryRepository.getCommissionTxs(data);
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.GET_BY_QUERY_STAFF_INVOLVED })
    async getPrimaryTxsByStaffInvolved(pattern: any) {
        const data = pattern.data;
        console.log('data', data);
        return await this.primaryContractQueryRepository.getCommissionTxsByStaffsInvolved(data);
    }

    @MessagePattern({ cmd: cmdPatternConstVersion2.SCHEDULE.CREATE_INTEREST })
    async createInterestCalculation(pattern: any) {
        return await this.primaryContractService.updateInterestCalculations();
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.PROPOSAL_FOR_REINSTATING_CONTRACT_INTO_DEBT_LIST })//phục hồi hợp đồng vào danh sách công nợ
    async listenerUpdateIsInterestExemptionIsFalse(data: any) {
        data = data.data.commandPayload;
        return await this.primaryContractService.updateIsInterestExemptionIsFalse(data);
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.PROPOSAL_FOR_EXCLUDING_CONTRACT_FROM_DEBT_LIST })//loại trừ hợp đồng khỏi danh mục công nợ
    async listenerUpdateIsInterestExemptionIsTrue(data: any) {
        data = data.data.commandPayload;
        return await this.primaryContractService.updateIsInterestExemptionIsTrue(data);
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.DEBT_COMMISSION_POLICY_PROPOSAL })
    async listenerUpdateStatusDebtCommissionPolicy(data: any) {
        data = data.data.commandPayload;
        return await this.debtCommissionPolicyService.updateForEapp(data);
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.PROPOSAL_FOR_INTEREST_WAIVER_OR_REDUCTION })
    async listenerUpdateInterestCalculations(data: any) {
        data = data.data.commandPayload;
        return await this.primaryContractService.updateInterestCalculationDetail(data);
    }
    @MessagePattern({ cmd: cmdPatternConstVersion2.PRIMARY_CONTRACT.CHECK_CONDITION_TO_UPDATE_DEBT_PENALTY })
    async checkConditionToUpdateDebtPenalty(pattern: any) {
        const data = pattern.data;
        return await this.primaryContractService.checkConditionToUpdateDebtPenalty(data);
    }
    //tích hợp hợp đồng case chuyển nhưởng cần chờ update thành công mới đẩy sap
    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SEND_SAP_LIQUIDATED_CONTRACT })
    async handleSendSap(pattern: any) {
        const { oldContract, newContract } = pattern.data;
        // Sau 3 phút mới xử lý
        setTimeout(async () => {
            try {
                await this.primaryContractService.sendSap(oldContract);
                await this.primaryContractService.sendSap(newContract);
            } catch (error) {
                console.error('Lỗi xử lý sendSap sau 3 phút:', error);
            }
        }, 3 * 60 * 1000);
    }
}
