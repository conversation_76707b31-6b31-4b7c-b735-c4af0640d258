import * as mongoose from 'mongoose';
import uuid = require('uuid');

export const commissionRateQuerySchema = new mongoose.Schema({
    topPrice: { type: Number, default: null },
    unit: { type: String, default: null },
    recordedCommissionUnit: { type: String, default: null },
    recordedCommission: { type: Number, default: null },
    bottomPrice: { type: Number, default: null },
}, { _id: false });
export const projectSchema = new mongoose.Schema({
    id: { type: String, default: null },
    code: { type: String, default: null },
    name: { type: String, default: null },
}, { _id: false });
export const penaltySchema = new mongoose.Schema({
    name: { type: String, default: null },
    amount: { type: Number, default: null },
    unit: { type: String, default: null },
    type: { type: String, default: null },
}, { _id: false });
export const projectDebtSchema = new mongoose.Schema({
    rate: { type: Number, default: null },
    isProgressive: { type: Boolean, default: false },
    type: { type: String, default: null },
    isVAT: { type: Boolean, default: false },
    listRate: { type: [commissionRateQuerySchema], default: null },
}, { _id: false });
export const DebtCommissionPolicySchema = new mongoose.Schema({
    _id: { type: String },
    modifiedBy: { type: Object, default: {} },
    modifiedDate: { type: Date, default: () => Date.now() },
    createdBy: { type: Object, default: {} },
    createdDate: { type: Date, default: () => Date.now() },
    reasonDelete: { type: String, default: null },
    softDelete: { type: Boolean, default: false },
    id: { type: String, default: uuid.v4, index: true },
    code: { type: String, index: true },
    name: { type: String, default: null },
    isInterestSupport: { type: Boolean, default: false },

    period: { type: String, default: '' },
    year: { type: Number },
    periodFrom: { type: Date, default: null },
    periodTo: { type: Date, default: null },
    periodName: { type: String, default: null },
    isActive: { type: Number, default: 1 },

    project: { type: projectSchema, default: {} },
    penalty: { type: [penaltySchema], default: [] },

    projectDebt: { type: projectDebtSchema, default: {} },
    badDebt: { type: projectDebtSchema, default: {} },
});

DebtCommissionPolicySchema.pre('save', function (next) {
    this._id = this.get('id');
    next();
});
