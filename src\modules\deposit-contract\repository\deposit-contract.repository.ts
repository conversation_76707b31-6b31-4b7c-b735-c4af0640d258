import { Inject, Injectable, BadRequestException, HttpException, HttpStatus } from "@nestjs/common";
import { isBoolean, isEmpty } from "lodash";
import { Model } from "mongoose";
import { BaseRepository } from "../../../../shared-modules";
import { CommonConst } from "../../shared/constant";
import { BulkWriteOperation } from "mongodb";
import { IDepositContract } from "../interfaces/deposit-contract.interface";
import { DepositContractRepositoryInterface } from "./deposit-contract.repository.interface";


@Injectable()
export class DepositContractRepository
  extends BaseRepository<IDepositContract>
  implements DepositContractRepositoryInterface {
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly depositContractModel: Model<IDepositContract>
  ) {
    super(depositContractModel);
  }

  // async bulkWriteEmployees(operations: BulkWriteOperation<IDepositContract>[]): Promise<void> {
  //   await this.depositContractModel.bulkWrite(operations);
  // }

  // async updateOne(query: any, data: Partial<IDepositContract>): Promise<IDepositContract> {
  //   return await this.depositContractModel.findOneAndUpdate(query
  //     , data, { new: true });
  // }
}
