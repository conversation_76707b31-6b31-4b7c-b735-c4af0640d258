import { Model } from "mongoose";
import { Inject, Injectable } from "@nestjs/common";
import { QueryAggregateModel } from "../models/query-aggregate.model";
import { IPrimaryContractDocument } from "../interfaces/document.interface";
import { IResult } from "../../shared/interfaces/result.interface";
import _ = require("lodash");
import { CommonConst } from "../../shared/constant/index";
import { EmployeeClient } from "../../mgs-sender/employee.client";
import { StsClient } from "../../mgs-sender/sts.client";
import { CustomerClient } from "../../mgs-sender/customer.client";
import { ContractEnum, StatusContractEnum, StatusEnum } from "../../shared/enum/primary-contract.enum";
import { CommonUtils } from "../../shared/classes/class-utils";
const momentTz = require('moment-timezone');

@Injectable()
export class PrimaryContractQueryRepository {
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IPrimaryContractDocument>,
    private readonly employeeClient: EmployeeClient,
    private readonly stsClient: StsClient,
    private readonly customerClient: CustomerClient
  ) { }


  async findAll(): Promise<IPrimaryContractDocument[]> {
    return await this.readModel
      .find()
      .exec()
      .then((result) => {
        return result;
      });
  }

  /**
   * find One
   */
  async findOne(query, projection?: any): Promise<any> {
    return await this.readModel
      .findOne(query, projection)
      .lean()
      .exec()
  }


  async findAggregateModelById(id: string): Promise<QueryAggregateModel> {
    return await this.readModel
      .findOne({ id })
      .exec()
      .then((response) => {
        return new QueryAggregateModel(id);
      })
      .catch((exp) => {
        return exp;
      });
  }

  async create(readmodel): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .create(readmodel)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async update(model): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .update({ id: model.id }, model)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async updateOne(query, model): Promise<IPrimaryContractDocument> {
    return await this.readModel
      .updateOne(query, model)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async delete(model): Promise<any> {
    return await this.readModel
      .deleteOne({ id: model.id })
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async contractFindAll(isPaging: boolean = false, isSeeAll, query?: any, page?, pageSize?): Promise<IPrimaryContractDocument[]> {
    let [_query, _queryKeywords]: any = this.getContractQuery(isSeeAll, query);
    let sort: any = {
      createdDate: -1
    };
    if (query.id) {
      _query = {
        id: query.id
      };
    }

    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort) || {
        createdDate: -1
      };
    }

    const aggregate: any[] = [
      {
        $match: _queryKeywords
      },
      {
        $match: _query
      },
      { $sort: sort }
    ];
    if (!_.isEmpty(query.status)) {
      if (Array.isArray(query.status)) {
        aggregate.push({
          $match: { status: { $in: query.status } }
        });
      } else {
        aggregate.push({
          $match: { status: query.status }
        });
      }
    }
    if (!_.isEmpty(query['projectIds'])) {
      if (query.projectIds.includes(',')) {
        query.projectIds = query.projectIds.split(',');
      }

      if (Array.isArray(query.projectIds)) {
        aggregate.push({
          $match: { 'primaryTransaction.project.id': { $in: query.projectIds } }
        });
      } else {
        aggregate.push({
          $match: { 'primaryTransaction.project.id': query.projectIds }
        });
      }
    } else {
      return [{
        row: [],
        totalCount: 0
      } as any];
    }
    if (!_.isEmpty(query['type'])) {
      query.type = query['type'].split(',');
      aggregate.push({
        $match: { 'type': { $in: query.type } }
      });

      // Với HĐC chỉ hiển thị những HĐ chưa chuyển sang HDMB
      if (query.type == ContractEnum.DEPOSIT && !_.isEmpty(query['searchInLiquidation'])) {
        aggregate.push({
          $match: { 'purchase': null }
        });
      }
    }

    if (!_.isEmpty(query['isTransferred'])) {
      aggregate.push({
        $match: { 'isTransferred': true }
      });
    }

    if (!_.isEmpty(query.startDate) && !_.isEmpty(query.endDate)) {
      const startDate: any = new Date(+query.startDate);
      const endDate: any = new Date(+query.endDate);
      aggregate.push({
        $match: {
          createdDate: {
            $exists: true,
            $gte: new Date(`'${startDate}'`),
            $lte: new Date(`'${endDate}'`)
          }
        }
      });
    }
    if (!_.isEmpty(query._fields)) {
      const fields = query._fields.split(',');
      const project: any = {};
      fields.forEach(f => {
        project[f.trim()] = 1;
      });
      // project._id = 0;
      aggregate.push({ $project: project });
    }
    if (isPaging) {
      aggregate.push({
        $facet: {
          rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
          totalCount: [
            {
              $count: 'count'
            }
          ]
        }
      });
    }
    return await this.readModel.aggregate(aggregate)
      .allowDiskUse(true)
      .exec().then(res => {
        return res;
      });
  }

  private escapeRegex(text) {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  }

  private getContractQuery(isSeeAll, query: any = {}): any {
    const match: any = {};
    const matchKeywords: any = {};
    if (query) {
      if (!_.isEmpty(query['primaryTransaction.project.id'])) {
        match['primaryTransaction.project.id'] = query['primaryTransaction.project.id'];
      }
      if (!_.isEmpty(query['salesProgramId'])) {
        match['primaryTransaction.propertyUnit.salesProgram.id'] = query['salesProgramId'];
      }
      if (!_.isEmpty(query.search)) {
        const search = this.escapeRegex(query.search);
        const regexSearch = new RegExp('.*' + search + '.*', 'i');
        matchKeywords.$or = [
          { 'code': { $regex: regexSearch } },
          { 'name': { $regex: regexSearch } },
          { 'primaryTransaction.bookingTicketCode': { $regex: regexSearch } },
          { 'primaryTransaction.escrowTicketCode': { $regex: regexSearch } },
          { 'primaryTransaction.propertyUnit.code': { $regex: regexSearch } },
          { 'createdDate': { $regex: regexSearch } },
          { 'status': { $regex: regexSearch } },
          { 'primaryTransaction.customer.personalInfo.name': { $regex: regexSearch } },
          { 'policyPayment.code': { $regex: regexSearch } },
          { 'policyPayment.name': { $regex: regexSearch } },
          { 'policyDiscounts.code': { $regex: regexSearch } },
          { 'policyDiscounts.name': { $regex: regexSearch } },
        ];
        // matchKeywords.$or = [
        //   { 'code': { $regex: query.keywords, $options: 'i' } },
        //   { 'name': { $regex: query.keywords, $options: 'i' } },
        //   { 'primaryTransaction.bookingTicketCode': { $regex: query.keywords, $options: 'i' } },
        //   { 'primaryTransaction.escrowTicketCode': { $regex: query.keywords, $options: 'i' } },
        //   { 'primaryTransaction.propertyUnit.code': { $regex: query.keywords, $options: 'i' } },
        //   { 'createdDate': { $regex: query.keywords, $options: 'i' } },
        //   { 'status': { $regex: query.keywords, $options: 'i' } },
        //   { 'primaryTransaction.customer.personalInfo.name': { $regex: query.keywords, $options: 'i' } },
        //   { 'policyPayment.code': { $regex: query.keywords, $options: 'i' } },
        //   { 'policyPayment.name': { $regex: query.keywords, $options: 'i' } },
        //   { 'policyDiscounts.code': { $regex: query.keywords, $options: 'i' } },
        //   { 'policyDiscounts.name': { $regex: query.keywords, $options: 'i' } },
        // ];
      }

      if (!_.isEmpty(query['status'])) {
        if (query.status.includes(',')) {
          query.status = query.status.split(',');
        }

        if (Array.isArray(query.status)) {
          match['status'] = { $in: query.status };
        } else {
          match['status'] = query['status'];
        }
      } else {
        match['status'] = { $ne: StatusEnum.CANCELLED }
      }

      if (!isSeeAll && query.queryEmpIDs && query.queryEmpIDs.length > 0) {
        match['createdBy'] = { "$in": query.queryEmpIDs };
      }

      if (query.forAccountant) {
        // cho màn hình tạo phiếu thu, loại bỏ các HĐC đã chuyển thành HĐMB
        match['purchase'] = { $exists: false }
      }

      if (query.isCheckProject) {
        match['primaryTransaction.project.id'] = query['primaryTransaction.project.id'];
      }

      if (query.discountPolicyIds) {
        match['policyDiscounts.id'] = {
          $in: query.discountPolicyIds.split(',') || []
        };
      }

      if (query.paymentPolicyIds) {
        match['policyPayment.id'] = {
          $in: query.paymentPolicyIds.split(',') || []
        };
      }
    }

    return [match, matchKeywords];
  }
  protected transformSort(paramSort?: String) {
    let sort: any = paramSort;
    if (_.isString(sort)) {
      sort = sort.split(",");
    }
    if (Array.isArray(sort)) {
      let sortObj = {};
      sort.forEach(s => {
        if (s.startsWith("-"))
          sortObj[s.slice(1)] = -1;
        else
          sortObj[s] = 1;
      });
      return sortObj;
    }

    return sort;
  }

  async findContractByDiscountPolicy(discountPolicyId: string): Promise<IPrimaryContractDocument[]> {
    const sort: any = {
      createdDate: -1
    };
    const _query = {
      "policyDiscount.id": discountPolicyId,
      "status": { $in: [StatusContractEnum.APPROVED, StatusContractEnum.WAITING, StatusContractEnum.ACCOUNTANT_WAITING] }
    };

    const aggregate: any[] = [
      {
        $match: _query
      },
      { $sort: sort }
    ];
    return await this.readModel.aggregate(aggregate)
      .allowDiskUse(true)
      .exec().then(res => {
        return res;
      });
  }

  async findPrimaryContractReminder(query: any = {}): Promise<IPrimaryContractDocument[]> {
    const sort: any = {
      createdDate: -1
    };
    const aggregate: any[] = [
      ...query,
      { $sort: sort }
    ];
    return await this.readModel.aggregate(aggregate)
      .allowDiskUse(true)
      .exec().then(res => {
        return res;
      });
  }

  async findPrimaryContractForCustomer(query: any = {}, isPaging = false, page = 1, pageSize = 10): Promise<IPrimaryContractDocument[]> {
    let sort: any = {
      createdDate: -1
    };
    if (!_.isEmpty(query.sort)) {
      sort = CommonUtils.transformSort(query.sort) || {
        createdDate: -1,
      };
      delete query.sort;
    }
    let project: any = { _id: 0 };
    if (!_.isEmpty(query._fields)) {
      const fields = query._fields.split(",");
      fields.forEach((f) => {
        project[f.trim()] = 1;
      });
      delete query._fields;
    }

    const aggregate: any[] = [
      { $match: query },
      { $sort: sort },
      { $project: project }
    ];

    if (isPaging) {
      aggregate.push({
        $facet: {
          rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
          totalCount: [
            {
              $count: 'count'
            }
          ]
        }
      });
    }

    return await this.readModel.aggregate(aggregate)
      .allowDiskUse(true)
      .exec().then(res => {
        return res;
      });
  }
  async findPrimaryContractForCustomerNew(
    query: any = {},
    isPaging = false,
    page = 1,
    pageSize = 10
  ): Promise<IPrimaryContractDocument[]> {

    const { sort: sortStr, _fields, ...matchQuery } = query;

    const sort = !_.isEmpty(sortStr)
      ? CommonUtils.transformSort(sortStr) || { createdDate: -1 }
      : { createdDate: -1 };

    const project = !_.isEmpty(_fields)
      ? _fields.split(",").reduce((acc, f) => ({ ...acc, [f.trim()]: 1 }), { _id: 0 })
      : { _id: 0 };

    const aggregate: any[] = [
      { $match: matchQuery },
      { $sort: sort },
      { $project: project }
    ];

    if (isPaging) {
      aggregate.push({
        $facet: {
          rows: [
            { $skip: (page - 1) * pageSize },
            { $limit: pageSize }
          ],
          totalCount: [{ $count: 'count' }]
        }
      });
    }

    return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
  }
  async findCustomerIdentitiesPrimaryContractByProjectId(query) {

    const aggregate: any[] = [
      { $match: query },
      {
        $unwind: {
          path: '$customer.identities'
        }
      },
      {
        $unwind: {
          path: '$customer2.identities',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: 0,
          identties: {
            $addToSet: '$customer.identities.value'
          },
          identties2: {
            $addToSet: '$customer2.identities.value'
          }
        }
      },
      {
        $project: {
          identties: {
            $concatArrays: [
              '$identties', '$identties2'
            ]
          }
        }
      },
    ];

    return await this.readModel.aggregate(aggregate)
      .allowDiskUse(true)
      .exec().then(res => {
        return res;
      });
  }

  async updateMany(query, model): Promise<IPrimaryContractDocument> {
    return await this.readModel.updateMany(query, model)
      .then((response) => {
        return response;
      }).catch((error) => {
        return error;
      });
  }

  updatePropertyUnits(units: any[]) {
    return this.readModel.bulkWrite(
      units.map(unit => ({
        updateMany: {
          filter: { "primaryTransaction.propertyUnit.id": unit.id },
          update: {
            $set: {
              "primaryTransaction.propertyUnit": unit
            }
          },
        }
      }))
    );
  }


  async find(query, project = {}): Promise<IPrimaryContractDocument[]> {
    return await this.readModel
      .find(query, project)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async aggregate(pipeline) {
    return await this.readModel.aggregate(pipeline).exec()
      .then((res) => {
        return res;
      });
  }

  async findPrimaryContractsAggregate(aggregate: any[]) {
    return this.readModel.aggregate(aggregate).exec();
  }

  async findDebtReportContract(query: any) {
    let aggregate: any[] = [];
    let sort: any = {
      'createdDate': -1
    };
    let match: any = {
      status: { $in: [StatusContractEnum.ACCOUNTANT_WAITING, StatusContractEnum.APPROVED] },
      $or: [
        { type: ContractEnum.DEPOSIT, purchase: null },
        { type: { $ne: ContractEnum.DEPOSIT } }
      ]
    };
    if (query.projectId) {
      // filter theo dự án
      match['primaryTransaction.project.id'] = query.projectId;
    } else {
      if (query.projectIds) {
        match['primaryTransaction.project.id'] = { "$in": query.projectIds }
      }
    }

    // check hợp đồng
    aggregate.push({
      $match: match
    });
    // add field sort type
    aggregate.push({
      $addFields: {
        sortType: {
          $switch: {
            "branches": [
              { "case": { "$eq": ["$type", ContractEnum.PURCHASE] }, "then": 3 },
              { "case": { "$eq": ["$type", ContractEnum.RENT] }, "then": 2 },
              { "case": { "$eq": ["$type", ContractEnum.DEPOSIT] }, "then": 1 },
            ],
            "default": 0
          }
        }
      }
    });
    // sort theo loại hóa đơn
    aggregate.push({
      $sort: {
        sortType: -1
      }
    })
    // Gộp hóa đơn cọc và hóa đơn mua bán với nhau theo primary transaction
    aggregate.push({
      $group: {
        _id: "$primaryTransaction.id",
        'name': { $first: '$name' },
        'policyPayment': { $first: '$policyPayment' },
        'primaryTransaction': { $first: '$primaryTransaction' },
        'expiredDate': { $first: '$expiredDate' },
        'type': { $first: '$type' },
        'isTransferred': { $first: '$isTransferred' },
        'id': { $first: '$id' }
      }
    });
    // lấy list các đợt trong mỗi hợp đồng ra
    aggregate.push({
      $unwind: {
        path: '$policyPayment.schedule.installments',
        preserveNullAndEmptyArrays: true
      }
    });
    aggregate.push({
      $match: {
        $expr: {
          $lt: ['$policyPayment.schedule.installments.totalTransfered', '$policyPayment.schedule.installments.totalAmount']
        }
      }
    })
    // sort hợp đồng
    aggregate.push({ $sort: sort });
    // lấy list các đợt trong mỗi hợp đồng ra
    aggregate.push({
      $unwind: {
        path: '$policyPayment.schedule.installments',
        preserveNullAndEmptyArrays: true
      }
    });
    // check xem các đợt còn công nợ không
    aggregate.push({
      $match: {
        $expr: {
          $lt: ['$policyPayment.schedule.installments.totalTransfered',
            '$policyPayment.schedule.installments.totalAmount']
        }
      }
    });
    // project data
    aggregate.push({
      $project: {
        _id: '$id',
        'namePrimaryContract': '$name',
        'installmentsName': '$policyPayment.schedule.installments.name',
        'project': '$primaryTransaction.project.name',
        'productCode': '$primaryTransaction.propertyUnit.code',
        'debt': {
          $subtract: ['$policyPayment.schedule.installments.totalAmount', '$policyPayment.schedule.installments.totalTransfered']
        },
        'paymentDueDate': '$policyPayment.schedule.installments.paymentDueDate',
        'expiredDate': '$expiredDate',
        'type': '$type',
        'isTransferred': '$isTransferred',
        'id': '$id'
      }
    });

    aggregate.push({ $sort: { 'paymentDueDate': 1 } });
    // filter theo tên hợp đồng, tên đợt, mã sản phẩm
    let matchFilter: any = {};
    if (query.q) {
      matchFilter.$or = [
        { 'namePrimaryContract': { $regex: query.q, $options: "i" } },
        { 'installmentsName': { $regex: query.q, $options: "i" } },
        { 'productCode': { $regex: query.q, $options: "i" } },
      ];
    };
    // filter theo thời hạn thanh toán hợp đồng
    if (query.createdFrom && query.createdTo) {
      const startOfDay = momentTz(parseInt(query.createdFrom)).tz('Asia/Ho_Chi_Minh').startOf('day').toDate();
      const endOfDay = momentTz(parseInt(query.createdTo)).tz('Asia/Ho_Chi_Minh').endOf('day').toDate();
      matchFilter['paymentDueDate'] = { $gte: startOfDay, $lte: endOfDay };
    } else {
      if (query.createdFrom) {
        const startOfDay = momentTz(parseInt(query.createdFrom)).tz('Asia/Ho_Chi_Minh').startOf('day').toDate();
        matchFilter['paymentDueDate'] = { $gte: startOfDay };
      }
      if (query.createdTo) {
        const endOfDay = momentTz(parseInt(query.createdTo)).tz('Asia/Ho_Chi_Minh').endOf('day').toDate();
        matchFilter['paymentDueDate'] = { $lte: endOfDay };
      }
    }
    if (matchFilter) {
      aggregate.push({ $match: matchFilter });
    }

    if (query.page || query.pageSize) {
      let pageSize: number = parseInt(query["pageSize"]) || 10;
      let page: number = parseInt(query["page"]) || 1;
      aggregate.push({
        $facet: {
          rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
          total: [
            {
              $count: 'count'
            }
          ],
        }
      });

      return this.readModel.aggregate(aggregate)
        .allowDiskUse(true)
        .exec().then(result => {
          const total = result[0].total[0] ? result[0].total[0].count : 0;
          return {
            rows: result[0].rows,
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize)
          }
        });
    } else {
      return this.readModel.aggregate(aggregate)
        .allowDiskUse(true)
        .exec().then(res => {
          return res;
        });
    }
  }

  async getCommissionTxs(query): Promise<IPrimaryContractDocument[]> {
    const { periodFrom, periodTo, orgCode, transactionSuccess } = query;

    const aggs = [];

    // default match
    aggs.push({
      $match: { status: StatusContractEnum.APPROVED, transactionSuccess, orgCode }
    });

    aggs.push({
      $unwind: {
        path: "$policyPayment.schedule.installments",
        preserveNullAndEmptyArrays: true
      }
    });

    aggs.push({
      $unwind: {
        path: "$policyPayment.schedule.installments.receipts",
        preserveNullAndEmptyArrays: true
      }
    });

    aggs.push({
      $addFields: {
        "policyPayment.schedule.installments.receipts.receiptDate": {
          $toDate: "$policyPayment.schedule.installments.receipts.receiptDate",
        },
      },
    });

    aggs.push({
      $match: {
        "policyPayment.schedule.installments.receipts.status": "TRANSFERED",
        "policyPayment.schedule.installments.receipts.receiptDate": {
          $gte: new Date(periodFrom),
          $lte: new Date(periodTo),
        },
      },
    });

    aggs.push({
      $project: {
        _id: 0,
        id: 1,
        code: 1,
        name: 1,
        signedDate: 1,
        type: 1,
        transactionState: "$status",
        orgCode: 1,
        project: '$primaryTransaction.project',
        propertyUnit: '$primaryTransaction.propertyUnit',
        customer: '$primaryTransaction.customer',
        employee: '$primaryTransaction.employee',
        employeeRole: 1,
        employeeRevenueRate: 1,
        installment: "$policyPayment.schedule.installments",
        receipt: "$policyPayment.schedule.installments.receipts",
        salesPolicy: 1,
        staffsInvolved: 1
      }
    });

    console.log('aggs', JSON.stringify(aggs, null, 2));

    return await this.readModel.aggregate(aggs)
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async getCommissionTxsByStaffsInvolved(query): Promise<IPrimaryContractDocument[]> {
    const { periodFrom, periodTo, orgCode, transactionSuccess } = query;

    const aggs = [];

    // default match
    aggs.push({
      $match: { status: StatusContractEnum.APPROVED, transactionSuccess, orgCode }
    });

    aggs.push({
      $unwind: {
        path: "$policyPayment.schedule.installments",
        preserveNullAndEmptyArrays: true
      }
    });

    aggs.push({
      $unwind: {
        path: "$policyPayment.schedule.installments.receipts",
        preserveNullAndEmptyArrays: true
      }
    });

    aggs.push({
      $addFields: {
        "policyPayment.schedule.installments.receipts.receiptDate": {
          $toDate: "$policyPayment.schedule.installments.receipts.receiptDate",
        },
      },
    });

    aggs.push({
      $match: {
        "policyPayment.schedule.installments.receipts.status": "TRANSFERED",
        "policyPayment.schedule.installments.receipts.receiptDate": {
          $gte: new Date(periodFrom),
          $lte: new Date(periodTo),
        },
      },
    });

    aggs.push({
      $group: {
        _id: "$code",
        id: { $first: "$id" },
        code: { $first: "$code" },
        name: { $first: "$name" },
        signedDate: { $first: "$signedDate" },
        type: { $first: "$type" },
        transactionState: { $first: "$status" },
        orgCode: { $first: "$orgCode" },
        project: { $first: "$primaryTransaction.project" },
        propertyUnit: { $first: "$primaryTransaction.propertyUnit" },
        customer: { $first: "$primaryTransaction.customer" },
        employee: { $first: "$staffsInvolved" },
        salesPolicy: { $first: "$salesPolicy" },
        staffsInvolved: { $first: "$staffsInvolved" }
      }
    });

    aggs.push({
      $match: {
        $expr: { $gt: [{ $size: "$staffsInvolved" }, 0] }
      }
    });

    aggs.push({
      $unwind: {
        path: "$staffsInvolved",
        preserveNullAndEmptyArrays: true
      }
    });

    aggs.push({
      $project: {
        _id: 0,
        id: 1,
        code: 1,
        name: 1,
        signedDate: 1,
        type: 1,
        transactionState: 1,
        orgCode: 1,
        project: 1,
        propertyUnit: 1,
        customer: 1,
        employee: "$staffsInvolved",
        // employeeRole: 1,
        employeeRevenueRate: '$staffsInvolved.percent',
        // installment: "$policyPayment.schedule.installments",
        // receipt: "$policyPayment.schedule.installments.receipts",
        salesPolicy: 1,
        staffsInvolved: 1
      }
    });

    console.log('aggs', JSON.stringify(aggs, null, 2));

    return await this.readModel.aggregate(aggs)
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        return result;
      });
  }
}
