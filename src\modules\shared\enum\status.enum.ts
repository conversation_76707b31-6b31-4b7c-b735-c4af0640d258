export enum TransactionStatusEnum {
  initial = 'INITIAL',
  received = 'RECEIVED', // 1. check code + spec hiện tại: là status mặc định khi tạo transaction
  transfered = 'TRANSFERED', // đã hoạch toán
  processing = 'PROCESSING', // từ chối
  waiting_transfer = 'WAITING_TRANSFER', // 2. sau khi được gán vào transaction order
  refunded = 'REFUNDED',
  canceled = 'CANCELED'
}

export enum InterestCalculationStatusEnum {
  init = 'INIT',
  approved = 'APPROVED',
  transfered = 'TRANSFERED'
}

export enum DebtTypeEnum {
  projectdebt = 'PROJECTDEBT',
  baddebt = 'BADDEBT',
}

export enum HandoverQualityEnum {
  'good' = 'good', // hài lòng
  'normal' = 'normal', // tạm được
  'bad' = 'bad', // không hài lòng
}

export enum HandoverStatusEnum {
  'init' = 'init', // chưa bàn giao
  'later' = 'later', // bàn giao sau
  'handed' = 'handed', // đã bàn giao
  'scheduled' = 'scheduled', // đã lên lịch
  'Eligible' = 'Eligible', // Sản phẩm đủ điều kiện làm sổ 
  'Cer_in_process' = 'Cer_in_process', // Sản phẩm đang làm sổ
  'Cer_ready_handover' = 'Cer_ready_handover', // Sản phẩm đã có sổ đợi bàn giao
  'Cer_handed_over' = 'Cer_handed_over', // Sản phẩm đã bàn giao sổ
}
export enum HandoverScheduleStatusEnum {
  unconfirmed = 'unconfirmed', // chưa xác nhận
  requested = 'requested', // yêu cầu thay đổi 
  confirmed = 'confirmed', // đã xác nhận
  rejected = 'rejected' // từ chối
}

export enum HandoverDeliveryItemType {
  category = 'category', // hạng mục
  index = 'index', // chỉ số
}

export enum ErpStatusEnum {

  TLYDCCC = 'TLYDCCC',
  // trạng thái update thanh lý hợp đồng
  TLHDC = 'TLHDC',
  TLYDCOCC = 'TLYDCOCC',
  TLYDCODC = 'TLYDCODC',
  TLYDCOHC = 'TLYDCOHC',
  TLYDCOTC = 'TLYDCOTC',
  TLYHDC = 'TLYHDC',
  TLYHDCCC = 'TLYHDC',
  TLYHDCCT = 'TLYHDCCT',
  TLYHDNT = 'TLYHDNT',
  TLYHDO = 'TLYHDO',
  TLYHDOCC = 'TLYHDOCC',
  TLYHUY = 'TLYHUY',
}
export class ProposalStatusEnum {
  static NEW ={
    code: 'NEW',
    name: 'Đang soạn thảo'
  };
  static INIT ={
    code: 'INIT',
    name: 'Chờ đệ trình'
  };
  static APPROVED ={
    code: 'APPROVED',
    name: 'Đã duyệt'
  };
  static WAITING ={
    code: 'WAITING',
    name: 'Đang phê duyệt'
  };
  static RETURNED ={
    code: 'RETURNED',
    name: 'Thu hồi'
  };
  static REJECTED ={
    code: 'REJECTED',
    name: 'Trả về'
  };
  static CANCELED ={
    code: 'CANCELED',
    name: 'Huỷ'
  };
}