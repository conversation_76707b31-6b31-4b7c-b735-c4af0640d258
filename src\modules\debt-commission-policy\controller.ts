import {
    Controller,
    Put,
    Post,
    Delete,
    Body,
    Headers,
    UseGuards,
    UseInterceptors,
    Param,
    Get,
    Query,
} from '@nestjs/common';
import { LoggingInterceptor } from '../../common/interceptors/logging.interceptor';
import { DebtCommissionPolicyService } from './service';
import { Action } from '../shared/enum/action.enum';
import { PermissionEnum } from '../shared/enum/permission.enum';
import { ApiUseTags, ApiBearerAuth, ApiForbiddenResponse, ApiCreatedResponse } from '@nestjs/swagger';
import { ValidationPipe } from '../../common/pipes/validation.pipe';
import { JwtAuthGuard, RoleGuard, Roles, User } from '../../../shared-modules';
import _ = require('lodash');
import { CreateDebtCommissionPolicyDto, DeleteDebtCommissionPolicyDto, UpdateDebtCommissionPolicyDto, UpdateIsActiveDebtCommissionPolicyDto } from './dto/debt-commission-policy.dto';

@ApiBearerAuth()
@ApiUseTags('v1/debt-commission-policy')
@Controller('v1/debt-commission-policy')
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard)
export class DebtCommissionPolicyController {
    private resSuccess = { success: true };
    constructor(
        private readonly service: DebtCommissionPolicyService
    ) { }

    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_CREATE)
    @Post()
    async createCommissionPolicy(@User() userLogged, @Body(new ValidationPipe()) dto: CreateDebtCommissionPolicyDto) {
        return await this.service.create(userLogged, dto) || this.resSuccess;
    }

    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_UPDATE)
    @Put("")
    async updateCommissionPolicy(
        @Body() dto: UpdateDebtCommissionPolicyDto,
        @User() userLogged) {
        return await this.service.update(userLogged, dto);
    }
    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_CHANGE_STATUS)
    @Put("/change-status")
    async updateIsActiveCommissionPolicy(
        @Body() dto: UpdateIsActiveDebtCommissionPolicyDto,
        @User() userLogged) {
        return await this.service.updateIsActive(userLogged, dto);
    }
    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_DELETE)
    @Delete()
    async deleteCommissionPolicy(
        @Body() dto: DeleteDebtCommissionPolicyDto,
        @User() userLogged) {
        return await this.service.delete(userLogged, dto);
    }

    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_CLONE)
    @Post('copy/:id')
    async cloneCommissionPolicy(@User() userLogged, @Param('id') id: string) {
        return await this.service.clone(userLogged, id) || this.resSuccess;
    }
    @Get("/get-dropdown-projects")
    async getDropdownProjects(
        @User() userLogged,
        @Query() query: any): Promise<any> {
        return await this.service.getDropdownProjects(userLogged, query);
    }
    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_GET_ALL)
    @Get()
    findAll(@User() userLogged, @Query() query: any): Promise<any> {
        return this.service.findAll(userLogged, query);
    }
    @UseGuards(RoleGuard)
    @Roles(PermissionEnum.DEBT_COMMISION_POLICY_GET_ID)
    @Get(':id')
    findOne(@Param('id') id: string): Promise<any> {
        return this.service.findById(id);
    }
}
