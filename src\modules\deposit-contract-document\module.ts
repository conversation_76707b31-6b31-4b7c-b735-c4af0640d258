import { Module } from '@nestjs/common';
import { SharedModule } from '../../../shared-modules';
import { DepositContractDocumentController } from './controller';
import { DepositContractDocumentService } from './service';
import { DepositContractDocumentRepository } from './repositories/deposit-contract-document.repository';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { DepositContractDocumentProviders } from './providers/deposit-contract-document.providers';

@Module({
  imports: [SharedModule, QueryDatabaseModule],
  controllers: [DepositContractDocumentController],
  providers: [
    DepositContractDocumentService,
    DepositContractDocumentRepository,
    ...DepositContractDocumentProviders
  ],
  exports: [DepositContractDocumentService, DepositContractDocumentRepository]
})

export class DepositContractDocumentModule {
}