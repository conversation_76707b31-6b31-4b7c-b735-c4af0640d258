import { Connection } from 'mongoose';
import { CommonConst } from '../../shared/constant/common.const';
import { DepositContractSchema } from '../schemas/deposit-contract.schema';

export const DepositContractProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) => connection.model(CommonConst.AGGREGATES.DEPOSIT_CONTRACT.NAME, DepositContractSchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  }
];
