import { Connection } from 'mongoose';
import { CommonConst } from '../../shared/constant/common.const';
import { DepositContractDocumentFolderSchema } from '../schemas/deposit-contract-document.schema';

export const DepositContractDocumentProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) => connection.model(CommonConst.DEPOSIT_CONTRACT_DOCUMENT_COLLECTION, DepositContractDocumentFolderSchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  }
];