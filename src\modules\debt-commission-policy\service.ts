import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { CommandBus, InvalidCommandHandlerException } from '@nestjs/cqrs';

import { ErrorConst } from '../shared/constant/error.const';
import { MsxLoggerService } from '../logger/logger.service';

import uuid = require('uuid');
import clc = require('cli-color');
import { CodeGenerateService } from '../code-generate/service';
import { CommonConst } from '../shared/constant';
import { StsClient } from '../mgs-sender/sts.client';
import { BaseService, CmdPatternConst, ErrorService } from '../../../shared-modules';

import { DebtCommissionPolicyQueryRepository } from './repository/repository';
import { CreateDebtCommissionPolicyDto, DeleteDebtCommissionPolicyDto, projectDebtDto, UpdateDebtCommissionPolicyDto, UpdateIsActiveDebtCommissionPolicyDto } from './dto/debt-commission-policy.dto';
import { GenPrefix } from '../shared/utils/transform';
import { PropertyClient } from '../mgs-sender/property.client';

@Injectable()
export class DebtCommissionPolicyService extends BaseService {
    private readonly context = DebtCommissionPolicyService.name;
    private commandId: string;

    constructor(
        private readonly commandBus: CommandBus,
        private readonly codeGenerateService: CodeGenerateService,
        private readonly repository: DebtCommissionPolicyQueryRepository,
        private readonly loggerService: MsxLoggerService,
        private readonly stsClient: StsClient,
        public readonly errorService: ErrorService,
        public readonly propertyClient: PropertyClient,
    ) {
        super(errorService);
    }

    async create(user: any, dto: CreateDebtCommissionPolicyDto) {
        const MAX_AMOUNT_VND = 10000000000000;
        const MAX_PERCENT = 999.99;

        if (dto.project && (!dto.penalty || dto.penalty.length === 0)) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0001");
        } else {
            for (const p of dto.penalty) {
                if (p.unit === "VNĐ" && p.amount >= MAX_AMOUNT_VND) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0002");
                }
                if (p.unit === "%" && p.amount >= MAX_PERCENT) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0002");
                }
            }
        }

        if (dto.projectDebt && dto.projectDebt?.listRate.length > 0) {
            let prevRate: { bottomPrice: number; topPrice: number } | null = null;

            for (let i = 0; i < dto.projectDebt?.listRate.length; i++) {
                const rate = dto.projectDebt?.listRate[i];

                // if (!rate.bottomPrice) return "DEBTCOMMISSIONPOLICY0003";
                if (rate.bottomPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0004");

                if (!rate.topPrice) return this.getResponse("DEBTCOMMISSIONPOLICY0005");
                if (rate.topPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0006");

                if (
                    (prevRate && rate.bottomPrice < prevRate.topPrice) ||
                    rate.topPrice <= rate.bottomPrice
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0007");
                }

                if (
                    (rate.recordedCommissionUnit === "VNĐ" && rate.recordedCommission >= MAX_AMOUNT_VND) ||
                    (rate.recordedCommissionUnit === "%" && rate.recordedCommission >= MAX_PERCENT)
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0008");
                }

                prevRate = rate;
            }
        }

        if (dto.badDebt && dto.badDebt?.listRate.length > 0) {
            let prevRate: { bottomPrice: number; topPrice: number } | null = null;

            for (let i = 0; i < dto.badDebt?.listRate.length; i++) {
                const rate = dto.badDebt?.listRate[i];

                // if (!rate.bottomPrice) return "DEBTCOMMISSIONPOLICY0003";
                if (rate.bottomPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0004");

                if (!rate.topPrice) return this.getResponse("DEBTCOMMISSIONPOLICY0005");
                if (rate.topPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0006");

                if (
                    (prevRate && rate.bottomPrice < prevRate.topPrice) ||
                    rate.topPrice <= rate.bottomPrice
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0007");
                }

                if (
                    (rate.recordedCommissionUnit === "VNĐ" && rate.recordedCommission >= MAX_AMOUNT_VND) ||
                    (rate.recordedCommissionUnit === "%" && rate.recordedCommission >= MAX_PERCENT)
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0008");
                }

                prevRate = rate;
            }
        }

        function formatDate(date: Date): string {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0'); // tháng bắt đầu từ 0
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }
        const startWithYear = formatDate(dto.periodFrom);
        const endWithYear = formatDate(dto.periodTo);
        dto.period = `${startWithYear} → ${endWithYear}`;


        let userInfo = {
            id: user.id,
            email: user.email,
            userName: user.userName,
            fullName: user.fullName,
        }
        dto.createdBy = userInfo;
        dto.modifiedBy = userInfo;

        // Generate code
        dto.code = await this.codeGenerateService.generateCodeDebtCommissionPolicy(user);
        dto.id = uuid.v4()
        await this.repository.create(dto);
        return this.getResponse(0, { id: dto.id });
    }
    async update(user: any, dto: UpdateDebtCommissionPolicyDto) {
        const debtCommissionPolicy = await this.repository.findOne({ id: dto.id });
        if (!debtCommissionPolicy) {
            return this.getResponse("COMMISSIONPOLICY0004");
        }
        const MAX_AMOUNT_VND = 10000000000000;
        const MAX_PERCENT = 999.99;

        if (dto.project && (!dto.penalty || dto.penalty.length === 0)) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0001");
        } else {
            for (const p of dto.penalty) {
                if (p.unit === "VNĐ" && p.amount >= MAX_AMOUNT_VND) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0002");
                }
                if (p.unit === "%" && p.amount >= MAX_PERCENT) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0002");
                }
            }
        }


        if (dto.projectDebt && dto.projectDebt?.listRate.length > 0) {
            let prevRate: { bottomPrice: number; topPrice: number } | null = null;

            for (let i = 0; i < dto.projectDebt?.listRate.length; i++) {
                const rate = dto.projectDebt?.listRate[i];

                // if (!rate.bottomPrice) return "DEBTCOMMISSIONPOLICY0003";
                if (rate.bottomPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0004");

                if (!rate.topPrice) return this.getResponse("DEBTCOMMISSIONPOLICY0005");
                if (rate.topPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0006");

                if (
                    (prevRate && rate.bottomPrice < prevRate.topPrice) ||
                    rate.topPrice <= rate.bottomPrice
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0007");
                }

                if (
                    (rate.recordedCommissionUnit === "VNĐ" && rate.recordedCommission >= MAX_AMOUNT_VND) ||
                    (rate.recordedCommissionUnit === "%" && rate.recordedCommission >= MAX_PERCENT)
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0008");
                }

                prevRate = rate;
            }
        }

        if (dto.badDebt && dto.badDebt?.listRate.length > 0) {
            let prevRate: { bottomPrice: number; topPrice: number } | null = null;

            for (let i = 0; i < dto.badDebt?.listRate.length; i++) {
                const rate = dto.badDebt?.listRate[i];

                // if (!rate.bottomPrice) return "DEBTCOMMISSIONPOLICY0003";
                if (rate.bottomPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0004");

                if (!rate.topPrice) return this.getResponse("DEBTCOMMISSIONPOLICY0005");
                if (rate.topPrice >= MAX_AMOUNT_VND) return this.getResponse("DEBTCOMMISSIONPOLICY0006");

                if (
                    (prevRate && rate.bottomPrice < prevRate.topPrice) ||
                    rate.topPrice <= rate.bottomPrice
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0007");
                }

                if (
                    (rate.recordedCommissionUnit === "VNĐ" && rate.recordedCommission >= MAX_AMOUNT_VND) ||
                    (rate.recordedCommissionUnit === "%" && rate.recordedCommission >= MAX_PERCENT)
                ) {
                    return this.getResponse("DEBTCOMMISSIONPOLICY0008");
                }

                prevRate = rate;
            }
        }

        function formatDate(date: Date): string {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0'); // tháng bắt đầu từ 0
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }
        const startWithYear = formatDate(dto.periodFrom);
        const endWithYear = formatDate(dto.periodTo);
        dto.period = `${startWithYear} → ${endWithYear}`;

        let userInfo = {
            id: user.id,
            email: user.email,
            userName: user.userName,
            fullName: user.fullName,
        }
        dto.createdBy = userInfo;
        dto.modifiedBy = userInfo;

        await this.repository.update(dto);
        return this.getResponse(0);
    }

    async delete(user: any, dto: DeleteDebtCommissionPolicyDto) {
        const debtCommissionPolicy = await this.repository.findOne({ id: dto.id });
        if (!debtCommissionPolicy) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0009");
        }
        debtCommissionPolicy.reasonDelete = dto.reasonDelete;
        debtCommissionPolicy.softDelete = true;
        let userInfo = {
            id: user.id,
            email: user.email,
            userName: user.userName,
            fullName: user.fullName,
        }
        debtCommissionPolicy.modifiedBy = userInfo;
        debtCommissionPolicy.modifiedDate = new Date;
        await this.repository.update(debtCommissionPolicy);
        return this.getResponse(0);

    }
    async updateIsActive(user: any, dto: UpdateIsActiveDebtCommissionPolicyDto) {
        const debtCommissionPolicy = await this.repository.findOne({ id: dto.id });
        if (!debtCommissionPolicy) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0009");
        }
        debtCommissionPolicy.isActive = dto.isActive;
        let userInfo = {
            id: user.id,
            email: user.email,
            userName: user.userName,
            fullName: user.fullName,
        }
        debtCommissionPolicy.modifiedBy = userInfo;
        debtCommissionPolicy.modifiedDate = new Date;
        this.commandId = uuid.v4();
        await this.repository.update(debtCommissionPolicy);
        return this.getResponse(0);
    }
    async clone(user: any, id: string) {
        const entity = await this.repository.findOne({ id });
        if (!entity) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0009");
        }
        const newCode = await this.codeGenerateService.generateCodeDebtCommissionPolicy(user);
        let dto = {
            id: uuid.v4(),
            name: entity.name,
            code: newCode,
            project: entity.project,
            projectDebt: entity.projectDebt,
            badDebt: entity.badDebt,
            penalty: entity.penalty,
            period: entity.period,
            periodName: entity.periodName,
            isActive: entity.isActive,
            periodFrom: entity.periodFrom,
            periodTo: entity.periodTo,
            isInterestSupport: entity.isInterestSupport,
            year: entity.year,
            modifiedBy: entity.modifiedBy,
            createdBy: entity.createdBy,
        };
        return await this.repository.create(dto);
    }
    async findAll(userLogged: any, query: any) {
        const { search, startCreatedDate, endCreatedDate, isActive, project, createdBy, period, hasEappNumber } = query;

        const dataQuery: any = { softDelete: false };
        const page: number = Number(query.page) || 1;
        const pageSize: number = Number(query.pageSize) || 10;

        if (isActive) {
            dataQuery.isActive = Number(isActive);
        }

        if (hasEappNumber === false) {
            dataQuery.eappNumber = { $in: [null, ""] };
        }

        if (createdBy) {
            dataQuery['createdBy.id'] = createdBy;
        }

        if (period) {
            dataQuery['period'] = period;
        }
        
        if (project) {
            const projects = project.split(",").map(p => p);
            dataQuery['project.id'] = { $in: projects };;
        }
        if (startCreatedDate && endCreatedDate) {
            dataQuery.createdDate = {
                $gte: new Date(new Date(startCreatedDate).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endCreatedDate).setHours(23, 59, 59, 999))
            };
        } else if (startCreatedDate) {
            dataQuery.createdDate = {
                $gte: new Date(new Date(startCreatedDate).setHours(0, 0, 0, 0)),
                $lte: new Date()
            };
        } else if (endCreatedDate) {
            dataQuery.createdDate = {
                $lte: new Date(new Date(endCreatedDate).setHours(23, 59, 59, 999))
            };
        }
        if (search) {
            dataQuery.$or = [
                { name: { $regex: new RegExp(search), $options: 'i' } },
                { code: { $regex: new RegExp(search), $options: 'i' } },
            ];
        }
        return await this.repository.findAll(page, pageSize, dataQuery);
    }

    async findById(id: string) {
        const debtCommissionPolicy = await this.repository.findById(id);
        if (!debtCommissionPolicy) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0009");
        }
        return debtCommissionPolicy;
    }

    async findByCode(code: string) {
        const debtCommissionPolicy = await this.repository.findByCode(code);
        if (!debtCommissionPolicy) {
            return this.getResponse("DEBTCOMMISSIONPOLICY0009");
        }
        return debtCommissionPolicy;
    }

    async findOne(query: any) {
        const debtCommissionPolicy = await this.repository.findOne(query);
        return debtCommissionPolicy;
    }

    async getDropdownProjects(userLogged, queryParams: any) {
        const { page = 1, pageSize = 10, search = '' } = queryParams;
        const limit = parseInt(pageSize, 10) || 10;
        const skip = (parseInt(page, 10) - 1) * limit;
        const projects = await this.propertyClient.sendDataPromise(
            {
                query: {},
                fields: { _id: 0, id: 1, name: 1, code: 1, "setting.debtage": 1 }
            },
            CmdPatternConst.PROJECT.GET_PROJECT_DROPDOWN_LIST
        );

        if (!projects || !Array.isArray(projects)) {
            return {
                rows: [],
                page: parseInt(page, 10),
                pageSize: limit,
                total: 0,
                totalPages: Math.ceil(0 / limit),
            };
        }



        // Lọc dữ liệu trong mảng
        let filteredData = projects.filter(item => {
            const name = item.name ? item.name.toString().toLowerCase() : "";
            const code = item.code ? item.code.toString().toLowerCase() : "";
            const keyword = search.toLowerCase()
            return name.includes(keyword) || code.includes(keyword);
        });

        // Tính tổng số phần tử sau khi lọc
        const total = filteredData.length;

        // Phân trang
        const rows = filteredData.slice(skip, skip + limit);

        return {
            rows,
            page: parseInt(page, 10),
            pageSize: limit,
            total,
            totalPages: Math.ceil(total / limit),
        };
    }

    async updateForEapp(dto: any) {
        const debtCommissionPolicy = await this.repository.findOne({ id: dto.debtCommissionPolicy?.id });
        if (!debtCommissionPolicy) {
            return this.getResponse("COMMISSIONPOLICY0004");
        }

        if (dto.status === "CANCELED"){
            debtCommissionPolicy.eappNumber = null;
            debtCommissionPolicy.status = null;
            debtCommissionPolicy.eappUrl = null;
        }
        else {
            debtCommissionPolicy.eappNumber = dto.eappNumber;
            debtCommissionPolicy.status = dto.status;
            debtCommissionPolicy.eappUrl = dto.urlEapp;
        }
        await this.repository.update(debtCommissionPolicy);
        return this.getResponse(0);
    }
}