import { Injectable, BadRequestException, HttpException, HttpStatus } from "@nestjs/common";
import { CommandBus } from "@nestjs/cqrs";
import { CreateLiquidationCommand } from "./commands/impl/create-liquidation.cmd";
import { UpdateLiquidationCommand } from "./commands/impl/update-liquidation.cmd";
import { DeleteLiquidationCommand } from "./commands/impl/delete-liquidation.cmd";
import { LiquidationQueryRepository } from "../liquidation.queryside/repository/liquidation.query.repository";
import { isNullOrUndefined } from "util";
import { CreateLiquidationDto, LiquidationStatusDto, UpdateLiquidationDto } from "./dto/liquidation.dto";
import { MsxLoggerService } from "../logger/logger.service";
import { CmdPatternConst, CommonConst } from "../shared/constant";
import { CodeGenerateService } from "../code-generate/service";
import { Action } from "../shared/enum/action.enum";
import { ErrorConst } from "../shared/constant/error.const";
import { PrimaryContractQueryRepository } from "../primary-contract.queryside/repository/primary-contract-query.repository";
import moment = require("moment");
import { LiquidationStatusEnum, LiquidationTypeEnum } from "../shared/enum/liquidation.enum";
import { CustomerClient } from "../mgs-sender/customer.client";
import { ContractEnum, ContractTypeEnum, StatusContractEnum } from "../shared/enum/primary-contract.enum";
import { InterestCalculationStatusEnum } from "../shared/enum/status.enum";
import { PrimaryContractDomainService } from "../primary-contract.domain/service";
import { PropertyClient } from "../mgs-sender/property.client";
import { ProposalQueryRepository } from "../proposal.queryside/repository/proposal.query.repository";
import { TransactionClient } from '../mgs-sender/transaction.client';
import axios from "axios";
import { PrimaryContractService } from "../primary-contract/service";
import { PrimaryContractClient } from "../mgs-sender/primarycontract.client";

const uuid = require("uuid");
const clc = require("cli-color");

@Injectable()
export class LiquidationDomainService {
  private readonly context = LiquidationDomainService.name;
  private commandId: string;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly primaryContractQueryRepository: PrimaryContractQueryRepository,
    private readonly proposalQueryRepository: ProposalQueryRepository,
    private readonly primaryContractDomainService: PrimaryContractDomainService,
    private readonly customerClient: CustomerClient,
    private readonly propertyClient: PropertyClient,
    private readonly queryRepository: LiquidationQueryRepository,
    private readonly primaryContractService: PrimaryContractService,
    private readonly loggerService: MsxLoggerService,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly primaryContractClient: PrimaryContractClient,
    private readonly transactionClient: TransactionClient
  ) { }

  async createLiquidation(user: any, dto: CreateLiquidationDto, actionName: string) {
    this.loggerService.log(this.context, clc.green("create service"));
    const model: any = { ...dto };
    if (model.id) {
      delete model.id;
    }
    // if(dto.liquidationDate && !moment(dto.liquidationDate).toDate()){
    //   throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, "liquidationDate", dto.liquidationDate.toString())});
    // }
    let contract: any = {};
    let proposal: any = {};
    contract = await this.primaryContractQueryRepository.findOne({ id: dto.contractId });
    if (!contract) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0004" }, HttpStatus.OK);
      // throw new BadRequestException({
      //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Hợp đồng', 'id', dto.contractId)
      // });
    }
    const contractDto: any = await this.queryRepository.findOne({ 'contract.id': dto.contractId });
    if (contractDto) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0012" }, HttpStatus.OK);
      // throw new BadRequestException({
      //   errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.contractId)
      // });
    }
    model.contract = contract;

    if (dto.type === LiquidationTypeEnum.TRANSFER) { // Thanh lý chuyển nhượng
      if (dto.proposal) {
        proposal = await this.proposalQueryRepository.findOne({ id: dto.proposal.id });
        if (!proposal) {
          throw new HttpException({ errorCode: "PCPROPOSAL001" }, HttpStatus.OK);
          // throw new BadRequestException({
          //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Đơn đề nghị', 'id', dto.proposal.id)
          // });
        }

        const proposalDto: any = await this.queryRepository.findOne({ 'proposal.id': dto.proposal.id });
        if (proposalDto) {
          throw new HttpException({ errorCode: "PCPROPOSAL002" }, HttpStatus.OK);
          // throw new BadRequestException({
          //   errors: ErrorConst.Error(ErrorConst.EXISTED, 'Đơn đề nghị', 'id', dto.proposal.id)
          // });
        }
        model.proposal = proposal;
        model.productCode = proposal.escrowTicket.propertyUnit.code;
        model.customer = proposal.customerTransfer;
        model.customer.employee = contract.primaryTransaction.customer.employee;
        proposal.customerTransferFormat.employee = contract.primaryTransaction.customer.employee;
        proposal.customerTransferFormat.identities = proposal.customerTransferFormat.personalInfo.identities;
        model.customerFormat = proposal.customerTransferFormat;
      } else {
        throw new HttpException({ errorCode: "PCPROPOSAL001" }, HttpStatus.OK);
        // throw new BadRequestException({
        //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Đơn đề nghị', 'id', dto.proposal.id)
        // });
      }
    } else { // Thanh lý chấm dứt
      model.proposal = {};
      model.productCode = contract.primaryTransaction.propertyUnit.code;
      model.customer = contract.primaryTransaction.customer;
    }

    if (!model.code) {
      const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_LIQUIDATION}`;
      model.code = await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    }
    model.name = `${CommonConst.BIEN_BAN_THANH_LY}-${contract.name}`;
    model.status = LiquidationStatusEnum.INIT;
    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    model.createdBy = user.id;
    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);

    // Lưu vào lịch sử sản phẩm
    let histories = {
      propertyUnitId: model.contract.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: dto.type === LiquidationTypeEnum.TRANSFER ? 'Thanh lý chuyển nhượng' + ' (' + model.customer.name + ')' : 'Thanh lý chấm dứt',
      contractStatus: 'Hợp đồng: ' + ContractTypeEnum.APPROVED,
      actionName: 'Tạo biên bản thanh lý',
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return { id: this.commandId, code: model.code };
  }

  async updateLiquidation(user: any, dto: UpdateLiquidationDto, actionName: string) {
    this.loggerService.log(this.context, clc.green("update service"));
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0013" }, HttpStatus.OK);
      // throw new BadRequestException({
      //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Thanh lý HĐ', 'id', 'null')
      // });
    }
    // if(dto.liquidationDate && !moment(dto.liquidationDate).toDate()){
    //   throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, "Ngày thanh lý", dto.liquidationDate.toString())});
    // }
    oldDto.liquidationDate = moment(dto.liquidationDate);
    let contract: any = {};
    let proposal: any = {};
    contract = await this.primaryContractQueryRepository.findOne({ id: dto.contractId });
    if (!contract) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0004" }, HttpStatus.OK);
      // throw new BadRequestException({
      //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Hợp đồng', 'id', dto.contractId)
      // });
    }
    if (oldDto.contract.id !== dto.contractId) {
      const contractDto: any = await this.queryRepository.findOne({ 'contract.id': dto.contractId });
      if (contractDto) {
        throw new HttpException({ errorCode: "PRIMARYCONTRACT0012" }, HttpStatus.OK);
        // throw new BadRequestException({
        //   errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.contractId)
        // });
      }
    }
    oldDto.contract = contract;

    if (dto.type === LiquidationTypeEnum.TRANSFER) { // Thanh lý chuyển nhượng
      if (dto.proposal) {
        proposal = await this.proposalQueryRepository.findOne({ id: dto.proposal.id });
        if (!proposal) {
          throw new HttpException({ errorCode: "PCPROPOSAL001" }, HttpStatus.OK);
          // throw new BadRequestException({
          //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Đơn đề nghị', 'id', dto.proposal.id)
          // });
        }
        if (oldDto.proposal && oldDto.proposal.id !== dto.proposal.id) {
          const liquidationDto: any = await this.queryRepository.findOne({ 'proposal.id': dto.proposal.id });
          if (liquidationDto) {
            throw new HttpException({ errorCode: "PCPROPOSAL002" }, HttpStatus.OK);
            // throw new BadRequestException({
            //   errors: ErrorConst.Error(ErrorConst.EXISTED, 'Đơn đề nghị', 'id', dto.proposal.id)
            // });
          }
        }
        oldDto.proposal = proposal;
        oldDto.productCode = proposal.escrowTicket.propertyUnit.code;
        oldDto.customer = proposal.customerTransfer;
        oldDto.customer.employee = contract.primaryTransaction.customer.employee;
        proposal.customerTransferFormat.employee = contract.primaryTransaction.customer.employee;
        proposal.customerTransferFormat.identities = proposal.customerTransferFormat.personalInfo.identities;
        oldDto.customerFormat = proposal.customerTransferFormat;
      } else {
        throw new HttpException({ errorCode: "PCPROPOSAL001" }, HttpStatus.OK);
        // throw new BadRequestException({
        //   errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Đơn đề nghị', 'id', dto.proposal.id)
        // });
      }
    } else { // Thanh lý chấm dứt
      oldDto.proposal = {};
      oldDto.productCode = contract.primaryTransaction.propertyUnit.code;
      oldDto.customer = contract.primaryTransaction.customer;
    }
    oldDto.type = dto.type;
    oldDto.name = `${CommonConst.BIEN_BAN_THANH_LY} ${contract.name}`;
    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.modifiedDate = new Date();
    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
    return { id: dto.id };
  }

  async requestApproveLiquidation(user: any, dto: LiquidationStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'request approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0013" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, "liquidation", "ID", dto.id)});
    }

    if (oldDto.status !== LiquidationStatusEnum.INIT && oldDto.status !== LiquidationStatusEnum.REJECTED) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0016" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "oldDto-Status")});
    }
    if (dto.status !== LiquidationStatusEnum.WAITING) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0015" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "liquidationStatus")});
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;
    oldDto.modifiedDate = new Date();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async approveLiquidation(user: any, dto: LiquidationStatusDto, actionName: string, authorization: string) {
    this.loggerService.log(this.context, 'approve liquidation');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0013" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id)});
    }

    if (oldDto.status !== LiquidationStatusEnum.WAITING) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0016" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "liquidationStatus")});
    }

    if (dto.status !== LiquidationStatusEnum.APPROVED && dto.status !== LiquidationStatusEnum.REJECTED) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0015" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "liquidationStatus")});
    }
    if (dto.status == LiquidationStatusEnum.REJECTED) {
      oldDto.status = LiquidationStatusEnum.REJECTED;
      oldDto.reason = dto.reason;
    } else {
      oldDto.status = LiquidationStatusEnum.APPROVED;
      await this.liquidationProgress(user, oldDto, authorization);
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.modifiedDate = new Date();
    let result = await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    // Lưu vào lịch sử sản phẩm
    let reason = '';
    let contractStatus = '';
    let actionNameUnit = '';
    if (dto.status === LiquidationStatusEnum.APPROVED) {
      if (oldDto.type === LiquidationTypeEnum.TRANSFER) {
        reason = 'Thanh lý chuyển nhượng' + ' (' + oldDto.customer.name + ')';
        contractStatus = 'Hợp đồng: ' + ContractTypeEnum.WAITING_COLLECT_AND_REFUND_MONEY;
      } else {
        reason = 'Thanh lý chấm dứt';
        contractStatus = 'Hợp đồng: ' + ContractTypeEnum.LIQUIDATED;
      }
      actionNameUnit = 'Duyệt biên bản thanh lý';
    } else if (dto.status === LiquidationStatusEnum.REJECTED) {
      reason = oldDto.reason;
      contractStatus = 'Hợp đồng: ' + ContractTypeEnum.APPROVED;
      actionNameUnit = 'Từ chối biên bản thanh lý';
    }
    let histories = {
      propertyUnitId: oldDto.contract.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: reason,
      contractStatus: contractStatus,
      actionName: actionNameUnit,
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result;
  }

  async deleteLiquidation(user: any, id: string, actionName: string) {
    this.loggerService.log(this.context, clc.green("delete service"));

    const oldDto: any = await this.queryRepository.findOne({ id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "Thanh lý HĐ", "ID", id) });
    }
    if (oldDto.status !== LiquidationStatusEnum.INIT) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "liquidationStatus") });
    }
    let model: any = { id };

    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    await this.executeCommand(Action.DELETE, actionName, this.commandId, model);
    return { id: id };
  }

  private axiosPost(url: string, data: any, authorization: string) {
    return axios.post(url, data, {
      headers: { 'Content-Type': 'application/json', Authorization: authorization },
      timeout: parseInt(process.env.TIMEOUT_API) || 120000
    });
  }

  private async liquidationProgress(user, liquidationDto, authorization) {
    if (liquidationDto.type === LiquidationTypeEnum.TRANSFER) {
      liquidationDto.customerFormat.identities = [{ value: liquidationDto.customer.identityNumber, date: liquidationDto.customer.identityIssueDate, place: liquidationDto.customer.identityIssueLocation }];
      if (!liquidationDto.customer.id) { // Nếu khách hàng chưa tồn tại trong KH giao dịch thì tạo mới
        liquidationDto.customerFormat.active = true;
        let customerInfo = await this.createCustomer(liquidationDto.customerFormat, user);
        if (customerInfo && customerInfo.customerId) {
          liquidationDto.customerFormat.id = customerInfo.customerId;
        }
      }

      const newTicket = await this.propertyClient.sendDataPromise({
        action: 'liquidationApproved',
        escrowTicketId: liquidationDto.proposal.escrowTicket.id,
        customer: liquidationDto.customerFormat,
        contract: {
          id: liquidationDto.contract.id,
          code: liquidationDto.contract.code,
        },
        user,
        isClone: true
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.LIQUIDATION_PRIMARY_TRANSACTION);

      //Tạo mới KH chính thức từ KH tiềm năng
      if (liquidationDto?.proposal?.customerTransferFormat?.customerType === 'demand') {
        await this.axiosPost(
          process.env.CREATE_CUSTOMER,
          { id: newTicket.id },
          authorization
        );
      }

      liquidationDto.proposal.escrowTicket.oldCustomer = Object.assign({}, liquidationDto.proposal.escrowTicket.customer); // Lưu thêm thông tin khách hàng cũ
      liquidationDto.proposal.escrowTicket.customer = liquidationDto.customerFormat;
      // Tạo HD mới
      const newContract = await this.primaryContractDomainService.cloneContract(user.id, liquidationDto, 'primary-contractCreated', newTicket);
      // tích hợp sap k chờ response
      this.primaryContractClient.sendData({ oldContract: newContract.oldContract, newContract }
        , CmdPatternConst.LISTENER.SEND_SAP_LIQUIDATED_CONTRACT)
      // Thanh lý HD cũ
      await this.primaryContractDomainService.liquidationContract(user.id, liquidationDto, 'primary-contractUpdated');
      // Tạo phiếu thu hợp đồng mới & Tạo phiếu chi hợp đồng cũ
      if (liquidationDto.type === LiquidationTypeEnum.TRANSFER) {
        await this.transactionClient.sendDataPromise({
          contractId: newContract.id,
          liquidationContractId: liquidationDto.contract?.id,
          propertyTicketId: liquidationDto.contract?.primaryTransaction?.id,
          user,
          installments: newContract.installments
        }, CmdPatternConst.TRANSACTION.CLONE_RECEIPTS)
      }
    }
    else {
      const body = {
        projectId: liquidationDto.contract.primaryTransaction.project.id,
        propertyIds: [liquidationDto.contract.primaryTransaction.propertyUnit.id],
        reasonRevoke: `Thu hồi sản phẩm ${liquidationDto.contract.primaryTransaction.propertyUnit.code} khi thanh lý hợp đồng`
      };

      // thanh lý phiếu cọc & thu hồi sản phẩm
      await this.propertyClient.sendDataPromise({
        escrowTicketId: liquidationDto.contract.primaryTransaction.id,
        user,
        isClone: false,
        propertyModel: body
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.LIQUIDATION_PRIMARY_TRANSACTION);

      // Thanh lý HD cũ
      await this.primaryContractDomainService.liquidationContract(user.id, liquidationDto, 'primary-contractUpdated');

    }

  }
  private async createCustomer(customer, user) {
    let isExist: boolean = await this.customerClient.sendDataPromise({
      phoneNumber: customer.phoneNumber,
      email: customer.email
    }, CmdPatternConst.CUSTOMER.CHECK_CUSTOMER_EXIST_BY_PHONE_EMAIL);

    let res: any = {};
    if (!isExist) { // chưa tồn tại thì tạo mới
      res = await this.customerClient.sendDataPromise({
        action: CmdPatternConst.CUSTOMER.CREATE_CUSTOMER_FROM_LIQUIDATION_CONTRACT,
        model: customer,
        notiUser: user
      }, CmdPatternConst.CUSTOMER.CREATE_CUSTOMER_FROM_LIQUIDATION_CONTRACT);
    }
    return res;
  }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreateLiquidationCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.UPDATE:
        commandObject = new UpdateLiquidationCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.DELETE:
        commandObject = new DeleteLiquidationCommand(
          actionName,
          commandId,
          item
        );
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
}
