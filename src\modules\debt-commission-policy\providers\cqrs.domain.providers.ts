import { Connection } from 'mongoose';
import { CommonConst } from '../../shared/constant';
import { DebtCommissionPolicySchema } from '../schemas/schema';

export const CqrsProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) => connection.model(CommonConst.DEBT_COMMISION_POLICY_COLLECTION, DebtCommissionPolicySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
