import { Model } from "mongoose";
import { Inject, Injectable } from "@nestjs/common";
import { IOwnershipCertificateDocument } from "../interfaces/document.interface";
import { IResult } from "../../shared/interfaces/result.interface";
import _ = require("lodash");
import { CommonConst } from "../../shared/constant/index";
import { EmployeeClient } from "../../mgs-sender/employee.client";
import { StsClient } from "../../mgs-sender/sts.client";
import { CustomerClient } from "../../mgs-sender/customer.client";
import { CommonUtils } from "../../shared/classes/class-utils";

@Injectable()
export class OwnershipCertificateQueryRepository {
  constructor(
    @Inject(CommonConst.OWNERSHIP_CETIFICATE_QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IOwnershipCertificateDocument> // private readonly employeeClient: EmployeeClient,
  ) { }

  async findOne(query, projection = {}): Promise<IOwnershipCertificateDocument> {
    return await this.readModel.findOne(query, projection).lean().exec();
  }
  async findAll(page, pageSize, query): Promise<any> {
    let project: any = { id: 1 };

    if (!_.isEmpty(query._fields)) {
      const fields = query._fields.split(",");
      fields.forEach((f) => {
        project[f.trim()] = 1;
      });
      delete query._fields;
    }

    const sort: any = {
      updatedDate: -1,
    };

    const skip = (page - 1) * pageSize;

    const [rows, total] = await Promise.all([
      this.readModel
        .find(query, project) // ✅ dùng đúng project ở đây
        .sort(sort)
        .skip(skip)
        .limit(pageSize)
        .lean()
        .exec(),

      this.readModel.countDocuments(query),
    ]);

    return {
      rows,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async create(readmodel): Promise<IOwnershipCertificateDocument> {
    return await this.readModel.create(readmodel);
  }
  async update(model): Promise<IOwnershipCertificateDocument> {
    return await this.readModel.updateOne({ id: model.id }, model).exec();
  }
}
