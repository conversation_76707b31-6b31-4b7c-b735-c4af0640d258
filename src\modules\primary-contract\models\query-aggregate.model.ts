import { AggregateRoot } from "@nestjs/cqrs";
import { CommandModel } from "../../shared/eventStream/models/command.model";
import { PrimaryContractQueryCreatedEvent } from "../../primary-contract.queryside/events/primary-contract-query-created.evt";
import { PrimaryContractQueryUpdatedEvent } from "../../primary-contract.queryside/events/primary-contract-query-updated.evt";
import { PrimaryContractQueryDeletedEvent } from "../../primary-contract.queryside/events/primary-contract-query-deleted.evt";

export class QueryAggregateModel extends AggregateRoot {
  constructor(private readonly id: string) {
    super();
  }

  addItem(commandModel: CommandModel) {
    this.apply(new PrimaryContractQueryCreatedEvent(commandModel));
  }

  updateItem(commandModel: CommandModel) {
    this.apply(new PrimaryContractQueryUpdatedEvent(commandModel));
  }

  deleteItem(commandModel: CommandModel) {
    this.apply(new PrimaryContractQueryDeletedEvent(commandModel));
  }
}
