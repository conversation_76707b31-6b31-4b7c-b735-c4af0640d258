import { BadRequestException, Injectable } from "@nestjs/common";
import { CommandBus } from "@nestjs/cqrs";
import { CreatePrimaryContractCommand } from "./commands/impl/create-primary-contract.cmd";
import { UpdatePrimaryContractCommand } from "./commands/impl/update-primary-contract.cmd";
import { DeletePrimaryContractCommand } from "./commands/impl/delete-primary-contract.cmd";
import { PrimaryContractQueryRepository } from "../primary-contract.queryside/repository/primary-contract-query.repository";
import { MsxLoggerService } from "../logger/logger.service";
import { CodeGenerateService } from "../code-generate/service";
import { Action } from "../shared/enum/action.enum";
import { HandoverPrimaryContractDto, OwnershipCertificatePrimaryContractDto, UpdateDepositConfirmDto, } from "./dto/primary-contract.dto";
import { ErrorConst } from "../shared/constant/error.const";
import { HandoverQueryService } from "../handover.queryside/service";
import { HandoverStatusEnum } from "../shared/enum/status.enum";
import { StatusContractEnum, StatusEnum, SyncErpCodeEnum } from "../shared/enum/primary-contract.enum";
import { PropertyClient } from "../mgs-sender/property.client";
import { CmdPatternConst } from "../shared/constant";
import { NotificationClient } from "../mgs-sender/notification.client";
import moment = require("moment");
import { CareClient } from "../mgs-sender/care.client";
import { HandoverScheduleDomainService } from "../handover-schedule.domain/service";
import { SyncErpClient } from '../mgs-sender/syncErp.client';
import { HandoverScheduleActionNameConst, HandoverScheduleStatusNameConst } from "../shared/constant/handover.const";
import { BaseService, ErrorService } from "../../../shared-modules";
import { OwnershipCertificateQueryRepository } from "../ownership-certificate/repository/ownership-certificate.query.repository";
import { pushFileS3 } from "../shared/utils/transform";
import * as FormData from 'form-data';
const uuid = require("uuid");
import * as axios from 'axios';
@Injectable()
export class PrimaryContractCustomerDomainService extends BaseService {
  private readonly context = PrimaryContractCustomerDomainService.name;
  private commandId: string;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly loggerService: MsxLoggerService,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly queryRepository: PrimaryContractQueryRepository,
    private readonly handoverQueryService: HandoverQueryService,
    private readonly handoverScheduleDomainService: HandoverScheduleDomainService,
    private readonly ownershipCertificateQueryRepository: OwnershipCertificateQueryRepository,
    private readonly propertyClient: PropertyClient,
    private readonly notificationClient: NotificationClient,
    private readonly careClient: CareClient,
    private readonly syncErpClient: SyncErpClient,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }




  async updateDepositConfirm(user, dto: UpdateDepositConfirmDto, actionName) {
    this.loggerService.log(this.context, 'update Deposit Confirm');

    const customer = await this.careClient.sendDataPromise({ userId: user.id }, CmdPatternConst.CARE.GET_CUSTOMER_BY_ID);

    if (!customer) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'customer') });
    }

    const identities = customer.identities || [];

    if (identities.length < 1) {
      return [];
    }

    const identitiesArr = identities.map(identity => {
      return identity.value
    });

    const query = {
      id: dto.id,
      $or: [
        { "primaryTransaction.customer.identityNumber": { $in: identitiesArr } },
        { "primaryTransaction.customer.identities.value": { $in: identitiesArr } },
        { "primaryTransaction.customer2.identityNumber": { $in: identitiesArr } },
        { "primaryTransaction.customer2.identities.value": { $in: identitiesArr } }
      ]
    };

    const oldDto: any = await this.queryRepository.findOne(query);
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (oldDto.depositConfirmFromCustomer) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "depositStatus") });
    }

    this.commandId = uuid.v4();
    oldDto.depositConfirmFromCustomer = true;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto);
  }
  //xác nhận căn hộ bàn giao
  async handover(user: any, dto: HandoverPrimaryContractDto, actionName: string) {
    this.loggerService.log(this.context, 'update contract handover');

    if (![HandoverStatusEnum.handed, HandoverStatusEnum.later].includes(dto.handoverStatus)) {
      return this.getResponse("PRIMARYCONTRACT0003");
    }

    const oldDto: any = await this.queryRepository.findOne({
      id: dto.id,
      handoverStatus: { $in: [HandoverStatusEnum.scheduled, HandoverStatusEnum.later] },
      status: StatusContractEnum.APPROVED
    });
    if (!oldDto) {
      return this.getResponse("PRIMARYCONTRACT0004");
    }
    oldDto.deliveryDate = new Date(dto.deliveryResult?.deliveryDate) || new Date();
    // lấy thông tin bàn giao
    const handOver = await this.handoverQueryService.findOneByQuery({
      status: StatusEnum.ACTIVE,
      'project.id': oldDto.primaryTransaction.project.id
    });
    if (!handOver) {
      return this.getResponse("HANDOVER0004");
    }

    this.commandId = uuid.v4();
    if (dto.handoverStatus === HandoverStatusEnum.handed) {
      dto.deliveryResult.deliveryDate = oldDto.deliveryDate;
      oldDto.deliveryResult = dto.deliveryResult;
      if (oldDto.handoverStatus && oldDto.handoverStatus != HandoverStatusEnum.handed) {
        oldDto.handoverStatus = HandoverStatusEnum.handed;
        let syncConfig = await this.syncErpClient.sendDataPromise(oldDto.primaryTransaction.project.id, CmdPatternConst.SYNC_ERP.GET_CAMPAIGN_ERP_BY_PROJECT_ID);
        if (syncConfig && oldDto.syncErpData && oldDto.syncErpData.contractid) {
          let property: any = '';
          if (oldDto.primaryTransaction && oldDto.primaryTransaction.propertyUnit && oldDto.primaryTransaction.propertyUnit.attributes && oldDto.primaryTransaction.propertyUnit.attributes.length > 21) {
            if (oldDto.primaryTransaction.propertyUnit.attributes[21].value) {
              property = oldDto.primaryTransaction.propertyUnit.attributes[21].value;
            }
          }

          let data: any = {
            formid: 'contract01',
            contractid: oldDto.syncErpData.contractid,
            property: property,
            statusdate: moment(oldDto.deliveryDate).format('YYYY-MM-DD hh:mm:ss A'),
            status: 'BGI',
            impstatus: 'W'
          }
          let dataSendCRM: any = {
            action: 'contract01',
            data: [data]
          }
          // send data sync erp
          await this.syncErpClient.sendDataPromise(dataSendCRM, CmdPatternConst.SYNC_ERP.SEND_REQUEST_TO_ERP);
        }
      }

      // save rating to project
      const rating = {
        userId: user.id,
        rate: dto.deliveryResult.rate
      };
      this.propertyClient.sendDataPromise({ id: oldDto.primaryTransaction.project.id, rating }, CmdPatternConst.PROJECT.RATING);

      // send notification to customer
      this.notificationClient.createNotificationCare(
        "care_deliveryHanded",
        null,
        user.id,
        "primary-contract",
        oldDto.id,
        {
          code: oldDto.primaryTransaction.propertyUnit.code,
          time: oldDto.deliveryDate ? moment(oldDto.deliveryDate).format('HH:MM') : '',
          date: oldDto.deliveryDate ? moment(oldDto.deliveryDate).format('DD/MM/YYYY') : ''
        }
      );

      // Cập nhật đã bàn giao căn hộ
      let updatePropery = {
        query: { id: oldDto.primaryTransaction.propertyUnit.id },
        model: { $set: { "contract.handoverStatus": HandoverStatusEnum.handed } }
      }
      this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
    } else {
      // send notification to customer
      this.notificationClient.createNotificationCare(
        "care_deliveryLater",
        null,
        user.id,
        "primary-contract",
        oldDto.id,
        {
          code: oldDto.primaryTransaction.propertyUnit.code,
          time: oldDto.deliveryDate ? moment(oldDto.deliveryDate).format('HH:MM') : '',
          date: oldDto.deliveryDate ? moment(oldDto.deliveryDate).format('DD/MM/YYYY') : '',
          nextTime: oldDto.deliveryHistories ? oldDto.deliveryHistories.length + 2 : 2,
          hotline: handOver.hotline ? handOver.hotline : ''
        }
      );
      oldDto.isSendDelivery = false;

      // later delete handover schedule
      if (dto.handoverStatus === HandoverStatusEnum.later) {
        this.handoverScheduleDomainService.deleteHandoverScheduleNew(user, oldDto.id);
      }
    }
    oldDto.handoverStatus = dto.handoverStatus;
    oldDto.deliveryItems = dto.deliveryItems;
    oldDto.files = dto.files;

    const history = {
      deliveryDate: new Date(oldDto.deliveryDate) || new Date(),
      isPass: dto.handoverStatus === HandoverStatusEnum.handed,
      items: dto.deliveryItems
    };

    if (oldDto.deliveryHistories) {
      oldDto.deliveryHistories.push(history);
    } else {
      oldDto.deliveryHistories = [history];
    }

    oldDto.modifiedDate = new Date();

    // Xác nhận bàn giao
    this.addHistoriesHandover(oldDto, user, new Date(), HandoverScheduleStatusNameConst.HANDED_OVER, HandoverScheduleActionNameConst.ACCEPT_HANDOVER, dto.handoverStatus);
    if (dto.handoverStatus === HandoverStatusEnum.handed) {
      this.handoverScheduleDomainService.deleteHandoverScheduleNew(user, oldDto.id);
    }
    await this.executeCommand(Action.UPDATE, "update", this.commandId, oldDto);
    if (dto.handoverStatus === HandoverStatusEnum.handed) {
      await this.sendContractToSap([oldDto]);
    }

    return this.getResponse(0);

  }
  async sendSap(
    dto: any,
  ) {
    const oldDto: any = await this.queryRepository.findOne({
      id: dto.id,
      handoverStatus: { $in: [HandoverStatusEnum.handed] },
      status: StatusContractEnum.APPROVED
    });
    if (!oldDto) {
      return this.getResponse("PRIMARYCONTRACT0004");
    }
    const contracts = [];
    contracts.push(oldDto.toObject());
    const sapResponse = await this.sendContractToSap(contracts);
    if (!sapResponse || sapResponse.statusCode !== 200) {
      return this.getResponse('COME0013', sapResponse);
    }
    // const updateOps = {
    //   $set: {
    //     sapCode: sapResponse.contractNumber,
    //     modifiedDate: new Date()
    //   }
    // };
    // await this.queryRepository.updateOne({ id: dto.id }, updateOps);
    return this.getResponse(0);
  }
  private async sendContractToSap(handovers: any) {
    const result = await axios.default.post(
      process.env.HANDOVER_SYNC_SAP || 'https://uat-api-crm.datxanh.com.vn/msx-masterdata-producer/api/v1/contract/handover/sync',
      handovers,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    return result?.data;
  }
  //xác nhận bàn giao sổ
  async ownershipCertificate(user: any, dto: OwnershipCertificatePrimaryContractDto, actionName: string) {
    this.loggerService.log(this.context, 'update contract handover');

    if (![HandoverStatusEnum.Cer_handed_over, HandoverStatusEnum.Cer_in_process, HandoverStatusEnum.Cer_ready_handover].includes(dto.cerHandoverStatus)) {
      return this.getResponse("PRIMARYCONTRACT0025");
    }

    const oldDto: any = await this.queryRepository.findOne({
      id: dto.id,
      cerHandoverStatus: { $in: [HandoverStatusEnum.scheduled, HandoverStatusEnum.later, HandoverStatusEnum.Eligible, HandoverStatusEnum.Cer_in_process, HandoverStatusEnum.Cer_ready_handover] },
      status: StatusContractEnum.APPROVED
    });
    if (!oldDto) {
      return this.getResponse("PRIMARYCONTRACT0004");
    }
    oldDto.deliveryDate = new Date(dto.deliveryDate) || new Date();

    // lấy thông tin bàn giao
    const ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({
      isActive: StatusEnum.ACTIVE,
      'project.id': oldDto.primaryTransaction.project.id
    });
    if (!ownershipCertificate) {
      return this.getResponse("PRIMARYCONTRACT0026");
    }

    if (dto.cerHandoverStatus === oldDto.cerHandoverStatus) {
      return this.getResponse("PRIMARYCONTRACT0025");
    }

    this.commandId = uuid.v4();
    if (dto.cerHandoverStatus === HandoverStatusEnum.later) {
      oldDto.isSendDelivery = false;
    }

    if (oldDto.cerHandoverStatus === HandoverStatusEnum.scheduled && dto.cerHandoverStatus === HandoverStatusEnum.Cer_in_process && dto.itemsForEligible?.length > 0) {
      oldDto.eligibleItems = dto.itemsForEligible;
      oldDto.eligibleFiles = dto.files;

      const history = {
        deliveryDate: new Date(oldDto.deliveryDate) || new Date(),
        isPass: dto.cerHandoverStatus === HandoverStatusEnum.Cer_in_process,
        items: dto.itemsForEligible
      };

      if (oldDto.deliveryHistories) {
        oldDto.deliveryHistories.push(history);
      } else {
        oldDto.deliveryHistories = [history];
      }
      oldDto.modifiedDate = new Date();

      let HandoverScheduleStatus = HandoverScheduleStatusNameConst.CER_IN_PROCESS;
      oldDto.cerHandoverStatus = HandoverStatusEnum.Cer_in_process

      // Xác nhận bàn giao
      this.addHistoriesHandoverAndUpdateSyncErpData(oldDto, user, new Date(), HandoverScheduleStatus, HandoverScheduleActionNameConst.ACCEPT_HANDOVER, SyncErpCodeEnum.XD09, dto.cerHandoverStatus);
      return await this.executeCommand(Action.UPDATE, "update", this.commandId, oldDto);
    }
    if (oldDto.cerHandoverStatus === HandoverStatusEnum.Cer_in_process && dto.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover && dto.itemsCerInProcess?.length > 0) {
      oldDto.cerInProcessItems = dto.itemsCerInProcess;
      oldDto.cerInProcessFiles = dto.files;

      const history = {
        deliveryDate: new Date(oldDto.deliveryDate),
        isPass: dto.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover,
        items: dto.itemsCerInProcess
      };

      if (oldDto.deliveryHistories) {
        oldDto.deliveryHistories.push(history);
      } else {
        oldDto.deliveryHistories = [history];
      }
      oldDto.modifiedDate = new Date();

      let HandoverScheduleStatus = HandoverScheduleStatusNameConst.CER_READY_HANDOVER;
      oldDto.cerHandoverStatus = HandoverStatusEnum.Cer_ready_handover
      // Xác nhận bàn giao
      this.addHistoriesHandoverAndUpdateSyncErpData(oldDto, user, new Date(), HandoverScheduleStatus, HandoverScheduleActionNameConst.ACCEPT_HANDOVER, SyncErpCodeEnum.XD10, dto.cerHandoverStatus);
      return await this.executeCommand(Action.UPDATE, "update", this.commandId, oldDto);
    }

    if (oldDto.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover && dto.cerHandoverStatus === HandoverStatusEnum.Cer_handed_over && dto.itemsForCerReadyHandover?.length > 0) {
      oldDto.cerReadyHandoverItems = dto.itemsForCerReadyHandover;
      oldDto.cerReadyHandoverFiles = dto.files;

      const history = {
        deliveryDate: new Date(oldDto.deliveryDate),
        isPass: dto.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover,
        items: dto.itemsForCerReadyHandover
      };

      oldDto.deliveryHistories = oldDto.deliveryHistories.push(history);
      if (oldDto.deliveryHistories) {
        oldDto.deliveryHistories.push(history);
      } else {
        oldDto.deliveryHistories = [history];
      }

      oldDto.modifiedDate = new Date();


      let HandoverScheduleStatus = HandoverScheduleStatusNameConst.CER_HANDED_OVER;
      oldDto.cerHandoverStatus = HandoverStatusEnum.Cer_handed_over
      // Xác nhận bàn giao
      this.addHistoriesHandoverAndUpdateSyncErpData(oldDto, user, new Date(), HandoverScheduleStatus, HandoverScheduleActionNameConst.ACCEPT_HANDOVER, SyncErpCodeEnum.XD11, dto.cerHandoverStatus);
      return await this.executeCommand(Action.UPDATE, "update", this.commandId, oldDto);
    }

    return this.getResponse("PRIMARYCONTRACT0025");
  }

  async addHistoriesHandover(contract, user, handoverStartTime, propertyStatusName, actionName, handoverStatus) {
    let historiesHandover = {
      propertyStatusName: propertyStatusName,
      actionName: actionName, // Hành động
      modifiedBy: user.id,
      modifiedByName: user.name, // Tên người thực hiện
      handoverStartTime: handoverStartTime
    }
    let anal_ppt9 = "XD03A";// trạng thái sẵn sàng bàn giao, nếu bàn giao sẽ đổi trạng thái, nếu bàn giao sau thì giữ nguyên
    if (handoverStatus === HandoverStatusEnum.handed) {
      anal_ppt9 = "XD04"
    }
    let updatePropery = {
      query: { id: contract.primaryTransaction.propertyUnit.id },
      model: { $push: { historiesHandover: historiesHandover } },
      $set: {
        "contract.handoverStatus": handoverStatus,
        "syncErpData.anal_ppt9": anal_ppt9,
      },
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
  }
  async addHistoriesHandoverAndUpdateSyncErpData(contract, user, handoverStartTime, propertyStatusName, actionName, anal_ppt9, cerHandoverStatus) {
    let historiesHandover = {
      propertyStatusName: propertyStatusName,
      actionName: actionName, // Hành động
      modifiedBy: user.id,
      modifiedByName: user.name, // Tên người thực hiện
      handoverStartTime: handoverStartTime
    }
    let updatePropery = {
      query: { id: contract.primaryTransaction.propertyUnit.id },
      model: {
        $push: { historiesHandover: historiesHandover },
        $set: {
          "contract.cerHandoverStatus": cerHandoverStatus,
          "syncErpData.anal_ppt9": anal_ppt9,
        },
      },
    };

    this.propertyClient.sendDataPromise(
      updatePropery,
      CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY
    );
  }
  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.UPDATE:
        commandObject = new UpdatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.DELETE:
        commandObject = new DeletePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
  async uploadFile(file: any): Promise<any> {
    try {
      const response = await pushFileS3(file);
      return response;
    } catch (error) {
      return {};
    }
  }
  getMimeType(ext: string): string {
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'heic':
        return 'image/heic';
      default:
        return 'application/octet-stream';
    }
  }

  async uploadImageToS3(
    fileBuffer: Buffer,
    originalName: string,
    folder = 'image', // mặc định đẩy vào thư mục 'avatar'
  ) {
    const maxSize = 5 * 1024 * 1024; // 5MB

    const extension = originalName.split('.').pop()?.toLowerCase();

    // ✅ Validate định dạng
    if (!['jpg', 'jpeg', 'png', 'webp', 'heic'].includes(extension || '')) {
      return this.getResponse('IMPORT_ERROR_FORMAT');
    }


    // ✅ Validate dung lượng
    if (fileBuffer.length > maxSize) {
      return this.getResponse('IMPORT_ERROR_SIZE');
    }

    const now = moment();
    const fileName = `${folder}_${now.format('YYYYMMDD_HHmmss')}.${extension}`;

    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: fileName,
      contentType: this.getMimeType(extension),
    });
    formData.append('path', folder);

    try {
      const response = await axios.default.post(
        process.env.MTD_UPLOAD,
        formData,
        { headers: { ...formData.getHeaders() } }
      );

      return {
        fileName,
        fileUrl: response.data.data.key,
      };
    } catch (error) {
      return this.getResponse('IMPORT_ERROR');
    }
  }
  async uploadMultipleFiles(
    files: { buffer: Buffer; originalName: string }[]
  ): Promise<string[] | { error: string }> {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const folder = 'primary-contract';

    const allowedExtensions = [
      'jpg', 'jpeg', 'png', 'webp', 'heic',
      'mp4', 'avi', 'mov', 'wmv',
      'xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx',
      'jfif', 'rar', 'zip', 'msg', 'txt'
    ];

    if (files.length > 10) {
      return { error: 'IMPORT_ERROR_TOO_MANY_FILES' };
    }

    const uploadedLinks: string[] = [];

    for (const file of files) {
      const extension = file.originalName.split('.').pop()?.toLowerCase() || '';
      if (!allowedExtensions.includes(extension)) {
        return { error: 'IMPORT_ERROR_FORMAT' };
      }

      if (file.buffer.length > maxSize) {
        return { error: 'IMPORT_ERROR_SIZE' };
      }

      const formData = new FormData();
      formData.append('file', file.buffer, {
        filename: file.originalName,
      });
      formData.append('path', folder);
      formData.append('useOriginalName', "true");

      try {
        const response = await axios.default.post(
          process.env.MTD_UPLOAD,
          formData,
          { headers: { ...formData.getHeaders() } }
        );

        const fileUrl = response.data?.data?.key;
        uploadedLinks.push(fileUrl);
      } catch (error) {
        return { error: 'IMPORT_ERROR' };
      }
    }

    return uploadedLinks;
  }
}
