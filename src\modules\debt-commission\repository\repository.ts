import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { CommonConst } from '../../shared/constant';
import { IDebtCommissionDocument } from '../interfaces/document.interface';
import { AwesomeLogger } from '../../../../shared-modules';

@Injectable()
export class DebtCommissionRepository {

  private logger = new AwesomeLogger(DebtCommissionRepository.name);

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IDebtCommissionDocument>,
  ) { }

  async findAll(page: number, pageSize: number, query: any = {}): Promise<any> {
    this.logger.info('findAll');

    console.log('query', query);

    const conditions: any[] = [
      {
        $match: query,
      },
      {
        $sort: { modifiedDate: -1 },
      },
      {
        $facet: {
          paginatedResults: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
          totalCount: [{ $count: 'count' }],
        }
      },
    ];
    return await this.readModel.aggregate(conditions)
      .allowDiskUse(true)
      .exec()
      .then(result => {
        const total = result[0].totalCount[0] ? result[0].totalCount[0].count : 0;
        return {
          page,
          pageSize,
          total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
          rows: result[0].paginatedResults
        };
      });
  }

  async find(query, exceptQuery?): Promise<any[]> {

    return await this.readModel.find(query, exceptQuery)
      .exec()
      .then(result => {
        return result;
      });
  }

  /**
   * find One
   */
  async findOne(query): Promise<any> {

    return await this.readModel.findOne(query)
      .exec()
      .then(result => {
        console.log('result commission', result);
        return result;
      });
  }

  async findOneFarthestPeriod(query: any = {}): Promise<any> {
    return await this.readModel.findOne(query)
      .sort({ periodTo: -1 })
      .exec()
      .then(result => {
        return result;
      });
  }

  async findDebtCommissionById(id: string, exceptQuery?): Promise<IDebtCommissionDocument> {
    return await this.readModel.findOne({ id }, exceptQuery)
      .exec()
      .then((response) => {
        return response;
      }).catch((exp) => {
        return exp;
      });
  }

  async create(readmodel): Promise<IDebtCommissionDocument> {
    return await this.readModel.create(readmodel)
      .then((response) => {
        this.logger.info('createEvent Commission at query side');
        return response;
      }).catch((error) => {
        console.log('error', error);
        return error;
      });
  }


  async update(model): Promise<IDebtCommissionDocument> {

    this.logger.info('update-repository', model);

    return await this.readModel.updateOne({ id: model.id }, model)
      .then((response) => {
        this.logger.info('updated Commission at query side');
        return response;
      }).catch((error) => {
        this.logger.info('updated error =>', error);
        return error;
      });
  }

  async updateMany(query, updateQuery): Promise<IDebtCommissionDocument> {

    this.logger.info('update-many-repository');

    return await this.readModel.updateMany(query, updateQuery)
      .then((response) => {
        this.logger.info('updated many Commission at query side');
        return response;
      }).catch((error) => {
        this.logger.info('updated error =>', error);
        return error;
      });
  }

  async delete(model): Promise<any> {

    this.logger.info('Delete Commission at query side');
    // console.log(model);

    return await this.readModel.deleteOne({ id: model.id })
      .then((response) => {
        this.logger.info('Deleted Commission at query side');
        console.log(response);
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteMany(model): Promise<any> {

    this.logger.info('Delete many Commission at query side');
    // console.log(model);

    return await this.readModel.deleteMany({ id: model.ids })
      .then((response) => {
        this.logger.info('Deleted Commission at query side');
        console.log(response);
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async listAll(query: any = {}) {
    return await this.readModel.find(query).exec();
  }
}
