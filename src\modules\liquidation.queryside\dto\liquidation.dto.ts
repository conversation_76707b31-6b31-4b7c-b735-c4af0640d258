import { ApiModelProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsNumber,
  IsDate,
  IsOptional,
  IsString
} from "class-validator";
import { trim, toNumber } from "lodash";

export class FindAllQueryDto {
  @ApiModelProperty({
    description: "Số trang",
    example: 1,
  })
  @Transform((value) => toNumber(value, { default: 1, min: 1 }))
  @IsNumber()
  @IsOptional()
  page: number;

  @ApiModelProperty({
    description: "Số lượng item 1 trang",
    example: 10,
  })
  @Transform((value) => toNumber(value, { default: 20, min: 1, max: 100 }))
  @IsNumber()
  @IsOptional()
  pageSize: number;

  @Transform((value) => trim(value))
  @IsOptional()
  search: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  type?: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  statuses?: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  projectIds?: string;

  @Transform((value) => trim(value))
  @IsString()
  @IsOptional()
  _fields: string;

  @Transform((value) => trim(value))
  @IsOptional()
  sort: string;
}
