import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from "@nestjs/common";
import { OwnershipCertificateQueryService } from "./service";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { RolesGuard } from "../../common/guards/roles.guard";
import { AuthGuard } from "@nestjs/passport";
import { ACGuard, UseRoles } from "nest-access-control";
import { Usr } from "../shared/services/user/decorator/user.decorator";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { ValidationPipe } from "../../common/pipes/validation.pipe";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
import { query } from "express";
import { changeStatusOwnershipCertificateDto, CreateOwnershipCertificateDto, DeleteOwnershipCertificateDto, UpdateOwnershipCertificateDto } from "./dto/ownership-certificate.dto";

@Controller("v1/ownership-certificate")
@UseGuards(JwtAuthGuard)
export class OwnershipCertificateQueryController {
  constructor(private readonly ownershipCertificateService: OwnershipCertificateQueryService) { }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.OWNERSHIP_CETIFICATE_GET_ALL)
  @Get()
  findAll(@User() user, @Query() query: any) {
    return this.ownershipCertificateService.findAll(query, user);
  }
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.OWNERSHIP_CETIFICATE_GET_ID)
  @Get(":id")
  findById(@Param("id") id: string, @Query() query) {
    return this.ownershipCertificateService.findById(id, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.OWNERSHIP_CETIFICATE_CREATE)
  @Post()
  async createownershipCertificate(
    @User() user,
    @Body(new ValidationPipe()) dto: CreateOwnershipCertificateDto,
  ) {
    return await this.ownershipCertificateService.create(
      user,
      dto,
    );
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.OWNERSHIP_CETIFICATE_UPDATE)
  @Put()
  async updateownershipCertificate(
    @User() user,
    @Body(new ValidationPipe()) dto: UpdateOwnershipCertificateDto,
  ) {
    return await this.ownershipCertificateService.update(
      user,
      dto,
    );
  }
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.OWNERSHIP_CETIFICATE_CHANGE_STATUS)
  @Put('change-status')
  async changeStatusownershipCertificate(
    @User() user,
    @Body(new ValidationPipe()) dto: changeStatusOwnershipCertificateDto,
  ) {
    return await this.ownershipCertificateService.changeStatus(
      user,
      dto,
    );
  }
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.OWNERSHIP_CETIFICATE_DELETE)
  @Delete()
  async deleteownershipCertificate(
    @User() user,
    @Body(new ValidationPipe()) dto: DeleteOwnershipCertificateDto,
  ) {
    return await this.ownershipCertificateService.delete(user, dto);
  }
}


