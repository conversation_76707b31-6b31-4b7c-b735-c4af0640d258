import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { CommonConst } from '../../shared/constant/index';
import { MsxLoggerService } from '../../logger/logger.service';
import _ = require('lodash');
import { IDebtExpenseListDocument } from '../interfaces/document.interface';

@Injectable()
export class DebtExpenseListRepository {
    private readonly context = DebtExpenseListRepository.name;
    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model<IDebtExpenseListDocument>,
        private readonly loggerService: MsxLoggerService
    ) { }

    async findAll(): Promise<any> {
        this.loggerService.log(this.context, 'findAll');
        return await this.readModel.find({}).sort({ level: 1 })
            .exec()
            .then(result => result);
    }

    async find(query, exceptQuery?): Promise<IDebtExpenseListDocument[]> {
        return await this.readModel.find(query, exceptQuery)
            .exec()
            .then(result => result);
    }

    async findOne(query): Promise<any> {
        return await this.readModel.findOne(query)
            .exec()
            .then(result => result);
    }

    async findById(id: string, exceptQuery?): Promise<IDebtExpenseListDocument> {
        return await this.readModel.findOne({ id }, exceptQuery)
            .exec()
            .then(result => result);
    }

    async create(readmodel): Promise<IDebtExpenseListDocument> {
        this.loggerService.log(this.context, 'Create Expense list at query side');
        
        try {
            const result = await this.readModel.create(readmodel);
            return result;
        } catch (error) {
            this.loggerService.error(this.context, 'Failed to create expense list', error);
            throw new Error('Error creating expense list: ' + error.message);
        }
    }


    async update(model): Promise<IDebtExpenseListDocument> {
        this.loggerService.log(this.context, 'Update Expense list at query side');
        
        try {
            const result = await this.readModel.updateOne({ id: model.id }, model)
            return result;
        } catch (error) {
            this.loggerService.error(this.context, 'Failed to update expense list', error);
            throw new Error('Error updating expense list: ' + error.message);
        }
    }

    async delete(model): Promise<any> {
        this.loggerService.log(this.context, 'Delete Expense list at query side');
        return await this.readModel.deleteOne({ id: model.id })
            .then(result => result);
    }

}
