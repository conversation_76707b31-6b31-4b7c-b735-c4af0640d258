import { Injectable, BadRequestException } from "@nestjs/common";
import { CommandBus } from "@nestjs/cqrs";
import { CreateHandoverCommand } from "./commands/impl/create-handover.cmd";
import { UpdateHandoverCommand } from "./commands/impl/update-handover.cmd";
import { DeleteHandoverCommand } from "./commands/impl/delete-handover.cmd";
import { HandoverQueryRepository } from "../handover.queryside/repository/handover.query.repository";
import { isNullOrUndefined } from "util";
import { CreateHandoverDto, UpdateHandoverDto } from "./dto/handover.dto";
import { MsxLoggerService } from "../logger/logger.service";
import { CmdPatternConst, CommonConst } from "../shared/constant";
import { CodeGenerateService } from "../code-generate/service";
import { Action } from "../shared/enum/action.enum";
import { ErrorConst } from "../shared/constant/error.const";
import { HandoverScheduleQueryRepository } from "../handover-schedule.queryside/repository/handover-schedule.query.repository";
import { HandoverConst } from "../shared/constant/handover.const";
import { PrimaryContractQueryRepository } from "../primary-contract.queryside/repository/primary-contract-query.repository";
import { HandoverStatusEnum } from "../shared/enum/status.enum";
import { PropertyClient } from "../mgs-sender/property.client";
import { StatusEnum } from "../shared/enum/primary-contract.enum";
import { BaseService, ErrorService, CmdPatternConst as CmdPatternConstNew } from "../../../shared-modules";
const uuid = require("uuid");
const clc = require("cli-color");

@Injectable()
export class HandoverDomainService extends BaseService {
  private readonly context = HandoverDomainService.name;
  private commandId: string;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly loggerService: MsxLoggerService, // private readonly codeGenerateService: CodeGenerateService,
    private readonly queryRepository: HandoverQueryRepository,
    private readonly handoverScheduleQueryRepository: HandoverScheduleQueryRepository,
    private readonly primaryContractQueryRepository: PrimaryContractQueryRepository,
    private readonly propertyClient: PropertyClient,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }

  async createHandover(user: any, dto: CreateHandoverDto, actionName: string) {
    this.loggerService.log(this.context, clc.green("create service"));
    const model: any = { ...dto };
    if (model.id) {
      delete model.id;
    }

    if (model.status === StatusEnum.ACTIVE) {
      const handover = await this.queryRepository.findOne({ "project.id": model.project.id, status: StatusEnum.ACTIVE, softDelete: false });
      if (handover) {
        return this.getResponse("HANDOVER0001");
      }
    }
    const errorCode = this.validateHandoverCommon(dto);
    if (errorCode) {
      return this.getResponse(errorCode);
    }

    this.commandId = uuid.v4();
    model.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    model.createdBy =
    {
      id: user.id,
      name: user.fullName,
      username: user.username,
    };
    model.createdDate = new Date();

    const projects = await this.propertyClient.sendDataPromise(
      {
        query: { id: model.project.id },
        fields: { id: 1, imageUrl: 1, code: 1, name: 1, type: 1 }
      },
      CmdPatternConstNew.PROJECT.GET_PROJECT_DROPDOWN_LIST
    );

    if (Array.isArray(projects) && projects.length > 0) {
      model.project = projects[0];
    }
    else {
      return this.getResponse("HANDOVER0007"); // Ví dụ mã lỗi mới
    }

    await this.executeCommand(Action.CREATE, 'create', this.commandId, model);
    return this.getResponse(0, { id: model.id });
  }

  async updateHandover(user: any, dto: UpdateHandoverDto, actionName: string) {
    this.loggerService.log(this.context, clc.green("update service"));

    const model: any = { ...dto };
    const handoverOld: any = await this.queryRepository.findOne({ id: dto.id, softDelete: false });
    // const handoverOld: any = await this.queryRepository.findOne({ id: dto.id, softDelete: false, 'createdBy.id': user.id });
    if (!handoverOld) {
      return this.getResponse("HANDOVER0004");
    }
    if (model.status === StatusEnum.ACTIVE) {
      const handover = await this.queryRepository.findOne({ "project.id": model.project.id, status: StatusEnum.ACTIVE, id: { $ne: model.id }, softDelete: false });
      if (handover) {
        return this.getResponse("HANDOVER0001");
      }
    }

    const errorCode = this.validateHandoverCommon(dto);
    if (errorCode) {
      return this.getResponse(errorCode);
    }

    this.commandId = uuid.v4();
    model.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    model.updatedDate = new Date();
    model.createdBy = handoverOld.createdBy;
    model.createdDate = handoverOld.createdDate;
    model.project = handoverOld.project;
    await this.executeCommand(Action.UPDATE, 'update', this.commandId, model);

    // Nếu thay đổi Đơn vị thực hiện
    let filterOrgchart = handoverOld.orgCharts.filter(x => !dto.orgCharts.map(i => i.id).includes(x.id));
    if (filterOrgchart && filterOrgchart.length > 0) {
      let handoverQuery = { deliveryId: handoverOld.id, "supportEmployee.pos.id": { $in: filterOrgchart.map(x => x.id) } }; // query xóa lịch

      // Hợp đồng bị thay đổi lich
      let listSchedule = await this.handoverScheduleQueryRepository.find(handoverQuery);
      let listContractId = listSchedule.map(x => x.handoverApartment.id);

      let contractQuery = { id: { $in: listContractId }, handoverStatus: HandoverStatusEnum.scheduled }; // query cập nhật Contract
      await this.deleteChedule(handoverQuery, contractQuery, listContractId);
    }

    // Nếu thay đổi % thanh toán yêu cầu
    if (handoverOld.paymentPercent !== dto.paymentPercent) {
      let query = {
        deliveryId: handoverOld.id,
        _fields: 'id'
      }
      let listSchedule = await this.handoverScheduleQueryRepository.findAll(query);
      // Hợp đồng bị nhỏ hơn % thanh toán
      let listContract = await this.primaryContractQueryRepository.find({ id: { $in: listSchedule.map(x => x.id) }, paymentPercent: { $lt: dto.paymentPercent } });
      let listContractId = listContract.map(x => x.id);
      let contractQuery = { id: { $in: listContractId }, handoverStatus: HandoverStatusEnum.scheduled }; // query cập nhật Contract
      let handoverQuery = { 'handoverApartment.id': { $in: listContractId } }; // query xóa lịch

      await this.deleteChedule(handoverQuery, contractQuery, listContractId);
    }

    // Thay đổi khung giờ
    await this.changeTimeFrame(handoverOld, dto, 'sundayFrame', 1);
    await this.changeTimeFrame(handoverOld, dto, 'mondayFrame', 2);
    await this.changeTimeFrame(handoverOld, dto, 'tuesdayFrame', 3);
    await this.changeTimeFrame(handoverOld, dto, 'wednesdayFrame', 4);
    await this.changeTimeFrame(handoverOld, dto, 'thursdayFrame', 5);
    await this.changeTimeFrame(handoverOld, dto, 'fridayFrame', 6);
    await this.changeTimeFrame(handoverOld, dto, 'saturdayFrame', 7);

    return this.getResponse(0, { id: model.id });
  }

  async deleteChedule(handoverQuery, contractQuery, listContractId) {
    // Danh sách hợp đồng bị xóa lịch
    let listContract = await this.primaryContractQueryRepository.find(contractQuery);
    if (listContract && listContract.length > 0) {
      let updatePropery = {
        query: { id: { $in: listContract.map(x => x.primaryTransaction.propertyUnit.id) } },
        model: { $set: { handoverSchedule: null } }
      }
      // Cập nhật sản phẩm => là ko còn lịch nữa
      await this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
    }

    await this.primaryContractQueryRepository.updateMany(contractQuery, { $set: { handoverStatus: HandoverStatusEnum.init, handoverSchedule: null } }); // cập nhật hợp đồng init
    await this.handoverScheduleQueryRepository.deleteByQuery(handoverQuery); // xóa lịch hẹn

    // Xóa lịch bên property unit
    let updatePropery = {
      query: { 'handoverSchedule.id': { $in: listContractId } },
      model: { $set: { handoverSchedule: null } }
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_MANY_PROPERTY_UNIT_BY_QUERY);
  }
  async changeTimeFrame(handoverOld, dto, dayOfWeek, numberDayOfWeek) {
    const timezone = "Asia/Ho_Chi_Minh";
    let timeFrames: any[] = [];
    let filterTimeFrame: any[] = [];

    if (handoverOld[dayOfWeek] && handoverOld[dayOfWeek].length > 0 && (!dto[dayOfWeek] || dto[dayOfWeek].length === 0)) {
      // Ngày trong tuần bị đổi
      filterTimeFrame = handoverOld[dayOfWeek];
    } else {
      timeFrames = handoverOld[dayOfWeek]
      handoverOld[dayOfWeek].forEach(x => {
        let check = dto[dayOfWeek].find(y => {
          return x.startTime === y.startTime && x.endTime === y.endTime;
        });
        if (check) {
          timeFrames = timeFrames.filter(o => !(x.startTime === o.startTime && x.endTime === o.endTime));
        }
      });
      if (timeFrames && timeFrames.length > 0) {
        // Khung giờ bị thay đổi
        filterTimeFrame = timeFrames;
      } else {
        // Amount bị thay đổi
        filterTimeFrame = handoverOld[dayOfWeek].filter(x => {
          return dto[dayOfWeek].find(y => {
            return x.startTime === y.startTime && x.endTime === y.endTime && x.amount !== y.amount;
          });
        });
      }
    }

    if (filterTimeFrame && filterTimeFrame.length > 0) {
      for (let timeFrame of filterTimeFrame) {
        let aggegate: any[] = [];
        aggegate.push({
          $addFields:
          {
            dow: {
              $dayOfWeek: { date: '$handoverStartTime', timezone } // Ngày trong tuần
            },
            startTime: { $dateToString: { format: "%H:%M", date: "$handoverStartTime", timezone } }, // Khung giờ
            endTime: { $dateToString: { format: "%H:%M", date: "$handoverEndTime", timezone } }, // Khung giờ
          }
        });
        aggegate.push({
          $match:
          {
            dow: numberDayOfWeek,
            startTime: timeFrame.startTime,
            endTime: timeFrame.endTime
          }
        });

        // Danh sách lịch bị ảnh hưởng
        let listSchedule = await this.handoverScheduleQueryRepository.findByAggregate(aggegate);

        // Hợp đồng bị thay đổi lich
        let listContractId = listSchedule.map(x => x.handoverApartment.id);

        let contractQuery = { id: { $in: listContractId }, handoverStatus: HandoverStatusEnum.scheduled }; // query cập nhật contract
        let handoverQuery = { id: { $in: listSchedule.map(x => x.id) } }; // query xóa lịch
        await this.deleteChedule(handoverQuery, contractQuery, listContractId);
      };
    }
  }

  async deleteHandover(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green("delete service"));
    const handoverOld: any = await this.queryRepository.findOne({ id: dto.id, softDelete: false });
    // const handoverOld: any = await this.queryRepository.findOne({ id: dto.id, softDelete: false, 'createdBy.id': user.id });
    if (!handoverOld) {
      return this.getResponse("HANDOVER0004");
    }
    this.commandId = uuid.v4();
    handoverOld.softDelete = true;
    handoverOld.reasonDelete = dto.reasonDelete;
    handoverOld.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    handoverOld.updatedDate = new Date();
    await await this.executeCommand(
      Action.UPDATE,
      'update',
      this.commandId,
      handoverOld
    );
    return this.getResponse(0, { id: handoverOld.id });
  }
  async changeStatusHandover(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green("changeStatus service"));
    // const handoverOld: any = await this.queryRepository.findOne({ id: dto.id, softDelete: false, 'createdBy.id': user.id });
    const handoverOld: any = await this.queryRepository.findOne({ id: dto.id, softDelete: false });
    if (!handoverOld) {
      return this.getResponse("HANDOVER0004");
    }

    if (handoverOld.status === StatusEnum.INACTIVE) {
      const handoverExist = await this.queryRepository.findOne({ "project.id": handoverOld.project.id, status: StatusEnum.ACTIVE, id: { $ne: handoverOld.id }, softDelete: false });
      if (handoverExist) {
        return this.getResponse("HANDOVER0001");
      }
    }
    this.commandId = uuid.v4();
    function toggleStatus(currentStatus: StatusEnum): StatusEnum {
      return currentStatus === StatusEnum.ACTIVE ? StatusEnum.INACTIVE : StatusEnum.ACTIVE;
    }

    // Gọi:
    handoverOld.status = toggleStatus(handoverOld.status);

    handoverOld.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    handoverOld.updatedDate = new Date();
    await await this.executeCommand(
      Action.UPDATE,
      'update',
      this.commandId,
      handoverOld
    );
    return this.getResponse(0, { id: handoverOld.id });
  }
  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreateHandoverCommand(actionName, commandId, item);
        break;
      case Action.UPDATE:
        commandObject = new UpdateHandoverCommand(actionName, commandId, item);
        break;
      case Action.DELETE:
        commandObject = new DeleteHandoverCommand(actionName, commandId, item);
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  private validateHandoverCommon(dto: any) {
    // Validate ngày
    dto.expectedStartDate = new Date(dto.expectedStartDate);
    dto.expectedEndDate = new Date(dto.expectedEndDate);
    if (isNaN(dto.expectedStartDate.getTime()) || isNaN(dto.expectedEndDate.getTime())) {
      return "HANDOVER0005";
    }

    // Validate hotline
    if (!/^\d+$/.test(dto.hotline)) {
      return "HANDOVER0006";
    }

    // Validate khung giờ có tồn tại
    const FRAME_DAYS = [
      "mondayFrame",
      "tuesdayFrame",
      "wednesdayFrame",
      "thursdayFrame",
      "fridayFrame",
      "saturdayFrame",
      "sundayFrame"
    ];

    const hasAnyFrame = FRAME_DAYS.some(day => dto[day]?.length > 0);
    if (!hasAnyFrame) {
      return "HANDOVER0002";
    }

    // Validate không overlap khung giờ
    for (const dayKey of FRAME_DAYS) {
      const frames = dto[dayKey];
      if (frames?.length > 1 && !this.validateFrame(frames)) {
        return "HANDOVER0009";
      }
    }

    // Validate items
    if (dto.items?.some(item => item.name == null)) {
      return "HANDOVER0003";
    }

    // Validate orgCharts
    if (dto.orgCharts?.some(item => item.name == null)) {
      return "HANDOVER0008";
    }

    return null; // OK
  }
  
  validateFrame(frame) {
    // Chuyển thời gian HH:mm sang phút
    const toMinutes = (time) => {
      const [h, m] = time.split(':').map(Number);
      return h * 60 + m;
    };

    // Sắp xếp theo startTime để đảm bảo thứ tự
    const frames = [...frame].sort(
      (a, b) => toMinutes(a.startTime) - toMinutes(b.startTime)
    );

    for (let i = 0; i < frames.length - 1; i++) {
      const currentEnd = toMinutes(frames[i].endTime);
      const nextStart = toMinutes(frames[i + 1].startTime);

      if (currentEnd >= nextStart) {
        return false
      }
    }
    return true
  }
}
