import { Injectable, NotFoundException } from "@nestjs/common";
import {
  CreateDebtCommissionDto,
  DeleteDebtCommissionDto,
} from "./dto/debt-commission.dto";
import { CodeGenerateService } from "../code-generate/service";
import { StsClient } from "../mgs-sender/sts.client";
import {
  AwesomeLogger,
  BaseService,
  ErrorService,
  CmdPatternConst,
  AccountInfoUtils,
} from "../../../shared-modules";
import { EmployeeClient } from "../mgs-sender/employee.client";
import { PropertyClient } from "../mgs-sender/property.client";
import * as _ from "lodash";
import { DebtCommissionRepository } from "./repository/repository";
import { DebtCommissionListRepository } from "../debt-commission-list/repository/debt-commission-list.repository";
import { ActiveEnum, DebtCommissionStatus } from "../shared/enum/commission.enum";
import { pushFileS3, ReAssignAccountInforForSingle } from "../shared/utils/transform";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { checkPermission } from "../shared/utils/checkPermission";
import { DebtCommissionPolicyQueryRepository } from "../debt-commission-policy/repository/repository";

const uuid = require("uuid");
const clc = require("cli-color");

@Injectable()
export class DebtCommissionService extends BaseService {

  private commandId: string;
  private readonly logger = new AwesomeLogger(DebtCommissionService.name);

  constructor(
    private readonly queryRepository: DebtCommissionRepository,
    private readonly commListRepository: DebtCommissionListRepository,
    private readonly comRepo: DebtCommissionRepository,
    private readonly commissionPolicyRepo: DebtCommissionPolicyQueryRepository,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly empClient: EmployeeClient,
    private readonly propertyClient: PropertyClient,
    private readonly stsClient: StsClient,
    public readonly errorSvc: ErrorService
  ) {
    super(errorSvc);
  }

  async findAll(user: any, query: any) {
    const { type, search, periodName, posName, startDate, endDate, createdBy = '' } = query;

    const dataQuery: any = { isActive: ActiveEnum.ACTIVE, softDelete: false };

    const page: number = parseInt(query["page"]) || 1;
    const pageSize: number = parseInt(query["pageSize"]) || 10;

    // Check condition type
    if (!_.isEmpty(type)) {
      dataQuery.type = type;
    }
    if (!_.isEmpty(periodName)) {
      dataQuery.name = periodName;
    }
    if (!_.isEmpty(search)) {
      dataQuery.$or = [
        { code: { $regex: new RegExp(search), $options: 'i' } },
      ];
    }
    if (!_.isEmpty(posName)) {
      dataQuery['pos.name'] = posName;
    }
    if (!_.isEmpty(createdBy)) {
      dataQuery.createdBy = { $in: createdBy.split(',') };
    }

    let sDate = 0;
    let eDate = 0;
    if (startDate && endDate) {
      sDate = Number(new Date(startDate));
      eDate = Number(new Date(endDate)) + ********;
      dataQuery['createdDate'] = { $gte: new Date(sDate), $lte: new Date(eDate) };
    } else if (startDate) {
      sDate = Number(new Date(startDate));
      dataQuery['createdDate'] = { $gte: new Date(sDate) };
    } else if (endDate) {
      eDate = Number(new Date(endDate)) + ********;
      dataQuery['createdDate'] = { $lte: new Date(eDate) };
    }

    const result = await this.queryRepository.findAll(page, pageSize, dataQuery);

    let clones = JSON.parse(JSON.stringify(result));

    clones.rows = await AccountInfoUtils.mapAccountInfo(result?.rows, this.stsClient, {
      includeCreatedBy: true,
      includeModifiedBy: true,
      replaceIds: true
    });

    return clones;
  }

  async findById(id) {
    return await this.queryRepository.findDebtCommissionById(id);
  }

  async findDebtCommissionById(user, id, _query?: any) {
    const commission = await this.queryRepository.findDebtCommissionById(id);
    if (commission.createdBy != user.id) {
      const hasPublish = checkPermission(user.roles, PermissionEnum.DEBT_COMMISSION_GET_PUBLISH_BY_ID);
      if (hasPublish && commission.isPublish) {
        commission.adjustmentVersions = commission.adjustmentVersions.filter(
          (x) => x.status === DebtCommissionStatus.CONFIRMED,
        );
      } else {
        commission.adjustmentVersions = [];
      }
    }

    const newCommission = await ReAssignAccountInforForSingle(commission, this.stsClient);

    newCommission.adjustmentVersions = await AccountInfoUtils.mapAccountInfo(newCommission?.adjustmentVersions, this.stsClient, {
      includeCreatedBy: false,
      includeUploadBy: true,
      replaceIds: true
    });

    // get all tx
    const transactions = await this.commListRepository.find({ 'commission.id': id });
    if (!transactions) {
      return this.getResponse('COME0002');
    }

    return { ...newCommission, transactions };
  }

  /**
   * Tạo commission => thực hiện tính phí + lưu lại kết quả
   * @param user
   * @param dto
   * @param actionName
   */
  async createDebtCommission(user, dto: CreateDebtCommissionDto, actionName: string) {
    this.logger.info("create service");
    this.commandId = uuid.v4();
    delete dto.id;
    dto.modifiedBy = user.id;
    dto.createdBy = user.id;
    // Default trạng thái commission sau khi tính phí
    if (!dto.status) {
      dto.status = DebtCommissionStatus.CREATED;
    }

    // 2025-01-26T00:00:00.000Z
    if (dto.periodFrom && dto.periodTo) {
      const from = new Date(dto.periodFrom);
      const to = new Date(dto.periodTo);
      const formattedFromDate = `${from.getDate().toString().padStart(2, '0')}/${(from.getMonth() + 1).toString().padStart(2, '0')}/${from.getFullYear()}`;
      const formattedEndDate = `${to.getDate().toString().padStart(2, '0')}/${(to.getMonth() + 1).toString().padStart(2, '0')}/${to.getFullYear()}`;
      dto.period = formattedFromDate + " → " + formattedEndDate;
      dto.name = dto.periodName;
    } else {
      return this.getResponse("COMME0007");
    }

    // get commission policy
    if (dto.commissionPolicyCode) {
      const commissionPolicy = await this.commissionPolicyRepo.findOne({ code: dto.commissionPolicyCode });
      if (!commissionPolicy) {
        return this.getResponse('COMME0013');
      }
      dto.commissionPolicy = commissionPolicy;
    }

    this.mergeAdjustmentVersions(dto);
    dto.code = await this.codeGenerateService.generateCodeCommission(user);
    if (_.isEmpty(dto.code)) {
      return this.getResponse("COME0011");
    }
    await this.comRepo.create(dto);
    return this.getResponse(0, { id: dto.id });
  }

  async updateDebtCommission(user, dto: any, actionName: string) {
    this.logger.info(clc.blue("update service"));
    if (dto.id === "") {
      return this.getResponse('COME0002');
    }
    const commission = await this.queryRepository.findOne({ id: dto.id });
    if (!commission) {
      return this.getResponse('COMME0001');
    }

    const cannotDelete = commission.adjustmentVersions.find(
      (x) =>
        x.status != null &&
        x.status != DebtCommissionStatus.NEW &&
        x.status != DebtCommissionStatus.REJECTED
    );
    if (
      cannotDelete &&
      dto.adjustmentVersions.some((x) => x.id === cannotDelete.id)
    ) {
      return this.getResponse("FEE0001");
    }

    if (user.id != commission.createdBy) {
      return this.getResponse("FEE0003");
    }

    this.mergeAdjustmentVersions(dto, true);
    dto.modifiedBy = user.id;
    dto.createdBy = user.id;
    this.commandId = uuid.v4();
    await this.comRepo.update(dto);
    return this.getResponse(0, { id: dto.id });
  }

  async updateStatusAdjustmentVersion(user, dto) {
    this.logger.info("update service");
    try {
      const id = dto.id;
      const commission = await this.queryRepository.findOne({ id });
      if (!commission) {
        return this.getResponse('COMME0001');
      }

      // case change version to CONFIRMED
      if (dto.status == DebtCommissionStatus.CONFIRMED && commission.adjustmentVersions?.some((version) => version.status === DebtCommissionStatus.CONFIRMED)) {
        return this.getResponse("PRIMARYCONTRACT0029");
      }

      // case cancel CONFIRMED version
      if (dto.status == DebtCommissionStatus.CREATED) {
        const version = commission.adjustmentVersions?.filter((version) => version.id == dto.adjustmentVersionId);
        console.log("version", version);
        if (version?.length == 0 || version[0].status != DebtCommissionStatus.CONFIRMED) {
          return this.getResponse("PRIMARYCONTRACT0030");
        }
      }

      commission.adjustmentVersions.forEach((version) => {
        if (version.id === dto.adjustmentVersionId) {
          version.status = dto.status;
          version.uploadDate = new Date();
          version.uploadBy = user.id;
        }
      });

      this.commandId = uuid.v4();
      await this.comRepo.update(commission);
      return this.getResponse(0);
    } catch (error) {
      this.logger.error("Error update status commission: ", error);
      return this.getResponse("COME0001");
    }
  }

  async publishCommission(user, id) {
    this.logger.info(clc.blue("publish commission"));
    if (id === "") {
      return this.getResponse("COMME0002");
    }
    const commission = await this.queryRepository.findOne({ id });
    if (!commission) {
      return this.getResponse("COMME0001");
    }
    commission.isPublish = true;
    this.commandId = uuid.v4();
    this.commListRepository.updateMany(
      { "commission.id": id },
      { isPublish: true }
    );

    // add all staffs of the same org to publishTo list
    const staffIds = await this.empClient.sendDataPromise(
      { id: user.id },
      CmdPatternConst.EMPLOYEE.GET_ALL_STAFFS
    );
    commission["publishTo"] = staffIds;
    console.log("commission", commission);
    await this.comRepo.update(commission);
    return this.getResponse(0);
  }

  async deleteDebtCommission(
    user,
    id: string,
    dto: DeleteDebtCommissionDto,
    actionName: string
  ) {
    const { softDeleteReason = "" } = dto;

    this.logger.info("delete service");
    if (id === "") {
      return this.getResponse("COMME0002");
    }

    const commission = await this.queryRepository.findOne({ id });
    if (!commission) {
      return this.getResponse("COMME0001");
    }
    if (commission.isPublish) {
      return this.getResponse("COMME0008");
    }

    if (
      !commission.adjustmentVersions?.every(
        (x) => x.status === null || x.status === DebtCommissionStatus.CREATED
      )
    ) {
      return this.getResponse("FEE0002");
    }

    commission.id = id;
    commission.softDelete = true;
    commission.softDeleteReason = softDeleteReason;

    // delete commission
    await this.commListRepository.deleteByCommissionId(id);
    this.commandId = uuid.v4();
    await this.comRepo.update(commission);
    return this.getResponse(0);
  }

  async syncCommissionWorkflow(dto: any) {
    this.logger.info("update service");
    if (dto.id === "") {
      return null;
    }
    const commission = await this.queryRepository.findOne({ id: dto.id });
    if (!commission) {
      return null;
    }
    commission.workflows = dto.workflows;
    commission.locked = dto.locked;
    commission.workflowStatus = dto.workflowStatus;
    this.commandId = uuid.v4();
    await this.comRepo.update(commission);
    return this.getResponse(0, { id: dto.id });
  }

  async uploadFile(file: any): Promise<any> {
    try {
      const response = await pushFileS3(file);
      return response;
    } catch (error) {
      return {};
    }
  }

  private mergeAdjustmentVersions(commission: any, isUpdate: boolean = false) {
    let i: number = 1;
    if (
      commission.adjustmentVersions == null ||
      commission.adjustmentVersions == undefined
    )
      return;

    for (const x of commission.adjustmentVersions) {
      let currentId = uuid.v4();
      x.version = `v.${i}`;
      x.status = x.status ?? DebtCommissionStatus.CREATED;
      x.id = isUpdate ? x.id : currentId;
      x.uploadDate = new Date();
      x.uploadBy = commission.userId;
      x.commissionId = commission.id;
      i++;
    }
  }

  async getOneProject(id: string) {
    const project = await this.propertyClient.sendDataPromise({ id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID);
    if (!project)
      throw new NotFoundException('project not found');
    return {
      id: project.id,
      name: project.name,
    };
  }

  async getDropdownProjects(queryParams: any) {
    const { page = 1, pageSize = 10, search = '' } = queryParams;
    const limit = parseInt(pageSize, 10) || 10;
    const skip = (parseInt(page, 10) - 1) * limit;

    const projects = await this.propertyClient.sendDataPromise(
      {
        query: {},
        fields: { _id: 0, id: 1, name: 1 }
      },
      CmdPatternConst.PROJECT.GET_PROJECT_DROPDOWN_LIST
    );

    if (!projects || !Array.isArray(projects)) {
      return {
        rows: [],
        page: parseInt(page, 10),
        pageSize: limit,
        total: 0,
        totalPages: Math.ceil(0 / limit),
      };
    }

    // Lọc dữ liệu trong mảng
    let filteredData = projects.filter(item => {
      const name = item.name ? item.name.toString().toLowerCase() : "";
      return name.includes(search.toLowerCase());
    });

    // Tính tổng số phần tử sau khi lọc
    const total = filteredData.length;

    // Phân trang
    const rows = filteredData.slice(skip, skip + limit);

    return {
      rows,
      page: parseInt(page, 10),
      pageSize: limit,
      total,
      totalPages: Math.ceil(total / limit),
    };
  }
}
