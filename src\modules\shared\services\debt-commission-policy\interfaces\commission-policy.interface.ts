import { IBaseInterface } from '../../../interfaces/base.interface';

export interface IDebtCommissionPolicy extends IBaseInterface {
    id: string;
    name: string;                   // Tên chính sách
    code: string;                   // <PERSON><PERSON> chính sách
    modifiedDate: Date;                // Ng<PERSON>y cập nhật
    isPublish: boolean;             // Tình trạng
    reasonDelete: string;              // Lý do xóa
    isActive: number;              // Trạng thái
    penalty: IDebtPenaltyConfig;
    projectDebt: IDebtCommissionConfig;
    badDebt: IDebtCommissionConfig;
    periodFrom: Date;
    periodTo: Date;
    period: string;
    year: number;
    periodName: string;
}

export interface IDebtCommissionConfig extends IBaseInterface {
    rate: string;
    isProgressive: boolean;
    isVAT: boolean;
    type: string; // all/base/interest
    listRate: IDebtCommissionRate[];    // Tỷ lệ hoa hồng
}

export interface IDebtPenaltyConfig extends IBaseInterface {
    debtage: { name: string };
    amount: number;
    unit: string; // VNĐ / %
    type: string; // all/base/interest
}

export interface IDebtCommissionRate extends IBaseInterface {
    topPrice: number;                               // Giá trị cận trên
    bottomPrice: number;                            // Giá trị cận dưới
    unit: string;                                   // Đơn vị tính VNĐ / %
    recordedCommission: number;                     // Thù lao ghi nhận
    recordedCommissionUnit: string;                 // Đơn vị tính VNĐ / %
}
