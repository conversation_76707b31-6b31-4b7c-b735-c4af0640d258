
import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
const fs = Bluebird.promisifyAll(require('fs'));
import * as path from 'path';
import { isNullOrUndefined } from 'util';
import { StaticAssetService } from '../config/static-asset.service';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { NumberToMoneyVN } from '../shared/classes/number-to-money-vn';
const libre = require('libreoffice-convert');
const XlsxTemplate = require('xlsx-template');
const _ = require("lodash");
const moment = require('moment');

@Injectable()
export class FileGenerationService {
  private readonly context = FileGenerationService.name;
  constructor(
    private readonly staticAssetService: StaticAssetService,
    private readonly orgchartClient: OrgchartClient
  ) { }

  async exportTransaction(file, data, query) {

    let expenseList = [];

    data.expenseList.map((value, index) => {
      const _value: any = value;
      _value.escrowDate = _value.escrowDate ? moment(_value.escrowDate).format('DD/MM/YYYY') : "";
      _value.submitDate = _value.submitDate ? moment(_value.submitDate).format('DD/MM/YYYY') : "";
      _value.stt = index + 1;
      expenseList.push({
        ..._value
      });
    });

    const total = {
      propertyUnitNvatPrice : this.calculateTotal(expenseList, 'propertyUnit.nvatPrice') || 0,
      transactionConsultingFee : this.calculateTotal(expenseList, 'transaction.consultingFee') || 0,
      propertyUnitPrice : this.calculateTotal(expenseList, 'propertyUnit.price') || 0,
      employeeRevenueRate : this.calculateTotal(expenseList, 'employee.revenueRate') || 0,
      transactionRegisterFee : this.calculateTotal(expenseList, 'transaction.registerFee') || 0,
      transactionOtherFee : this.calculateTotal(expenseList, 'transaction.otherFee') || 0,
      transactionTotal : this.calculateTotal(expenseList, 'transaction.total') || 0,
      transactionReceived : this.calculateTotal(expenseList, 'transaction.received') || 0,
      transactionAdvance : this.calculateTotal(expenseList, 'transaction.advance') || 0,
      transactionResidual : this.calculateTotal(expenseList, 'transaction.residual') || 0
    };

    // Create a template
    const templateFile = await fs.readFileAsync(this.staticAssetService.getTemplateFileDownload('Template_TPHH.xlsx'), 'binary');
    var template = new XlsxTemplate(templateFile);
    // Replacements take place on first sheet
    var sheetNumber = 1;
    // Set up some placeholder values matching the placeholders in the template
    var pos: any;
    if (query.posId) {
      pos = await this.orgchartClient.getPosById(query.posId);
    }
    let posName: string = '';
    let posAddress: string = '';
    let posTaxNumber: string = '';

    if(pos){
      posName = pos.name;
      posAddress = pos.contactAddress;
      posTaxNumber = pos.taxNumber;
    }
    let periodMonth: string = moment(query.period, 'YYYY/MM').format('MM/YYYY');
    let periodDayFrom: string = '';
    let periodDayTo: string = '';
    if(query.period){
      let periodMonth = moment(query.period, 'YYYY/MM').format('MM');
      let periodYear = moment(query.period, 'YYYY/MM').format('YYYY');
      if(Number(periodMonth) == 1){
        let year = Number(periodYear) - 1;
        periodDayFrom = `26/12/${year}`;
        periodDayTo = `25/01/${periodYear}`;
      }else{
        let monthFrom = Number(periodMonth) - 1;
        periodDayFrom = `26/${monthFrom}/${periodYear}`;
        periodDayTo = `25/${periodMonth}/${periodYear}`;
      }
    }
    let totalTextMoney = NumberToMoneyVN.parse(isNullOrUndefined(total.transactionAdvance) ? 0 : total.transactionAdvance);
    var values = {
      unitName: posName ? posName.toString().toUpperCase() : '',
      unitAddress: posAddress,
      unitTaxNumber: posTaxNumber,
      chargePeriod: query.period ? `THÁNG ${periodMonth} (TỪ NGÀY ${periodDayFrom} ĐẾN ${periodDayTo})` : '',
      expenseList,
      total,
      totalTextMoney
    };

    // Perform substitution
    template.substitute(sheetNumber, values);
    // Get binary data
    var nodebuffer = template.generate({ type: 'nodebuffer' });
    try {
      const fileName = `${file}.xlsx`;
      await fs.writeFileAsync(path.resolve(this.staticAssetService.getUploadFolderPath(), fileName), nodebuffer, function (err) {
        if (err) {
          return console.log(err);
        }
        return;
      });
      return;
    }
    catch (error) {
      throw error;
    }
  }

  calculateTotal(arr, field) {
    return arr.reduce((a, b) => {
        return a + _.get(b, field);
    }, 0);
  }

}
