import {
  LiquidationStatusEnum,
  LiquidationTypeEnum,
} from "./../shared/enum/liquidation.enum";
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { CommandBus } from "@nestjs/cqrs";
import { MsxLoggerService } from "../logger/logger.service";
import { CodeGenerateService } from "../code-generate/service";
import { CmdPatternConst, CommonConst } from "../shared/constant";
import { Action } from "../shared/enum/action.enum";
import {
  AssignDebtCollectorDto,
  CreatePrimaryContractDto,
  DeassignDebtCollectorDto,
  HandoverPrimaryContractDto,
  InteractionDto,
  InterestCalculationDto,
  PrimaryContractStatusDto,
  ResendEmailDto,
  SendPrimaryContractDeliveryNotifyDto,
  UpdateInterestCalculationDto,
  UpdateManyPrimaryContract,
  UpdatePrimaryContractDeliveryDateDto,
  UpdatePrimaryContractDto,
  UpdatePrimaryContractFileDto,
  UpdateShowReceiptDto,
} from "./dto/primary-contract.dto";
import { PropertyClient } from "../mgs-sender/property.client";
import { ErrorConst } from "../shared/constant/error.const";
import { PolicyQueryService } from "../policy.queryside/service";
import {
  ActionContractName,
  AssignStatusEnum,
  ContractEnum,
  ContractTypeEnum,
  DebtHistoryStatusEnum,
  DiscountTypeEnum,
  DiscountTypeRealEstateEnum,
  PolicyTypeEnum,
  ScheduleInstallmentEnum,
  StatusContractEnum,
} from "../shared/enum/primary-contract.enum";
import { CommonUtils } from "../shared/classes/class-utils";
import {
  DebtTypeEnum,
  HandoverStatusEnum,
  InterestCalculationStatusEnum,
  ProposalStatusEnum,
  TransactionStatusEnum,
} from "../shared/enum/status.enum";
import moment = require("moment");
const momentTz = require("moment-timezone");
import { NotificationClient } from "../mgs-sender/notification.client";
import { CareClient } from "../mgs-sender/care.client";
import { HandoverQueryService } from "../handover.queryside/service";
import { MailerClient } from "../mgs-sender/mailer.client";
import { LiquidationQueryRepository } from "../liquidation.queryside/repository/liquidation.query.repository";
import { TransferHistoryRepository } from "../transfer-history/repository/transfer-history.query.repository";
import * as _ from "lodash";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { UploadClient } from "../mgs-sender/uploader.client";
import { HistoryImportQueryRepository } from "../import-history.queryside/repository/query.repository";
import { TransactionClient } from "../mgs-sender/transaction.client";
import { SyncErpClient } from "../mgs-sender/syncErp.client";
import { SocialClient } from "../mgs-sender/social.client";
import { HandoverScheduleQueryService } from "../handover-schedule.queryside/service";
import {
  HandoverConst,
  HandoverScheduleActionNameConst,
  HandoverScheduleStatusNameConst,
} from "../shared/constant/handover.const";
import { expiredDateType } from "../shared/enum/policies.enum";
import { OrgchartClient } from "../mgs-sender/orgchart.client";
import { ConfigService } from "@nestjs/config";
import { existsSync, unlinkSync } from "fs";
import { join } from "path";
import { StaticAssetService } from "../config/static-asset.service";
import { IHandoverScheduleDocument } from "../handover-schedule.queryside/interfaces/document.interface";
import { EmployeeClient } from "../mgs-sender/employee.client";
import { CreatePrimaryContractCommand } from "../primary-contract.domain/commands/impl/create-primary-contract.cmd";
import { DeletePrimaryContractCommand } from "../primary-contract.domain/commands/impl/delete-primary-contract.cmd";
import { UpdatePrimaryContractCommand } from "../primary-contract.domain/commands/impl/update-primary-contract.cmd";
import { FileGenerationService } from "../primary-contract.queryside/file-generation.service";
import { VerifyAccountStatusEnum } from "../shared/enum/care-customer.enum";
import { StatushistoryService } from "../statusHistory/application/service";
import { EmployeeQueryRepository } from "../employee.query/repository/query.repository";
import { PrimaryContractRepository } from "./repository/primary-contract-query.repository";
import { HandoverScheduleDomainService } from "../handover-schedule.domain/service";
import { BaseService, ErrorService, CmdPatternConst as cmd } from "../../../shared-modules";
import { StsClient } from "../mgs-sender/sts.client";
import { CustomerClient } from "../mgs-sender/customer.client";
import { notificationPayloadDto } from "../../../shared-modules/core/noti/noti.dto";
import { NotiService } from "../../../shared-modules/core/noti/service";
import * as Bluebird from "bluebird";
import * as path from 'path';
import * as AWS from 'aws-sdk';

const uuid = require("uuid");
const clc = require("cli-color");
const fs = Bluebird.promisifyAll(require("fs"));
const handlebars = require('handlebars');
import * as axios from 'axios';
import { convertToPdf, countWeekendDays, fillDocxTemplateFromBuffer, GenPrefix, getValueFileBuffer } from "../shared/utils/transform";
import { CustomHttpService } from "../../shared-modules/http/http.service";
import { CustomerTypeEnum } from "../proposal/dto/proposal.dto";

@Injectable()
export class PrimaryContractService extends BaseService {
  private readonly context = PrimaryContractService.name;
  private commandId: string;
  private s3: AWS.S3;
  private ONE_DAY = 1000 * 60 * 60 * 24;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly loggerService: MsxLoggerService,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly repository: PrimaryContractRepository,
    private readonly propertyClient: PropertyClient,
    private readonly careClient: CareClient,
    private readonly policyQueryService: PolicyQueryService,
    private readonly notificationClient: NotificationClient,
    private readonly handoverQueryService: HandoverQueryService,
    private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
    private readonly liquidationQueryRepository: LiquidationQueryRepository,
    private readonly mailerClient: MailerClient,
    private readonly transferHistoryRepository: TransferHistoryRepository,
    private readonly historyRepository: HistoryImportQueryRepository,
    private readonly uploadClient: UploadClient,
    private readonly transactionClient: TransactionClient,
    private readonly syncErpClient: SyncErpClient,
    private readonly socialClient: SocialClient,
    private readonly orgchartClient: OrgchartClient,
    private readonly employeeClient: EmployeeClient,
    private readonly stsClient: StsClient,
    private readonly customerClient: CustomerClient,
    private readonly statushistoryService: StatushistoryService,
    private readonly fileGenerationService: FileGenerationService,
    private readonly configService: ConfigService,
    private readonly staticAssetService: StaticAssetService,
    private readonly employeeRepository: EmployeeQueryRepository,
    private readonly handoverScheduleDomainService: HandoverScheduleDomainService,
    private readonly notiService: NotiService,
    public readonly errorService: ErrorService,
    public readonly httpService: CustomHttpService,
  ) {
    super(errorService);
    this.s3 = new AWS.S3({
      accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      region: this.configService.get('AWS_DEFAULT_REGION'),
      endpoint: this.configService.get('AWS_EMDPOINT_URL'),
    });
  }

  public async getContract(
    user,
    query,
    isSeeAll: boolean = false
  ): Promise<any> {
    let res = [];
    let response: any = {};
    let authoredIDs = [];
    let queryEmpIDs = [];
    let isManager = false;
    if (user) {

      //TODO: Lâm đang gọi sang bên property để lấy danh sách dự án của user nhưng phần code bên property lại đang lấy all 
      // dẫn đến api bị chậm vì phải lấy hết dữ liệu project

      // const projects = await this.propertyClient.sendDataPromise(
      //   user,
      //   CmdPatternConst.LISTENER.GET_ALL_PROJECT_BY_USER
      // );

      // query["projectIds"] = (projects || []).map((e) => e._id) || [];



      const employee = await this.employeeRepository.findOne({ id: user.id });
      isManager =
        employee && employee.managerAt && employee.managerAt.toString() !== ""
          ? true
          : false;

      authoredIDs =
        employee && employee["managerAt"] ? employee["staffIds"] : [];
      authoredIDs.push(user.id);
      if (isManager) {
        queryEmpIDs = authoredIDs;
      } else {
        queryEmpIDs =
          employee && employee["staffIds"] ? employee["staffIds"] : [];
        queryEmpIDs.push(user.id);
      }
    } else {
      query["projectIds"] = [];
    }
    query.queryEmpIDs = queryEmpIDs;
    if (!_.isEmpty(query["projectId"])) {
      if (query.projectId.includes(",")) {
        query.projectId = query.projectId.split(",");
      }

      if (Array.isArray(query.projectId)) {
        query["primaryTransaction.project.id"] = { $in: query.projectId };
      } else {
        query["primaryTransaction.project.id"] = query.projectId;
      }
    }
    if (query.type) {
      query["type"] = query.type;
    }

    if (query.isTransferred) {
      query["isTransferred"] = query.isTransferred;
    }

    if (query.searchInLiquidation) {
      query["searchInLiquidation"] = query.searchInLiquidation;
    }

    if (!_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"])) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      res = await this.repository
        .contractFindAll(true, isSeeAll, query, page, pageSize)
        .then((result: any) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          response = {
            page,
            pageSize,
            total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
          return result[0].rows;
        });
    } else {
      res = await this.repository.contractFindAll(false, isSeeAll, query, 0, 0);
    }
    response.rows = res;
    return response;
  }
  async getContractById(user, id: string): Promise<any> {
    let contract = await this.repository
      .findOne({ id })
      .then((res) => res ? res.toObject() : null);

    if (!contract)
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0004" },
        HttpStatus.OK
      );

    contract.hasInterest = false;
    contract.interest = {};
    if (
      contract &&
      contract.status === StatusContractEnum.APPROVED &&
      contract.policyPayment &&
      contract.policyPayment.schedule &&
      contract.policyPayment.schedule.installments
    ) {
      if (contract.primaryTransaction.project.setting) {
        // check xem có chậm thanh toán hay không
        contract.interest = this.getInterestOfContract(contract);
      }
    }

    const project = await this.getProjectById(
      contract.primaryTransaction.project.id
    );
    contract.projectType = project.type;
    const transferHistories = await this.transferHistoryRepository.find({
      "propertyUnit.id": contract.primaryTransaction?.propertyUnit?.id,
    });

    if (contract.syncErpData) {
      contract.statusErp = contract.syncErpData.status;
      contract.statusHistory = await this.statushistoryService.findAll({
        contractid: contract.syncErpData.contractid,
      });

      // Lấy statuscode cuối cùng.
      const syncStatusHistory = contract.statusHistory
        ? contract.statusHistory.find(
          (x) => x.contractid === contract.syncErpData.contractid
        )
        : null;
      const statusCodes = syncStatusHistory
        ? syncStatusHistory.statusHistory
          .filter((x) => !!x.statuscode)
          .map((x) => x.statuscode)
        : [];
      contract.statusCode = statusCodes ? statusCodes.slice(-1)[0] : "";
    }

    contract.transferHistories = transferHistories || [];
    return contract;
  }

  async previewForm(contract: any, headers: any) {
    const backendUrl = this.configService.get("BACKEND_URL");
    let formId: any;
    if (contract.type === ContractEnum.DEPOSIT) formId = "d3a1f7c9-9c34-42b5-98e6-1f84762d9b8c"
    else if (contract.type === ContractEnum.RENT) formId = "1f2d3c44-5b6a-4e9c-91ad-2f731a0ed9f7"
    else if (contract.type === ContractEnum.TRANSFER) formId = "ae6b9d1e-7c2b-4f3c-9338-8c4e63fa4c12"
    const fileInfo: any = await this.httpService.getAsync(
      `${backendUrl}/msx-property/api/v2/project/${contract.primaryTransaction.project.id}/getDetailProjectForms/${formId}`,
      {
        headers: {
          Authorization: headers.authorization,
          'Content-Type': 'application/json',
        },
      },
    )
    if (!fileInfo || !fileInfo.data[0].files)
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0037" },
        HttpStatus.OK
      );

    const fileUrl = this.configService.get("S3_URL") + `/${fileInfo.data[0].files[0]?.fileUrl}`;
    const fileBuffer = await getValueFileBuffer(fileUrl);
    const employee = await this.httpService.getAsync(
      `${backendUrl}/msx-employee/api/v2/internal-employee/getInternalEmployeeByAccountLogin`,
      {
        headers: {
          Authorization: headers.authorization,
          'Content-Type': 'application/json',
        },
      },
    )

    const employeeData = employee['data'] || {};
    const level1 = employeeData['level1'] || {};
    const level1Code = level1['code'] || '';
    const logo = `${this.configService.get("S3_URL_DXG")}/dxg/${level1Code}.png`;
    const cusCCCD = contract.primaryTransaction.customer.personalInfo.identities.find(x => x.type === "CCCD");
    const cusAdress = contract.primaryTransaction.customer.info.rootAddress.address
    const data = {
      customerName: contract.primaryTransaction.customer.personalInfo.name,
      customerBirthday: contract.primaryTransaction.customer.info.birthday,
      customerCCCD: cusCCCD.value,
      customerCCCDDate: cusCCCD.date,
      customerCCCDPlace: cusCCCD.place,
      cusAdress: cusAdress,
      customerPhone: contract.primaryTransaction.customer.personalInfo.phone,
      customerEmail: contract.primaryTransaction.customer.personalInfo.email,
      projectName: contract.primaryTransaction.project.name,
      customer: contract.primaryTransaction.customer,
      project: contract.primaryTransaction.project,
      orgchartName: employeeData.orgName,
      city: contract.city,
      property: contract.primaryTransaction.propertyUnit,
      propertyCode: contract.primaryTransaction.propertyUnit.propertyCode,
      propertyFloor: contract.primaryTransaction.propertyUnit.floor,
      carpetArea: contract.primaryTransaction.propertyUnit.insideArea || 0,
      priceVat: new Intl.NumberFormat('vi-VN').format(contract.primaryTransaction.propertyUnit.priceVat),
      bedroom: contract.primaryTransaction.propertyUnit.bedroom,
      direction: contract.primaryTransaction.propertyUnit.direction,
      view2: contract.primaryTransaction.propertyUnit.view2,
      corner: contract.primaryTransaction.propertyUnit.corner,
      area: contract.primaryTransaction.propertyUnit.area,
      outsideArea: contract.primaryTransaction.propertyUnit.outsideArea,
      priceAbove: contract.primaryTransaction.propertyUnit.priceAbove,
      priceAboveVat: contract.primaryTransaction.propertyUnit.priceAboveVat,
      price: contract.primaryTransaction.propertyUnit.price,
      housePriceVat: contract.primaryTransaction.propertyUnit.housePriceVat,
      landPriceVat: contract.primaryTransaction.propertyUnit.landPriceVat,
      housePrice: contract.primaryTransaction.propertyUnit.housePrice,
      landPrice: contract.primaryTransaction.propertyUnit.landPrice,
      day: moment().format('DD'),
      month: moment().format('MM'),
      year: moment().format('YYYY'),
      dear: contract.dear,
      body: contract.body,
      logo: logo,
    }

    const outputBuffer = await fillDocxTemplateFromBuffer(fileBuffer, data);

    const outputDir = path.join(__dirname, '../../output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    const outputPath = path.join(outputDir, `template-${Date.now()}.docx`);

    fs.writeFileSync(outputPath, outputBuffer);
    const filePDF = await convertToPdf(outputPath, "link", this.configService, this.s3, "contract", contract.name)
    return filePDF;
  }

  async listInterestCalculations(user, id: string, query): Promise<any> {
    const contract = await this.getContractById(user, id);
    const interestCalculations = contract.interestCalculations;
    let result = interestCalculations;
    if (query.search) {
      result = interestCalculations.filter(item => {
        const title = item.title?.toString().toLowerCase() || "";
        const code = item.code?.toString().toLowerCase() || "";
        return title.includes(query.search.toLowerCase()) || code.includes(query.search.toLowerCase());
      });
    }
    if (query.status) {
      result = result.filter(x => x.status === query.status)
    }
    if (query.installmentName) {
      result = result.filter(x => x.installmentName === query.installmentName)
    }

    // Phân trang
    const page = parseInt(query.page) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const total = result.length;
    const totalPages = Math.ceil(total / pageSize);
    const paginatedResult = result.slice(startIndex, endIndex);

    return {
      total: result.length,
      page,
      pageSize,
      rows: paginatedResult,
      totalPages: totalPages
    };
  }

  async getInterest(contractId, query): Promise<any> {
    const contract = await this.repository
      .findOne({ id: contractId })

    if (!contract)
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0004" },
        HttpStatus.OK
      );

    if (query.code)
      return contract.interestCalculations.find(x => x.code === query.code);
    else (query.id)
    return contract.interestCalculations.find(x => x.id === query.id);
  }

  async getAllF1Employee(
    f1OrgId: string,
    query: any
  ): Promise<any> {
    const orgchart = await this.orgchartClient.sendDataPromise(
      f1OrgId,
      CmdPatternConst.ORGCHART.GET_BY_ID,
    );

    const project = await this.propertyClient.sendDataPromise(
      { id: query.projectId },
      CmdPatternConst.PROJECT.GET_PROJECT_BY_ID_FPT,
    );

    if (!project) return [];

    let result = [];
    const orgchartF2Ids = project.internalOrgcharts?.find(x => x.id === orgchart.id)?.externalOrgchartIds
    if (orgchartF2Ids) {
      const orgchartF2s = await this.orgchartClient.sendDataPromise(
        { ids: orgchartF2Ids },
        CmdPatternConst.ORGCHART.GET_ALL_ORGCHART_EXTERNAL_BY_IDS,
      );

      const empF2 = await this.orgchartClient.sendDataPromise(
        orgchartF2s.map(x => x.partnershipCode),
        CmdPatternConst.ORGCHART.GET_EXTERNAL_EMPLOYEE_BY_PARTNERSHIPCODES,
      );
      if (empF2)
        result.push(...empF2);
    }

    const empF1 = await this.employeeClient.sendDataPromise(
      { orgCode: orgchart.code, level: orgchart.level, query: query },
      CmdPatternConst.EMPLOYEE.GET_EMPLOYEE_BY_INTERNAL_ORG_CODE
    );
    result.push(...empF1);

    return result;
  }

  async getContractByPropertyUnitId(
    user,
    propertyUnitId: string
  ): Promise<any> {
    return await this.repository.findOne({
      "primaryTransaction.propertyUnit.id": propertyUnitId,
    });
  }

  private getInterestOfContract(contract, query: any = {}) {
    let installments = contract.policyPayment.schedule.installments || [];
    let interestIdx = -1;
    const interestRate =
      contract?.primaryTransaction?.project?.setting?.interestRate || 0;
    const daysPerYear =
      contract?.primaryTransaction?.project?.setting?.daysPerYear || 1;
    const delayDay =
      contract?.primaryTransaction?.project?.setting?.delayDay || 0;
    const onlyWorkday =
      contract?.primaryTransaction?.project?.setting?.onlyWorkday || false;
    let interest: any = {
      title: contract.name, // tiêu đề
      customer: contract.primaryTransaction.customer.personalInfo.name, // khách hàng
      rate: interestRate,
      needTransfer: 0,
      delayFrom: "",
      delayTo: "",
      totalDelayDate: 0,
      amount: 0, // số tiền lãi
    };
    installments.forEach((i, idx) => {
      i.totalTransfered = i.totalTransfered || 0;
      if (interestIdx === -1) {
        // Nếu đợt chưa thanh toán đủ tiền
        if (
          i.paymentDueDate &&
          moment().isAfter(moment(i.paymentDueDate), "day") &&
          i.totalAmount > i.totalTransfered
        ) {
          contract.hasInterest = true;
          interestIdx = idx;

          // số tiền cần thanh toán
          const startDate = query.startDate
            ? moment(query.startDate)
            : moment(i.paymentDueDate).add(1, "days"); // ngày bắt đầu tính lãi
          const endDate = query.endDate ? moment(query.endDate) : moment(); // ngày kết thúc tính lãi

          // tính tổng số ngày đang bị chậm ( đã trừ số ngày cho phép chậm không tính lãi )
          const totalDelayDate = onlyWorkday
            ? CommonUtils.workdayCount(moment(i.paymentDueDate), endDate) - 1
            : CommonUtils.getDateDiffIgnoreTime(
              moment(i.paymentDueDate),
              endDate
            );

          // nếu vượt số ngày không tính lãi, bắt đầu tính lãi
          if (totalDelayDate > delayDay) {
            // tổng số ngày trễ hạn thanh toán tính tới hôm nay
            const interestDate =
              CommonUtils.getDateDiffIgnoreTime(startDate, endDate) + 1;

            interest.name = i.name; // tên đợt
            interest.needTransfer = i.totalAmount - i.totalTransfered; // số tiền gốc
            interest.delayFrom = startDate.format("YYYY-MM-DD"); // tính lãi từ ngày
            interest.delayTo = endDate.format("YYYY-MM-DD"); // tính lãi đến ngày
            interest.totalDelayDate = interestDate; // số ngày trễ hạn thanh toán

            // lấy các phiếu thu ĐÃ THANH TOÁN của đợt nằm trong khoảng cần tính
            const receipts = i.receipts.filter(
              (r) =>
                r.status === TransactionStatusEnum.transfered &&
                moment(r.receiptDate).isSameOrBefore(endDate)
            );

            // nếu có tồn tại phiếu thu, tính lãi của từng mốc
            if (receipts.length > 0) {
              let total = i.totalAmount;
              let dateFrom = moment(interest.delayFrom).clone();
              receipts.forEach((receipt) => {
                // lấy ngày thanh toán phiếu thu
                const receiptDate = moment(receipt.receiptDate);
                if (receiptDate.isSameOrAfter(dateFrom)) {
                  // tính tiền từ mốc trước cho đến mốc phiếu thu sau
                  const totalDays = CommonUtils.getDateDiffIgnoreTime(
                    dateFrom,
                    receiptDate
                  );
                  interest.amount += CommonUtils.getTotalInterest(
                    total,
                    interestRate,
                    daysPerYear,
                    totalDays
                  );
                  dateFrom = moment(receipt.receiptDate).clone();
                }
                // trừ số tiền tổng đi cho mốc sau
                total -= receipt.amount;
              });
              // sau khi không còn phiếu nào, tính tiền từ phiếu thu cuối => ngày kết thúc
              const totalDays =
                CommonUtils.getDateDiffIgnoreTime(dateFrom, endDate) + 1;
              interest.amount += CommonUtils.getTotalInterest(
                total,
                interestRate,
                daysPerYear,
                totalDays
              );
            } else {
              // nếu không tồn tại phiếu thu, tính lãi của tất cả các ngày chậm
              interest.amount = CommonUtils.getTotalInterest(
                i.totalAmount - i.totalTransfered,
                interestRate,
                daysPerYear,
                interestDate
              );
            }
            interest.amount = Math.ceil(interest.amount);
          }
        }
      }
    });
    return interest;
  }

  async calculateInterestById(user, id, query) {
    let contract = await this.repository.findOne({ id });
    contract.interest = {};
    if (
      contract &&
      contract.status === StatusContractEnum.APPROVED &&
      contract.policyPayment &&
      contract.policyPayment.schedule &&
      contract.policyPayment.schedule.installments &&
      contract.primaryTransaction.project.setting
    ) {
      // check xem có chậm thanh toán hay không
      contract.interest = this.getInterestOfContract(contract, query);
    }
    return contract.interest;
  }

  async getProjectById(id: string): Promise<any> {
    return await this.propertyClient.sendDataPromise(
      { id },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
    );
  }
  async getProjectByIds(ids: string[], _fields?: any): Promise<any> {
    return await this.propertyClient.sendDataPromise(
      { ids, _fields },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );
  }
  async getContractByDiscountPolicy(user, id: string): Promise<any> {
    const contracts = await this.repository.findContractByDiscountPolicy(id);
    return {
      id,
      total: contracts.length,
    };
  }

  public async getDepositForPurchaseContract(
    user,
    query,
    isSeeAll: boolean = false
  ): Promise<any> {
    let res = [];
    let response: any = {};
    query.status = StatusContractEnum.APPROVED;
    query.type = ContractEnum.DEPOSIT;
    if (!_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"])) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      res = await this.repository
        .contractFindAll(true, isSeeAll, query, page, pageSize)
        .then((result: any) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          response = {
            page,
            pageSize,
            total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
          return result[0].rows;
        });
    } else {
      res = await this.repository.contractFindAll(false, isSeeAll, query, 0, 0);
    }
    response.rows = res;
    return response;
  }

  async getContractReminder(): Promise<any> {
    const firstReminder = this.configService
      .get("FIRST_DEBT_REMINDER")
      .split(",");
    const otherReminder = this.configService
      .get("DEBT_REMINDED_BEFORE_DATE")
      .split(",");
    const agg = [
      {
        $match: {
          "policyPayment.schedule.installments.0": { $exists: true },
          isDebtRemind: true,
          status: {
            $in: [
              StatusContractEnum.APPROVED,
              StatusContractEnum.ACCOUNTANT_WAITING,
            ],
          },
        },
      },
      {
        $addFields: {
          firstRemindDateArray: {
            $cond: {
              if: {
                $lte: [
                  "$$ROOT.primaryTransaction.project.setting.dateBeforeFirstDebtRemind",
                  [],
                ],
              },
              then: firstReminder.map((i) => parseInt(i)),
              else: "$$ROOT.primaryTransaction.project.setting.dateBeforeFirstDebtRemind",
            },
          },
        },
      },
      {
        $addFields: {
          firstRemindDateArray: {
            $map: {
              input: "$firstRemindDateArray",
              as: "date",
              in: {
                $dateToString: {
                  format: "%Y-%m-%d",
                  date: {
                    $convert: {
                      input: {
                        $add: [
                          new Date(),
                          {
                            $multiply: ["$$date", 86400000],
                          },
                        ],
                      },
                      to: "date",
                    },
                  },
                  timezone: "+07",
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          otherRemindDateArray: {
            $cond: {
              if: {
                $lte: [
                  "$$ROOT.primaryTransaction.project.setting.dateBeforeNextDebtRemind",
                  [],
                ],
              },
              then: otherReminder.map((i) => parseInt(i)),
              else: "$$ROOT.primaryTransaction.project.setting.dateBeforeNextDebtRemind",
            },
          },
        },
      },
      {
        $addFields: {
          otherRemindDateArray: {
            $map: {
              input: "$otherRemindDateArray",
              as: "date",
              in: {
                $dateToString: {
                  format: "%Y-%m-%d",
                  date: {
                    $convert: {
                      input: {
                        $add: [
                          new Date(),
                          {
                            $multiply: ["$$date", 86400000],
                          },
                        ],
                      },
                      to: "date",
                    },
                  },
                  timezone: "+07",
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          installDate: {
            $map: {
              input: "$policyPayment.schedule.installments",
              as: "installment",
              in: {
                $cond: {
                  if: { $lte: ["$$installment.paymentDueDate", null] },
                  then: {
                    $dateToString: {
                      format: "",
                      date: {
                        $convert: {
                          input: new Date(),
                          to: "date",
                        },
                      },
                      timezone: "+07",
                    },
                  },
                  else: {
                    $dateToString: {
                      format: "%Y-%m-%d",
                      date: {
                        $convert: {
                          input: "$$installment.paymentDueDate",
                          to: "date",
                        },
                      },
                      timezone: "+07",
                    },
                  },
                },
              },
            },
          },
        },
      },
      {
        $match: {
          $expr: {
            $or: [
              {
                $gt: [
                  {
                    $size: {
                      $setIntersection: [
                        "$installDate",
                        "$otherRemindDateArray",
                      ],
                    },
                  },
                  0,
                ],
              },
              {
                $gt: [
                  {
                    $size: {
                      $setIntersection: [
                        "$installDate",
                        "$firstRemindDateArray",
                      ],
                    },
                  },
                  0,
                ],
              },
            ],
          },
        },
      },
    ];

    let contracts: any = await this.repository.findPrimaryContractReminder(agg);
    let projectIds = contracts.map((contract: any) => {
      return contract?.primaryTransaction?.project?.id;
    });
    projectIds = [...new Set(projectIds)];
    let _fields: any = {
      id: 1,
      emailTemplate: 1,
      smsTemplate: 1,
    };
    let templates = await this.getProjectByIds(projectIds, _fields);

    contracts = contracts.map((contract: any) => {
      const installments = contract.policyPayment.schedule.installments;
      installments.map(async (item, idx) => {
        const paymentAmount =
          installments[idx]?.totalAmount - installments[idx]?.totalTransfered;
        const isFirst = contract.firstRemindDateArray.includes(
          moment(item.paymentDueDate).format("YYYY-MM-DD")
        );
        const isOther = contract.otherRemindDateArray.includes(
          moment(item.paymentDueDate).format("YYYY-MM-DD")
        );
        if (paymentAmount > 0 && (isFirst || isOther)) {
          if ((idx == 0 && isFirst) || (idx != 0 && isOther)) {
            if (templates.length) {
              let temp = templates.find(
                (p) => p.id === contract?.primaryTransaction?.project?.id
              );
              if (temp) {
                let emailTemplate: any;
                let smsTemplate: any;

                if (idx == 0) {
                  emailTemplate = temp.emailTemplate
                    .reverse()
                    .find((e) => e.type === CommonConst.EMAIL_THANH_TOAN_DOT_1);
                  smsTemplate = temp.smsTemplate
                    .reverse()
                    .find(
                      (e) => e.type === CommonConst.SMS_NHAC_NO_THANH_TOAN_DOT_1
                    );
                } else {
                  emailTemplate = temp.emailTemplate
                    .reverse()
                    .find(
                      (e) =>
                        e.type === CommonConst.EMAIL_THANH_TOAN_DOT_TIEP_THEO
                    );
                  smsTemplate = temp.smsTemplate
                    .reverse()
                    .find(
                      (e) =>
                        e.type ===
                        CommonConst.SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO
                    );
                }

                if (emailTemplate) {
                  const dataEmail = {
                    emailTo:
                      contract?.primaryTransaction?.customer?.personalInfo
                        ?.email,
                    productCode:
                      contract?.primaryTransaction?.propertyUnit?.code,
                    projectName: contract?.primaryTransaction?.project?.name,
                    paymentAmount: paymentAmount.toLocaleString(),
                    date: moment(item.paymentDueDate).format("DD/MM/YYYY"),
                    installment: idx,
                    installmentName: installments[idx]?.name,
                    htmlContentMail: emailTemplate.body,
                    htmlSubjectMail: emailTemplate.subject,
                  };
                  // TODO: add notiSystem => schedule
                  this.mailerClient.sendData(
                    dataEmail,
                    CmdPatternConst.LISTENER.PRIMARY_CONTRACT_QUERY_SEND_MAIL
                  );
                }
                if (smsTemplate) {
                  const dataSMS = {
                    productCode:
                      contract?.primaryTransaction?.propertyUnit?.code,
                    projectName: contract?.primaryTransaction?.project?.name,
                    amount: paymentAmount.toLocaleString(),
                    date: moment(item.paymentDueDate).format("DD/MM/YYYY"),
                    phone:
                      contract?.primaryTransaction?.customer?.personalInfo
                        ?.phone,
                    contentSMS: smsTemplate.body,
                  };
                  this.notificationClient.sendDataSubscribe(
                    dataSMS,
                    CmdPatternConst.SMS.PRIMARY_CONTRACT_PAYMENT_REMINDER
                  );
                }
              }
            }

            // send notification for care customer
            const identityNumber =
              contract?.primaryTransaction?.customer?.identities[0].value;
            const careCustomer = await this.careClient.sendDataPromise(
              { identityNumber },
              CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY
            );
            if (careCustomer && careCustomer.id) {
              this.notificationClient.createNotificationCare(
                "care_PrimaryContract_PaymentReminder",
                null,
                careCustomer.id,
                CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
                contract.id,
                {
                  productCode: contract?.primaryTransaction?.propertyUnit?.code,
                  projectName: contract?.primaryTransaction?.project?.name,
                  amount: paymentAmount.toLocaleString(),
                  date: moment(item.paymentDueDate).format("DD/MM/YYYY"),
                }
              );
            }
          }
        }
      });
    });
    return { "email was sent": contracts.length };
  }
  async getTransactionHistory(
    user: any,
    query: any,
    getDetail: boolean = true
  ) {
    const customer = await this.careClient.sendDataPromise(
      { userId: user.id },
      CmdPatternConst.CARE.GET_CUSTOMER_BY_ID
    );

    if (
      !customer ||
      customer.verifyAccount !== VerifyAccountStatusEnum.APPROVED
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "customer"),
      });
    }

    const identities = customer.identities || [];

    if (identities.length < 1) {
      return [];
    }

    const identitiesArr = identities.map((identity) => {
      return identity.value;
    });

    // lấy tất cả hợp đồng của KH
    let _query = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
          StatusContractEnum.LIQUIDATED,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.customer.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer.identities.value": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identities.value": {
                $in: identitiesArr,
              },
            },
          ],
        },
      ],
      sort: "-modifiedDate",
    };

    let models: any;
    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      const result: any[] =
        await this.repository.findPrimaryContractForCustomer(
          _query,
          true,
          page,
          pageSize
        );

      const data = result?.[0];
      const rows = data?.rows || [];
      const totalCountData = data?.totalCount?.[0];
      const total: number = totalCountData?.count || 0;

      const totalPages = Math.ceil(total / pageSize);

      models = {
        rows,
        page,
        pageSize,
        total,
        totalPages,
      };
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
    }

    const data = isPaging ? models.rows : models;

    if (!getDetail) {
      return data;
    }
    // lọc unique dự án và lấy thông tin
    let projectIds = data.map(
      (contract) => contract.primaryTransaction.project.id
    );
    projectIds = [...new Set(projectIds)];
    const projects = await this.propertyClient.sendDataPromise(
      { ids: projectIds },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );

    // lấy thông tin bàn giao
    const handOvers = await this.handoverQueryService.findAllByQuery({
      status: true,
      "project.id": {
        $in: projectIds,
      },
      // sessions: {
      //   $elemMatch: {
      //     status: true,
      //     startDate: { $lte: new Date() },
      //     endDate: { $gte: new Date() }
      //   }
      // }
    });

    let transactionCodes = [];
    ((data as any[]) || []).forEach((item, index) => {
      let installments = item?.policyPayment?.schedule?.installments || [];
      installments.forEach((i) => {
        (i.receipts || []).forEach((r, index) => {
          transactionCodes.push(r.code);
        });
      });
    });

    const transactions =
      ((await this.transactionClient.sendDataPromise(
        transactionCodes,
        CmdPatternConst.LISTENER.GET_TRANSACTIONS_BY_TRANSACTION_CODES
      )) as any[]) || [];
    const transactionsMapping = transactions.reduce((pv, cu) => {
      pv[cu.code] = cu;
      return pv;
    }, {});

    // transform lại data
    const transformedData = this.transformContractForCustomer(
      data,
      projects,
      handOvers,
      transactionsMapping
    );
    return transformedData;
  }
  async getByCustomer(user: any, query: any, getDetail: boolean = true) {
    const customer = await this.careClient.sendDataPromise(
      { userId: user.id },
      CmdPatternConst.CARE.GET_CUSTOMER_BY_ID
    );

    if (
      !customer ||
      customer.verifyAccount !== VerifyAccountStatusEnum.APPROVED
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "customer"),
      });
    }

    const identities = customer.identities || [];

    if (identities.length < 1) {
      return [];
    }

    const identitiesArr = identities.map((identity) => {
      return identity.value;
    });

    // lấy tất cả hợp đồng của KH
    let _query = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
          StatusContractEnum.LIQUIDATED,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.customer.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer.identities.value": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identities.value": {
                $in: identitiesArr,
              },
            },
          ],
        },
      ],
      sort: "-modifiedDate",
    };

    let models: any;
    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      models = await this.repository
        .findPrimaryContractForCustomer(_query, true, page, pageSize)
        .then((result: any[]) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          return {
            rows: result[0].rows,
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
        });
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
    }

    const data = isPaging ? models.rows : models;

    if (!getDetail) {
      return data;
    }
    // lọc unique dự án và lấy thông tin
    let projectIds = data.map(
      (contract) => contract.primaryTransaction.project.id
    );
    projectIds = [...new Set(projectIds)];
    const projects = await this.propertyClient.sendDataPromise(
      { ids: projectIds },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );

    // lấy thông tin bàn giao
    const handOvers = await this.handoverQueryService.findAllByQuery({
      status: true,
      "project.id": {
        $in: projectIds,
      },
      // sessions: {
      //   $elemMatch: {
      //     status: true,
      //     startDate: { $lte: new Date() },
      //     endDate: { $gte: new Date() }
      //   }
      // }
    });

    let transactionCodes = [];
    ((data as any[]) || []).forEach((item, index) => {
      let installments = item?.policyPayment?.schedule?.installments || [];
      installments.forEach((i) => {
        (i.receipts || []).forEach((r, index) => {
          transactionCodes.push(r.code);
        });
      });
    });

    const transactions =
      ((await this.transactionClient.sendDataPromise(
        transactionCodes,
        CmdPatternConst.LISTENER.GET_TRANSACTIONS_BY_TRANSACTION_CODES
      )) as any[]) || [];
    const transactionsMapping = transactions.reduce((pv, cu) => {
      pv[cu.code] = cu;
      return pv;
    }, {});

    // transform lại data
    const transformedData = this.transformContractForCustomer(
      data,
      projects,
      handOvers,
      transactionsMapping
    );
    return isPaging
      ? Object.assign(models, { rows: transformedData })
      : transformedData;
  }

  async getAllCustomerByProjectId(lstProjectId): Promise<any> {
    const _query: any = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
          StatusContractEnum.LIQUIDATED,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.project.id": { $in: lstProjectId },
            },
          ],
        },
      ],
    };

    const data =
      await this.repository.findCustomerIdentitiesPrimaryContractByProjectId(
        _query
      );
    let lstCustomerIdentities: any[] = [];
    if (data.length > 0) {
      lstCustomerIdentities = data[0].identties;
    }
    return lstCustomerIdentities;
  }

  private async transformContractForCustomer(
    models,
    projects,
    handOvers,
    transactionsMapping = {}
  ) {
    const contractsMap = {};
    const modelsSyncErpData = models.filter((x) => !!x.syncErpData);
    const syncHistorys: any = await this.statushistoryService.findByQuery({
      "statusHistory.contractid": {
        $in: modelsSyncErpData.map((x) => x.syncErpData.contractid),
      },
    });
    models.forEach((model) => {
      const project: any = projects.find(
        (p) => p.id === model.primaryTransaction.project.id
      );
      let status = "waiting";
      let installment = -1;
      let installments = model?.policyPayment?.schedule?.installments || [];
      const { imageUrl, ward, province, district, city, address, type } =
        project;

      let interest = {};

      const {
        _id,
        code,
        price,
        bedroom,
        toilet,
        area,
        block,
        floor,
        direction,
      } = model.primaryTransaction.propertyUnit;
      let deliveryItems = model.deliveryItems || [];
      let ownerStatus = "owner";

      if (model && model.status === StatusContractEnum.LIQUIDATED) {
        // đã thanh lý
        ownerStatus = model.liquidation
          ? model.liquidation.type
          : LiquidationTypeEnum.TRANSFER;
      }

      if (
        model.handoverStatus &&
        model.handoverStatus !== HandoverStatusEnum.init &&
        model.handoverStatus !== HandoverStatusEnum.scheduled
      ) {
        status = model.handoverStatus;
        installment = model.deliveryHistories.length + 1;
      } else {
        if (
          model &&
          (model.status === StatusContractEnum.APPROVED ||
            model.status === StatusContractEnum.LIQUIDATED)
        ) {
          if (installments.length > 0) {
            // get interest from contract;
            interest = this.getInterestOfContract(model);

            let totalTransfered = 0;
            let installmentChoose = 0;
            // check xem có đợt thanh toán nào chưa thanh toán đủ tiền không
            installments.forEach((i, idx) => {
              i.totalTransfered = i.totalTransfered || 0;
              if (installment === -1 && i.totalAmount > i.totalTransfered) {
                status = "payment";
                installment = idx;
              }
              totalTransfered += i.totalTransfered;
            });
            if (installment === -1) {
              status = "paymentSuccess";
              installment = installments.length;
            }
            const paymentPercent = model.paymentPercent
              ? model.paymentPercent
              : price != 0
                ? Math.floor((totalTransfered * 100) / price)
                : 0;
            // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
            const handover = handOvers.find(
              (h) => h.project.id === model.primaryTransaction.project.id
            );
            if (handover) {
              let minPercent =
                handover.paymentPercent || HandoverConst.CH_MIN_PERCENT;
              if (paymentPercent >= minPercent) {
                if (model.handoverStatus === HandoverStatusEnum.scheduled) {
                  status = HandoverStatusEnum.scheduled;
                } else {
                  status = HandoverStatusEnum.init;
                }
                deliveryItems = handover.items;
              }
            }
          }
        }
      }

      // lọc các đợt đã thanh toán
      // let transferedInstallments = installments.filter(i => {
      //   return i.totalTransfered > 0
      // });

      ///change get all các đượt thanh toán
      let transferedInstallments = [...installments];

      // lọc các phiếu thu đã được duyệt
      transferedInstallments = transferedInstallments.map((i) => {
        //i.receipts = i.receipts.filter(r => r.status === TransactionStatusEnum.transfered);
        let filteredReceipts = [];
        if (i.receipts) {
          for (let r of i.receipts) {
            const transaction = transactionsMapping[r.code] || {};
            if (r.status === TransactionStatusEnum.transfered) {
              r.state = transaction.state;
              filteredReceipts.push(r);
            }
          }
          i.receipts = filteredReceipts;
        }
        return i;
      });

      let totalAmount = 0;
      if (transferedInstallments && transferedInstallments.length > 0) {
        transferedInstallments.forEach((item: any) => {
          if (item.totalAmount) {
            totalAmount = totalAmount + item.totalAmount;
          }
        });
      }

      // lấy statuscode gần nhất
      let statusCode;
      if (model.syncErpData && model.syncErpData.contractid) {
        const syncHistory = syncHistorys
          ? syncHistorys.find(
            (x) => x.contractid === model.syncErpData.contractid
          )
          : [];
        const stthistory = syncHistory
          ? syncHistory.statusHistory
            .filter((x) => !!x.statuscode)
            .map((x) => x.statuscode)
          : [];
        statusCode = stthistory ? stthistory.slice(-1)[0] : "";
      }

      const result = {
        id: model.id,
        code: model.code,
        project: {
          id: model.primaryTransaction.project.id,
          name: model.primaryTransaction.project.name,
          priceFrom: project.priceFrom
            ? project.priceFrom["$numberDecimal"]
            : "",
          imageUrl,
          ward,
          province,
          district,
          city,
          address,
          type,
          interestRate: project.setting ? project.setting.interestRate : null,
          daysPerYear: project.setting ? project.setting.daysPerYear : null,
          delayDay: project.setting ? project.setting.delayDay : null,
          onlyWorkday: project.setting ? project.setting.onlyWorkday : null,
        },
        propertyUnit: {
          _id,
          code,
          price,
          bedroom: parseInt(bedroom),
          toilet,
          area,
          block,
          floor,
          direction,
        },
        status: {
          code: status,
          installment: installment,
          ownerStatus: ownerStatus,
        },
        installments: transferedInstallments,
        totalTransfered: transferedInstallments.reduce((prev, cur) => {
          return prev + cur.totalTransfered;
        }, 0),
        depositConfirm: model.depositConfirmFromCustomer || false,
        transferConfirm: model.transferConfirmFromCustomer || false,
        files: model.files || [],
        deliveryItems: deliveryItems,
        interest: interest,
        interestCalculations: model.interestCalculations,
        policyPayment: model.policyPayment,
        policyDiscounts: model.policyDiscounts,
        totalAmount: totalAmount,
        liquidation: model.liquidation,
        liquidate: model.liquidate,
        statusCode: statusCode,
        contractStatus: model.status,
      };

      // propertyUnitId - contract type
      const keyMap = `${_id}_${model.type}`;
      if (
        model.type === ContractEnum.PURCHASE ||
        model.type === ContractEnum.RENT
      ) {
        if (contractsMap[`${_id}_${ContractEnum.DEPOSIT}`]) {
          contractsMap[`${_id}_${ContractEnum.DEPOSIT}`] = result;
        } else {
          contractsMap[`${_id}_${ContractEnum.PURCHASE}`] = result;
        }
      } else if (model.type === ContractEnum.DEPOSIT) {
        if (
          !contractsMap[`${_id}_${ContractEnum.PURCHASE}`] &&
          !contractsMap[`${_id}_${ContractEnum.RENT}`]
        ) {
          contractsMap[keyMap] = result;
        }
      } else {
        contractsMap[keyMap] = result;
      }
    });

    return Object.keys(contractsMap).map((key) => contractsMap[key]);
  }

  async getHandoverByProject(handoverId, query) {
    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: true,
    });

    if (!handOverSetting) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Thiết lập"),
      });
    }

    const project = await this.getProjectById(handOverSetting.project.id);

    if (!project) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Dự án"),
      });
    }

    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);

    // let sessions = (handOverSetting.sessions || []).filter(ses => ses.status);
    // if(!_.isEmpty(query["session"])) {
    //   sessions = sessions.filter(ses => ses.name === query["session"])
    // }

    // if(sessions.length === 0) {
    //   if (isPaging) {
    //     return {
    //       rows: [],
    //       page: query["page"],
    //       pageSize: query["pageSize"],
    //       total: 0,
    //       totalPages: 1
    //     };
    //   } else {
    //     return [];
    //   }
    // }

    // lấy ra các mã sản phẩm thuộc các đợt bàn giao đang active
    // let productCodes = [];
    // sessions.map(ses => {
    //   productCodes = productCodes.concat(ses.productCodes);
    //   if (ses.exception.priority) {
    //     productCodes = productCodes.concat(ses.exception.productCodes);
    //   }
    // });
    // productCodes = [...new Set(productCodes)];

    // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
    const minPercent =
      handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    let andQuery: any = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.keywords)) {
      andQuery = andQuery.concat({
        $or: [
          {
            "primaryTransaction.propertyUnit.code": {
              $regex: query.keywords,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.name": {
              $regex: query.keywords,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.email": {
              $regex: query.keywords,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.phone": {
              $regex: query.keywords,
              $options: "i",
            },
          },
        ],
      });
    }

    if (!_.isEmpty(query.floors) && query.floors.length > 0) {
      let lstFloor = [];
      lstFloor = lstFloor.concat(query.floors);
      andQuery = andQuery.concat({
        "primaryTransaction.propertyUnit.floor": { $in: lstFloor },
      });
    }

    if (!_.isEmpty(query.blocks) && query.blocks.length > 0) {
      let lstBlock = [];
      lstBlock = lstBlock.concat(query.blocks);
      andQuery = andQuery.concat({
        "primaryTransaction.propertyUnit.block": { $in: lstBlock },
      });
    }

    if (!_.isEmpty(query.rooms) && query.rooms.length > 0) {
      let lstRoom = [];
      lstRoom = lstRoom.concat(query.rooms);
      andQuery = andQuery.concat({
        "primaryTransaction.propertyUnit.shortCode": { $in: lstRoom },
      });
    }

    // lấy tất cả hợp đồng đủ điều kiện bàn giao của project
    let _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      // 'primaryTransaction.propertyUnit.code': { $in: productCodes },
      $and: andQuery,
      sort: "handoverStatus,-modifiedDate",
      _fields: `
          id,
          primaryTransaction.propertyUnit.code,
          primaryTransaction.customer.personalInfo,
          primaryTransaction.customer.info,
          primaryTransaction.customer.employee.name,
          deliveryResult,
          handoverStatus,
          deliveryDate,
          isSendDelivery,
          deliveryItems,
          handoverSchedule
        `,
      paymentPercent: { $gte: minPercent },
    };

    // Trạng thái
    if (!_.isEmpty(query.status) && query.status.length > 0) {
      let lstStatus = [];
      lstStatus = lstStatus.concat(query.status);
      _query["handoverStatus"] = { $in: lstStatus };
    }

    // Filter theo ngày Xác nhận bàn giao
    if (!_.isEmpty(query["createdFrom"]) || !_.isEmpty(query["createdTo"])) {
      _query["deliveryDate"] = {};
      if (!_.isEmpty(query["createdFrom"])) {
        _query["deliveryDate"]["$gte"] = new Date(query["createdFrom"]);
      }
      if (!_.isEmpty(query["createdTo"])) {
        _query["deliveryDate"]["$lte"] = new Date(query["createdTo"]);
      }
    }

    let models: any;
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      models = await this.repository
        .findPrimaryContractForCustomer(_query, true, page, pageSize)
        .then((result: any[]) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          return {
            rows: result[0].rows.map((r) => {
              return this.transformHandoverItem(r, project, handOverSetting);
            }),
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
        });
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
      models = models.map((r) => {
        return this.transformHandoverItem(r, project, handOverSetting);
      });
    }

    return models;
  }

  async reportHandoverByProject(handoverId, query) {
    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: true,
    });

    if (!handOverSetting) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Thiết lập"),
      });
    }

    const project = await this.getProjectById(handOverSetting.project.id);

    if (!project) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Dự án"),
      });
    }

    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);

    // let sessions = (handOverSetting.sessions || []).filter(ses => ses.status);
    // if(!_.isEmpty(query["session"])) {
    //   sessions = sessions.filter(ses => ses.name === query["session"])
    // }

    // if(sessions.length === 0) {
    //     return [];
    // }

    // lấy ra các mã sản phẩm thuộc các đợt bàn giao đang active
    // let productCodes = [];
    // sessions.map(ses => {
    //   productCodes = productCodes.concat(ses.productCodes);
    //   if (ses.exception.priority) {
    //     productCodes = productCodes.concat(ses.exception.productCodes);
    //   }
    // });
    // productCodes = [...new Set(productCodes)];

    // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
    const minPercent =
      handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    let andQuery: any = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.q)) {
      andQuery = andQuery.concat({
        $or: [
          {
            "primaryTransaction.propertyUnit.code": {
              $regex: query.q,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.name": {
              $regex: query.q,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.email": {
              $regex: query.q,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.phone": {
              $regex: query.q,
              $options: "i",
            },
          },
        ],
      });
    }

    // lấy tất cả hợp đồng đủ điều kiện bàn giao của project
    let _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      // 'primaryTransaction.propertyUnit.code': { $in: productCodes },
      $and: andQuery,
      sort: "handoverStatus,-modifiedDate",
      _fields: `
          id,
          primaryTransaction.propertyUnit.code,
          primaryTransaction.customer.personalInfo,
          primaryTransaction.customer.info,
          primaryTransaction.customer.employee.name,
          deliveryResult,
          handoverStatus,
          deliveryDate,
          isSendDelivery,
          deliveryItems
        `,
      paymentPercent: { $gte: minPercent },
    };

    let models: any;
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      models = await this.repository
        .findPrimaryContractForCustomer(_query, true, page, pageSize)
        .then((result: any[]) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          return result[0].rows.map((r) => {
            return this.transformHandoverItem(r, project, handOverSetting);
          });
        });
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
      models = models.map((r) => {
        return this.transformHandoverItem(r, project, handOverSetting);
      });
    }

    return models;
  }

  async getHandoverByStatus(handoverId, query) {
    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: handoverId,
      status: true,
    });
    const handoverSchedules: IHandoverScheduleDocument[] =
      await this.handoverScheduleQueryService.findAllByDeliveryId(handoverId);
    if (!handOverSetting) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Thiết lập"),
      });
    }
    let handoverScheduleIds: string[] = [];
    if (handoverSchedules)
      handoverScheduleIds = handoverSchedules.map((element) => {
        return element.id;
      });

    const project = await this.getProjectById(handOverSetting.project.id);

    if (!project) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Dự án"),
      });
    }
    let lstPropertyUnitId: any[] = [];

    // get list id sản phẩm xd03
    let q;
    if (handOverSetting.accountingConfirm) {
      q = {
        "syncErpData.anal_ppt9": "XD03",
        "syncErpData.ktxn": "Y",
        "project.id": handOverSetting.project.id,
      };
    } else {
      q = {
        "syncErpData.anal_ppt9": "XD03",
        "project.id": handOverSetting.project.id,
      };
    }
    let lstPropertyUnit = await this.propertyClient.sendDataPromise(
      q,
      CmdPatternConst.PROPERTY.GET_PROPERTY_UNIT_BY_PROJECT_ID
    );
    if (lstPropertyUnit && lstPropertyUnit.length > 0) {
      lstPropertyUnitId = lstPropertyUnit.map((item) => item.id);
    }

    let isPaging = !_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"]);

    // let sessions = (handOverSetting.sessions || []).filter(ses => ses.status);
    // if(!_.isEmpty(query["session"])) {
    //   sessions = sessions.filter(ses => ses.name === query["session"])
    // }

    // if(sessions.length === 0) {
    //   if (isPaging) {
    //     return {
    //       rows: [],
    //       page: query["page"],
    //       pageSize: query["pageSize"],
    //       total: 0,
    //       totalPages: 1
    //     };
    //   } else {
    //     return [];
    //   }
    // }

    // lấy ra các mã sản phẩm thuộc các đợt bàn giao đang active
    // let productCodes = [];
    // sessions.map(ses => {
    //   productCodes = productCodes.concat(ses.productCodes);
    //   if (ses.exception.priority) {
    //     productCodes = productCodes.concat(ses.exception.productCodes);
    //   }
    // });
    // productCodes = [...new Set(productCodes)];

    // tính phần trăm tối thiểu hợp đồng phải thanh toán để được bàn giao
    const minPercent =
      handOverSetting.paymentPercent || HandoverConst.CH_MIN_PERCENT;

    let andQuery: any = [
      {
        $or: [
          { type: ContractEnum.DEPOSIT, purchase: null },
          { type: { $ne: ContractEnum.DEPOSIT } },
        ],
      },
    ];

    if (!_.isEmpty(query.q)) {
      andQuery = andQuery.concat({
        $or: [
          {
            "primaryTransaction.propertyUnit.code": {
              $regex: query.q,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.name": {
              $regex: query.q,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.email": {
              $regex: query.q,
              $options: "i",
            },
          },
          {
            "primaryTransaction.customer.personalInfo.phone": {
              $regex: query.q,
              $options: "i",
            },
          },
        ],
      });
    }

    // lấy tất cả hợp đồng đủ điều kiện bàn giao của project
    let _query = {
      status: StatusContractEnum.APPROVED,
      "primaryTransaction.project.id": handOverSetting.project.id,
      "primaryTransaction.propertyUnit.id": { $in: lstPropertyUnitId },
      // 'primaryTransaction.propertyUnit.code': { $in: productCodes },
      id: { $nin: handoverScheduleIds },
      $and: andQuery,
      $or: [
        { handoverStatus: HandoverStatusEnum.later },
        { handoverStatus: HandoverStatusEnum.init },
      ],

      sort: "handoverStatus,-modifiedDate",
      _fields: `
          id,
          primaryTransaction.propertyUnit.code,
          primaryTransaction.customer.personalInfo,
          primaryTransaction.customer.info,
          primaryTransaction.customer.employee.name,
          deliveryResult,
          handoverStatus,
          deliveryDate,
          isSendDelivery,
          deliveryItems
        `,
      paymentPercent: { $gte: minPercent },
    };

    let models: any;
    if (isPaging) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      models = await this.repository
        .findPrimaryContractForCustomer(_query, true, page, pageSize)
        .then((result: any[]) => {
          const total = result[0].totalCount[0]
            ? result[0].totalCount[0].count
            : 0;
          return {
            rows: result[0].rows.map((r) => {
              return this.transformHandoverItem(r, project, handOverSetting);
            }),
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
          };
        });
    } else {
      models = await this.repository.findPrimaryContractForCustomer(_query);
      models = models.map((r) => {
        return this.transformHandoverItem(r, project, handOverSetting);
      });
    }

    return models;
  }

  private transformHandoverItem(r, project, handOverSetting) {
    r.projectType = project.type;
    r.projectName = project.name;
    r.projectId = project.id;
    const productCode = r.primaryTransaction.propertyUnit.code;
    // const session = sessions.find(ses => {
    //   if (ses.exception.priority) {
    //     return ses.productCodes.includes(productCode) || ses.exception.productCodes.includes(productCode);
    //   } else {
    //     return ses.productCodes.includes(r.primaryTransaction.propertyUnit.code)
    //   }
    // });
    if (r.deliveryItems) {
      const notPassItem = r.deliveryItems.findIndex((group) => {
        return group.list ? group.list.findIndex((item) => item.isPass) : -1;
      });
      r.checklist = notPassItem === -1;
      delete r.deliveryItems;
    }
    r.session = "";
    r.smsTemplate = handOverSetting ? handOverSetting.smsTemplate : "";
    r.emailTemplate = handOverSetting ? handOverSetting.emailTemplate : "";
    r.careInvestor = project.careInvestor;
    return r;
  }

  async getContractByCode(code: string) {
    const contract = await this.repository.findOne(
      { code },
      { "policyPayment.schedule": 1 }
    );
    return contract;
  }

  async getContractsWithStatusForCareCustomer(identitiesArr: any[]) {
    // lấy ra hợp đồng của khách hàng với trang thái approve, waiting
    let _query = {
      status: {
        $in: [
          StatusContractEnum.APPROVED,
          StatusContractEnum.ACCOUNTANT_WAITING,
        ],
      },
      $and: [
        {
          $or: [
            { type: ContractEnum.DEPOSIT, purchase: null },
            { type: { $ne: ContractEnum.DEPOSIT } },
          ],
        },
        {
          $or: [
            {
              "primaryTransaction.customer.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer.identities.value": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identityNumber": {
                $in: identitiesArr,
              },
            },
            {
              "primaryTransaction.customer2.identities.value": {
                $in: identitiesArr,
              },
            },
          ],
        },
      ],
      _fields: "id",
    };

    return this.repository.findPrimaryContractForCustomer(_query, false);
  }
  syncProject(project: any) {
    return this.repository.updateMany(
      {
        "primaryTransaction.project.id": project.id,
      },
      {
        $set: {
          "primaryTransaction.project": project,
        },
      }
    );
  }

  syncPrimaryTransaction(primaryTransaction: any) {
    return this.repository.updateMany(
      {
        "primaryTransaction.id": primaryTransaction.id,
      },
      {
        $set: {
          primaryTransaction: primaryTransaction,
        },
      }
    );
  }

  async getPrimaryContractsByPropertyUnitCodeAndProjectId(query: {
    propertyUnitCode: string;
    projectId: string;
  }) {
    const aggregate = [
      {
        $match: {
          $and: [
            { "primaryTransaction.project.id": query.projectId },
            { "primaryTransaction.propertyUnit.code": query.propertyUnitCode },
            {
              status: {
                $in: [
                  StatusContractEnum.APPROVED,
                  StatusContractEnum.ACCOUNTANT_WAITING,
                  StatusContractEnum.LIQUIDATED,
                ],
              },
            },
            {
              $or: [
                { type: ContractEnum.DEPOSIT, purchase: null },
                { type: { $ne: ContractEnum.DEPOSIT } },
              ],
            },
          ],
        },
      },
      {
        $project: {
          primaryTransaction: 1,
        },
      },
    ];

    return this.repository.findPrimaryContractsAggregate(aggregate);
  }

  async getPrimaryContractsByPropertyUnitCodesAndProjectIds(query: {
    propertyUnitCodes: string[];
    projectIds: string[];
  }) {
    const aggregate = [
      {
        $match: {
          $and: [
            { "primaryTransaction.project.id": { $in: query.projectIds } },
            {
              "primaryTransaction.propertyUnit.code": {
                $in: query.propertyUnitCodes,
              },
            },
            {
              status: {
                $in: [
                  StatusContractEnum.APPROVED,
                  StatusContractEnum.ACCOUNTANT_WAITING,
                  StatusContractEnum.LIQUIDATED,
                ],
              },
            },
            {
              $or: [
                { type: ContractEnum.DEPOSIT, purchase: null },
                { type: { $ne: ContractEnum.DEPOSIT } },
              ],
            },
          ],
        },
      },
      {
        $project: {
          primaryTransaction: 1,
        },
      },
    ];

    return this.repository.findPrimaryContractsAggregate(aggregate);
  }

  async getDebtReportContract(user: any, query: any) {
    return this.repository.findDebtReportContract(user, query);
  }

  async resendEmail(user: any, dto: ResendEmailDto) {

    if (!dto.id) {
      return this.getResponse('PRIMARYCONTRACT0018');
    }

    const contract = await this.repository.findOne({ id: dto.id });
    if (!contract) {
      return this.getResponse('PRIMARYCONTRACT0004');
    }

    const projectId = contract.primaryTransaction.project.id;
    const project = await this.propertyClient.sendDataPromise(
      { id: projectId },
      cmd.PROJECT.GET_PROJECT_BY_ID
    );
    if (!project) {
      return this.getResponse('PRJE0011');
    }
    if (project?.emailTemplate?.length == 0) {
      return this.getResponse('PRJE0018');
    }

    // get all customer info 
    if (dto.customerCodes?.length == 0) {
      return this.getResponse('CUS0001');
    }

    const customers = await this.customerClient.sendDataPromise(
      { code: { $in: dto.customerCodes ? dto.customerCodes : [] } },
      cmd.CUSTOMER.GET_INFO_WITH_QUERY
    );
    if (customers?.length == 0) {
      console.log('no customer found!');
      return this.getResponse('CUS0001');
    }

    const template = project.emailTemplate.filter(t => t.type == '[template_resend_email]');

    // const emailTemplate = this.renderTemplate(emailTemplateRaw.replace(/(?:\r\n|\r|\n)/g, '<br>'), data);

    // Gửi lại mail nhắc nợ
    customers.forEach(async customer => {
      const emailBody = Object.assign(new notificationPayloadDto(), {
        subject: template.subject,
        content: template.body,
        receives: customer.personalInfo.email,
        // ccReceives: emailCC,
        // bccReceives: emailBCC,
      });

      await this.notiService.push(emailBody);
    });

    return { success: true };
  }

  async getFileByPosIdUserLogin(user: any) {
    // lấy thông tin user login
    const e = await this.employeeClient.sendDataPromise({ id: user.id }, CmdPatternConst.EMPLOYEE.EMPLOYEE_GET_BY_ID);
    if (e && e.pos && e.pos.id) {
      const file = await this.orgchartClient.sendDataPromise(
        e.pos.id,
        CmdPatternConst.ORGCHART.GET_TEMPLATE_DEPT_REPORT_FILE_BY_POS_ID
      );
      if (file && file.length) {
        // get data
        return file[0];
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  async assignDebtCollector(user, body: AssignDebtCollectorDto) {
    const contracts = await this.repository.find({ id: { $in: body.contractIds } });
    if (_.isEmpty(contracts)) {
      return this.getResponse('PRIMARYCONTRACT0004');
    }

    if (!body.employee) {
      return this.getResponse('PRIMARYCONTRACT0011');
    }

    const employeesInfo = await this.employeeClient.sendDataPromise(
      { id: { $in: [body.employee] } },
      cmd.EMPLOYEE.GET_EMPLOYEE_BY_QUERY
    );
    if (employeesInfo?.length == 0) {
      return this.getResponse('PRIMARYCONTRACT0011');
    }

    const accountInfo = await this.stsClient.sendDataPromise(
      { id: { $in: [employeesInfo[0].account.id] } },
      cmd.STS.ACCOUNT.GET_INFO
    );
    if (accountInfo?.result?.length == 0) {
      return this.getResponse('STSE0005');
    }

    for (let i = 0; i < contracts.length; i++) {
      const assignedDate = new Date();
      const debtEmp = {
        id: employeesInfo[0].id,
        code: employeesInfo[0].code,
        accountId: employeesInfo[0].account.id,
        username: accountInfo.result[0].username,
        name: accountInfo.result[0].fullName,
        email: accountInfo.result[0].email,
        phone: accountInfo.result[0].phone,
        assignedDate,
      };

      contracts[i].debtCollector = debtEmp;
      contracts[i].assignStatus = AssignStatusEnum.ASSIGN;

      // add to assign histories
      const data = {
        debtCollector: debtEmp,
        assignStatus: AssignStatusEnum.ASSIGN,
        assigner: user.id
      };
      contracts[i].assignHistories.push(data);

      // add to debt history
      this.addDebtHistory(contracts[i], DebtHistoryStatusEnum.ASSIGN, user, assignedDate, data, 'assigned');

      await this.repository.updateOne({ id: contracts[i].id }, contracts[i]);
    }

    return this.getResponse('0');
  }

  async deassignDebtCollector(user, body: DeassignDebtCollectorDto) {
    const contracts = await this.repository.find({ id: { $in: body.contractIds } });
    if (_.isEmpty(contracts)) {
      return this.getResponse('PRIMARYCONTRACT0004');
    }

    for (let i = 0; i < contracts.length; i++) {
      contracts[i].debtCollector = null;
      contracts[i].assignStatus = AssignStatusEnum.PENDING;

      // add to histories
      contracts[i].assignHistories.push({
        debtCollector: null,
        assignStatus: AssignStatusEnum.DEASSIGN,
        assigner: user.id
      });

      // add to debt history
      // this.addDebtHistory(contracts[i], DebtHistoryStatusEnum.DEASSIGN, user, new Date());

      await this.repository.updateOne({ id: contracts[i].id }, contracts[i]);
    }

    return this.getResponse('0');
  }

  async addInteractHistory(account, body: InteractionDto, isCallRecord = false) {
    const contract = await this.repository.findOne({ id: body.id });
    if (_.isEmpty(contract)) {
      return this.getResponse('PRIMARYCONTRACT0004');
    }

    // only assigned collector can interact
    if (account.id !== contract.debtCollector?.['accountId']) {
      return this.getResponse('PRIMARYCONTRACT0028');
    }

    const createdDate = new Date();

    const data = {
      title: body.title,
      detail: body.detail,
      createdDate
    };

    if (isCallRecord) {
      contract.callRecords.push(data);
      this.addDebtHistory(contract, DebtHistoryStatusEnum.INTERACTING, account, createdDate, data, 'call');
    } else {
      contract.notes.push(data);
      this.addDebtHistory(contract, DebtHistoryStatusEnum.INTERACTING, account, createdDate, data, 'note');
    }

    // check if has history
    await this.repository.updateOne({ id: body.id }, contract);

    return this.getResponse('0');
  }

  addDebtHistory(contract, status, user, createdDate, data: any, type = '') {
    // check if has history
    if (contract.debtHistory && !contract.debtHistory.customer) {
      const customerInfo = contract.primaryTransaction.customer;
      const historyCustomer = {
        name: customerInfo.company.name ? customerInfo.company.name : customerInfo.personalInfo.name,
        gender: customerInfo.info.gender,
        address: customerInfo.company?.address?.address ?
          customerInfo.company?.address?.address :
          customerInfo.info.rootAddress?.address ? 
          customerInfo.info.rootAddress?.address : 
          customerInfo.info.address?.address || '',
        phone: customerInfo.personalInfo.phone
      };
      contract.debtHistory = {
        customer: historyCustomer,
        histories: []
      };
    }

    contract.debtHistory.status = status;
    contract.debtHistory.histories.push({
      status,
      type,
      data,
      createdBy: {
        id: user.id,
        fullName: user.fullName,
        username: user.username,
      },
      createdDate,
    });

    return contract;
  }

  countDayBetweenCurrentDate(paymentDueDate) {
    let difference = new Date().getTime() - new Date(paymentDueDate).getTime();
    let days = Math.ceil(difference / (1000 * 3600 * 24));
    return days;
  }

  async handedContractByProjectId(projectId) {
    const query = {
      handoverStatus: HandoverStatusEnum.handed,
      "primaryTransaction.project.id": projectId,
    };
    return await this.repository.find(query, { id: 1 });
  }

  async generateFileByQuery(user, query, fileName: string, isSeeAll = false) {
    const entities = await this.getContract(user, query, isSeeAll);

    return await this.fileGenerationService.generateAndSaveTicketFile(
      query["url"],
      entities,
      fileName,
      user
    );
  }

  async printTicketByQuery(user, query, res, isSeeAll = false) {
    var fileName = "templateFile" + new Date().getTime();
    this.generateFileByQuery(user, query, fileName, isSeeAll).then(() => {
      fileName = fileName + ".pdf";
      const filePath = join(
        this.staticAssetService.getUploadFolderPath(),
        fileName
      );

      const isFileExisted = existsSync(filePath);
      if (isFileExisted === false) {
        throw new NotFoundException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "field"),
        });
      }
      res.sendFile(fileName, {
        root: this.staticAssetService.getUploadFolderPath(),
      });
      setTimeout(() => {
        unlinkSync(filePath);
      }, 10000);
    });
  }

  async createContract(
    user: any,
    dto: CreatePrimaryContractDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, clc.green("create service"));
    let amount,
      price = 0,
      landPrice = 0,
      housePrice = 0;
    let receipt = [];
    let projectSetting = {};
    const model: any = { ...dto };
    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    model.createdBy = user.id;
    model.createdDate = new Date();
    let primaryTransaction;
    if (dto.primaryTransactionId) {
      primaryTransaction = await this.propertyClient.sendDataPromise(
        {
          action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
          id: dto.primaryTransactionId,
        },
        CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID
      );
      if (!primaryTransaction) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0005" },
          HttpStatus.OK
        );
      }
      if (primaryTransaction.status !== "SUCCESS") {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0019" },
          HttpStatus.OK
        );
      }
      if (primaryTransaction.contract) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0006" },
          HttpStatus.OK
        );
      }
      if (dto.customer2 && dto.customer2.name) {
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(
          dto.customer2
        );
      }
      model.primaryTransaction = primaryTransaction;

      if (dto.calcContractPrice) {
        price = primaryTransaction.propertyUnit.contractPrice;
        housePrice = 0;
        landPrice = 0;
      } else {
        price = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.priceVat
          : primaryTransaction.propertyUnit.price;
        housePrice = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.housePriceVat
          : primaryTransaction.propertyUnit.housePrice;
        landPrice = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.landPriceVat
          : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee = {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee:
          primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
      };

      receipt = primaryTransaction.reciept ? primaryTransaction.reciept : [];
      receipt = receipt.filter((r) => r.status === "TRANSFERED");

      amount = primaryTransaction.amount;
      projectSetting = primaryTransaction.project.setting;
    } else {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0005" },
        HttpStatus.OK
      );
    }

    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIdsForContract(
        dto.policyDiscountIds,
        primaryTransaction.project.id
      );
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0007" },
          HttpStatus.OK
        );
      }
      model.policyDiscounts = policyDiscounts;

      // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (
          policyDiscounts.some(
            (e) =>
              e.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
          )
        ) {
          throw new HttpException(
            { errorCode: "PRIMARYCONTRACT0009" },
            HttpStatus.OK
          );
        }
      } else {
        if (
          policyDiscounts.some(
            (e) =>
              !e.typeRealEstate ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
          )
        ) {
          throw new HttpException(
            { errorCode: "PRIMARYCONTRACT0008" },
            HttpStatus.OK
          );
        }
      }
    } else {
      model.policyDiscounts = [];
    }
    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findOne({
        id: dto.policyPaymentId,
        type: PolicyTypeEnum.PAYMENT,
        active: true,
        "project.id": primaryTransaction.project.id
      });
      if (!policyPayment) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0010" },
          HttpStatus.OK
        );
      }
      const dataAftertransformPolicyPayment = this.transformPolicyPayment(
        model,
        price,
        projectSetting,
        dto.signedDate,
        receipt,
        policyPayment,
        housePrice,
        landPrice,
        dto.maintenanceFee
      );

      model.policyPayment = dataAftertransformPolicyPayment.policyPayment;
      model.discountValue = dataAftertransformPolicyPayment.discountValue;
      model.issuedPrice = (primaryTransaction.propertyUnit.landPrice && primaryTransaction.propertyUnit.housePrice) ?
        primaryTransaction.propertyUnit.housePrice + primaryTransaction.propertyUnit.landPrice : primaryTransaction.propertyUnit.price;
      model.productPrice = model.policyPayment.schedule.installments.reduce((sum, x) => sum + x.totalAmount, 0);
    }

    if (!model.code) {
      const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-`;
      model.code = await this.codeGenerateService.generateCode(
        CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
        prefix
      );
    }
    if (model.primaryTransaction.customer.type === CustomerTypeEnum.INDIVIDUAL)
      model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`;
    else
      model.name = `${model.code}-${model.primaryTransaction.customer.company.name}`;
    if (model.policyPayment) {
      model.paymentPercent = this.calPaymentPercentage(
        {
          installments: model.policyPayment.schedule.installments,
        },
        model
      );
    }

    model.orgCode = user.orgCode?.substring(0, 4);
    model.interestCalculations = await this.createInterestCalculations(model)

    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);
    let histories = {
      propertyUnitId: primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: "",
      contractStatus: "Hợp đồng: " + ContractTypeEnum.CREATED,
      actionName: ActionContractName.CREATE_CONTRACT,
      modifiedDate: new Date(),
    };
    await this.propertyClient.sendDataPromise(
      histories,
      CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT
    );
    return { id: model.id };
  }

  async createPurchaseContract(user: any, dto: any, headers) {
    this.loggerService.log(this.context, clc.green("create service"));
    let price = 0,
      landPrice = 0,
      housePrice = 0;
    const purchaseDto: any = await this.repository.findOne({
      "deposit.id": dto.id,
      type: { $in: [ContractEnum.PURCHASE, ContractEnum.RENT] },
    });

    if (purchaseDto) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0014" },
        HttpStatus.OK
      );
    }

    const model: any = { ...dto };
    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    model.createdBy = user.id;
    model.createdDate = new Date();
    let projectInfo: any = {};
    let _fields: any = {
      id: 1,
      ownership: 1,
    };
    const projectIds = [model.primaryTransaction.project.id];
    projectInfo = await this.propertyClient.sendDataPromise(
      { projectIds, _fields },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS
    );
    if (
      projectInfo &&
      projectInfo.ownership &&
      projectInfo.ownership === CommonConst.HINH_THUC.SO_HUU.SU_DUNG_VINH_VIEN
    ) {
      model.type = ContractEnum.PURCHASE;
    } else {
      model.type = ContractEnum.RENT;
    }

    model.deposit = {
      id: dto.id,
      code: dto.code,
      name: dto.name,
    };

    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIdsForContract(
        dto.policyDiscountIds,
        dto.primaryTransaction.project.id
      );
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0007" },
          HttpStatus.OK
        );
      }
      model.policyDiscounts = policyDiscounts;

      // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (
          policyDiscounts.some(
            (e) =>
              e.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
          )
        ) {
          throw new HttpException(
            { errorCode: "PRIMARYCONTRACT0009" },
            HttpStatus.OK
          );
        }
      } else {
        if (
          policyDiscounts.some(
            (e) =>
              !e.typeRealEstate ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
          )
        ) {
          throw new HttpException(
            { errorCode: "PRIMARYCONTRACT0008" },
            HttpStatus.OK
          );
        }
      }
    } else {
      model.policyDiscounts = [];
    }

    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findOne({
        id: dto.policyPaymentId,
        type: PolicyTypeEnum.PAYMENT,
      });
      if (!policyPayment) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0010" },
          HttpStatus.OK
        );
      }
      // chỉ tính lại khi thay đổi chính sách
      if (dto.policyPaymentId !== model.policyPayment.id) {
        const receiptList = this.getTransferredReceiptList(model);
        const dataAftertransformPolicyPayment = this.transformPolicyPayment(
          model,
          model.primaryTransaction.propertyUnit.price,
          {},
          dto.signedDate,
          receiptList,
          policyPayment,
          model.primaryTransaction.propertyUnit.housePrice,
          model.primaryTransaction.propertyUnit.landPrice
        );

        model.policyPayment = dataAftertransformPolicyPayment.policyPayment;
        model.discountValue = dataAftertransformPolicyPayment.discountValue;
        model.issuedPrice = (model.primaryTransaction.propertyUnit.landPrice && model.primaryTransaction.propertyUnit.housePrice) ?
          model.primaryTransaction.propertyUnit.housePrice + model.primaryTransaction.propertyUnit.landPrice : model.primaryTransaction.propertyUnit.price;
        model.productPrice = model.policyPayment.schedule.installments.reduce((sum, x) => sum + x.totalAmount, 0);
      }
    }

    let prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_PURCHASE_CONTRACT}${model.primaryTransaction.project.code}-`;
    model.code = await this.codeGenerateService.generateCode(
      CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
      prefix
    );
    if (model.primaryTransaction.customer.type === CustomerTypeEnum.INDIVIDUAL)
      model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`;
    else
      model.name = `${model.code}-${model.primaryTransaction.customer.company.name}`;
    model.status = StatusContractEnum.INIT;
    model.orgCode = user.orgCode?.substring(0, 4);
    delete model.id;
    delete model.interestCalculations;

    if (model.policyPayment) {
      model.paymentPercent = this.calPaymentPercentage(
        {
          installments: model.policyPayment.schedule.installments,
        },
        model
      );
    }
    await this.createNewCustomer(model, headers);
    await this.executeCommand(Action.CREATE, "create", this.commandId, model);

    const depositContract = await this.repository.findOne({ id: model.deposit.id });
    depositContract.purchase = {
      id: model.id,
      code: model.code,
      name: model.name,
    };
    await this.executeCommand(Action.UPDATE, null, this.commandId, depositContract);
    return { id: model.id };
  }

  async requestApproveContract(
    user: any,
    dto: PrimaryContractStatusDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "request approve ticket");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    if (
      oldDto.status !== StatusContractEnum.INIT &&
      oldDto.status !== StatusContractEnum.REJECTED
    ) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0016" },
        HttpStatus.OK
      );
    }
    if (dto.status !== StatusContractEnum.WAITING) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0017" },
        HttpStatus.OK
      );
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;

    let result = await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );

    // Lưu vào lịch sử sản phẩm
    let histories = {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      contractStatus: "Hợp đồng: " + ContractTypeEnum.WAITTING_APPROVAL,
      actionName: ActionContractName.REQUEST_APPROVAL_CONTRACT,
    };
    await this.propertyClient.sendDataPromise(
      histories,
      CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT
    );
    return true;
  }

  async liquidationContract(
    userId: string,
    liquidation: any,
    actionName: string
  ) {
    this.loggerService.log(this.context, "request approve ticket");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({
      id: liquidation.contract.id,
    });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "contract",
          "ID",
          liquidation.contract.id
        ),
      });
    }

    if (oldDto.status !== StatusContractEnum.APPROVED) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus"),
      });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = userId;
    if (liquidation.type === LiquidationTypeEnum.TRANSFER) {
      oldDto.status = StatusContractEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND;
    } else {
      oldDto.status = StatusContractEnum.LIQUIDATED;
      oldDto.liquidate = new Date();
    }
    oldDto.liquidation = liquidation;
    return await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
  }

  async cloneContract(
    userId: string,
    liquidationDto: any,
    actionName: string,
    newTicket
  ) {
    this.loggerService.log(this.context, "clone contract");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository
      .findOne({ id: liquidationDto.contract.id })
      .then((res) => res.toObject());
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "contract",
          "ID",
          liquidationDto.contract.id
        ),
      });
    }

    if (oldDto.status !== StatusContractEnum.APPROVED) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus"),
      });
    }
    const model: any = _.cloneDeep(oldDto);
    this.commandId = uuid.v4();
    model.modifiedBy = userId;
    model.createdDate = new Date();
    model.modifiedDate = new Date();
    model.primaryTransaction = newTicket;
    model.liquidation = liquidationDto;
    model.transferConfirmFromCustomer = false;
    model.status = LiquidationStatusEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND;
    if (model.policyPayment?.schedule?.installments) {
      model.policyPayment.schedule.installments =
        model.policyPayment.schedule.installments.map((e) => ({
          ...e,
          totalTransfered: 0,
          receipts: [],
        }));
    }

    model.oldContract = {
      id: oldDto.id,
      code: oldDto.code,
      name: oldDto.name,
    };

    model.id = uuid.v4();
    model.isTransferred = true;

    // if (!model.code) {
    const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_TRANSFER_CONTRACT}${liquidationDto.proposal.escrowTicket.project.code}-`;
    model.code = await this.codeGenerateService.generateCode(
      CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
      prefix
    );
    // }

    model.name = `${model.code}-${liquidationDto.proposal.customerTransfer.name}`;
    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);

    // update thông tin contract vào sản phẩm
    const propertyUnitId = model.primaryTransaction.propertyUnit.id;
    this.propertyClient.sendDataPromise(
      {
        id: propertyUnitId,
        contract: {
          id: model.id,
          code: model.code,
          type: model.type,
          isTransferred: model.isTransferred,
        },
      },
      CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
        .UPDATE_CONTRACT_IN_PROPERTY_UNIT
    );

    return {
      id: model.id,
      installments: oldDto.policyPayment?.schedule?.installments,
    };
  }

  async approveContract(
    user: any,
    dto: PrimaryContractStatusDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "approve ticket");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    if (oldDto.status !== StatusContractEnum.WAITING) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0016" },
        HttpStatus.OK
      );
    }
    if (
      dto.status !== StatusContractEnum.ACCOUNTANT_WAITING &&
      dto.status !== StatusContractEnum.REJECTED
    ) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0017" },
        HttpStatus.OK
      );
    }

    if (dto.status === StatusContractEnum.ACCOUNTANT_WAITING) {
      //  create resident
      let projectInfo: any = {};
      projectInfo = await this.propertyClient.sendDataPromise(
        oldDto.primaryTransaction.project,
        CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
      );
      let newObject = {};
      if (projectInfo) {
        newObject = {
          ...oldDto.primaryTransaction,
          project: projectInfo,
          customer: oldDto.primaryTransaction.customer,
          propertyUnit: oldDto.primaryTransaction.propertyUnit,
          contractId: oldDto.id,
        };
      }

      // send transaction to care service
      await this.careClient.sendDataPromise(
        {
          primaryTransaction: newObject,
        },
        CmdPatternConst.CARE.GET_TRANSACTION
      );

      await this.socialClient.sendDataPromise(
        {
          projectId: oldDto.primaryTransaction.project.id,
        },
        CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP
      );

      const customer = await this.careClient.sendDataPromise(
        {
          identityNumber:
            oldDto.primaryTransaction.customer.identityNumber,
        },
        CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY
      );

      if (customer) {
        this.notificationClient.createNotificationCare(
          "care_depositContract_Approved",
          null,
          customer.id,
          "primary-contract",
          oldDto.id,
          {
            code: oldDto.primaryTransaction.propertyUnit.code,
            projectCode: oldDto.primaryTransaction.project.code,
            projectName: oldDto.primaryTransaction.project.name,
          }
        );
      }
    }

    // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
    if (
      oldDto.policyPayment &&
      oldDto.policyPayment.schedule.installments[0].totalTransfered >=
      oldDto.policyPayment.schedule.installments[0].totalAmount
    ) {
      oldDto.status = StatusContractEnum.APPROVED;
    } else {
      oldDto.status = dto.status;
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.reason = dto.reason;

    await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );

    // Lưu vào lịch sử sản phẩm
    let histories = {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: oldDto.reason,
      contractStatus:
        oldDto.status === StatusContractEnum.ACCOUNTANT_WAITING
          ? "Hợp đồng: " + ContractTypeEnum.WAITING_COLLECT_MONEY
          : "Hợp đồng: " + ContractTypeEnum.REJECTED,
      actionName:
        oldDto.status === StatusContractEnum.ACCOUNTANT_WAITING
          ? ActionContractName.APPROVAL_CONTRACT
          : ActionContractName.REJECT_CONTRACT,
    };
    await this.propertyClient.sendDataPromise(
      histories,
      CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT
    );
    // if (oldDto.type != "deposit")
    //   return await this.sendSap({ id: dto.id });
    return true;
  }
  async sendSap(
    dto: any,
  ) {
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto || oldDto.type === "deposit") {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }
    const contracts = [];
    contracts.push(oldDto.toObject());
    const sapResponse = await this.sendContractToSap(contracts);
    if (!sapResponse || sapResponse.statusCode !== 200) {
      return this.getResponse('COME0013', sapResponse);
    }
    const updateOps = {
      $set: {
        sapCode: sapResponse.contractNumber,
        modifiedDate: new Date()
      }
    };
    await this.repository.updateOne({ id: dto.id }, updateOps);
    return this.getResponse(0);
  }
  private async sendContractToSap(contracts: any) {
    const result = await axios.default.post(
      process.env.CONTRACT_SYNC_SAP || 'https://uat-api-crm.datxanh.com.vn/msx-masterdata-producer/api/v1/contract/sync',
      contracts,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    return result?.data;
  }
  async approveLiquidationContract(
    user: any,
    dto: PrimaryContractStatusDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "approveLiquidationContract");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    if (
      oldDto.status !== StatusContractEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus"),
      });
    }
    if (
      dto.status !== StatusContractEnum.APPROVED &&
      dto.status !== StatusContractEnum.LIQUIDATED
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus"),
      });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;
    await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
    const liquidation = await this.liquidationQueryRepository.findOne({
      "contract.id": dto.id,
    });
    if (dto.status === StatusContractEnum.LIQUIDATED) {
      const newContract: any = await this.repository.findOne({
        "oldContract.id": dto.id,
      });
      // create history
      const history = {
        from: {
          contract: {
            id: oldDto.id,
            code: oldDto.code,
            name: oldDto.name,
          },
          customer: oldDto.primaryTransaction.customer,
          date: liquidation.liquidationDate,
        },
        to: {
          contract: {
            id: newContract.id,
            code: newContract.code,
            name: newContract.name,
          },
          customer: newContract.primaryTransaction.customer,
          date: liquidation.liquidationDate,
        },
        propertyUnit: {
          id: newContract.primaryTransaction.propertyUnit.id,
          code: newContract.primaryTransaction.propertyUnit.code,
        },
      };
      await this.transferHistoryRepository.create(history);
      await this.liquidationQueryRepository.updateStatus(
        {
          "contract.id": dto.id,
        },
        StatusContractEnum.APPROVED
      );
    }
  }

  async approvePurchaseContract(
    user: any,
    dto: PrimaryContractStatusDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "approve ticket");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    if (oldDto.status !== StatusContractEnum.WAITING) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0016" },
        HttpStatus.OK
      );
    }
    if (
      dto.status !== StatusContractEnum.ACCOUNTANT_WAITING &&
      dto.status !== StatusContractEnum.REJECTED
    ) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0017" },
        HttpStatus.OK
      );
    }

    // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
    const existedInstallmentToContractIndex = oldDto.policyPayment
      ? (oldDto.policyPayment.schedule.installments as Array<any>).findIndex(
        (item) => item.isToContract === true
      )
      : -1;
    if (
      existedInstallmentToContractIndex >= 0 &&
      oldDto.policyPayment.schedule.installments[
        existedInstallmentToContractIndex
      ].totalTransfered >=
      oldDto.policyPayment.schedule.installments[
        existedInstallmentToContractIndex
      ].totalAmount
    ) {
      oldDto.status = StatusContractEnum.APPROVED;
    } else {
      oldDto.status = dto.status;
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.reason = dto.reason;
    await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
    return await this.sendSap({ id: dto.id });
  }

  async updateContractFiles(
    user: any,
    dto: UpdatePrimaryContractFileDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "update contract file");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.files = dto.files;
    return await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
  }

  async updateDeliveryDate(
    user: any,
    dto: UpdatePrimaryContractDeliveryDateDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "update contract delivery date");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.deliveryDate = dto.deliveryDate;
    oldDto.filesDelivery = dto.filesDelivery;
    if (
      oldDto.handoverStatus &&
      oldDto.handoverStatus != HandoverStatusEnum.handed
    ) {
      oldDto.handoverStatus = HandoverStatusEnum.handed;
      let syncConfig = await this.syncErpClient.sendDataPromise(
        oldDto.primaryTransaction.project.id,
        CmdPatternConst.SYNC_ERP.GET_CAMPAIGN_ERP_BY_PROJECT_ID
      );
      if (syncConfig && oldDto.syncErpData && oldDto.syncErpData.contractid) {
        let property: any = "";
        if (
          oldDto.primaryTransaction &&
          oldDto.primaryTransaction.propertyUnit &&
          oldDto.primaryTransaction.propertyUnit.attributes &&
          oldDto.primaryTransaction.propertyUnit.attributes.length > 21
        ) {
          if (oldDto.primaryTransaction.propertyUnit.attributes[21].value) {
            property =
              oldDto.primaryTransaction.propertyUnit.attributes[21].value;
          }
        }

        let data: any = {
          formid: "contract01",
          contractid: oldDto.syncErpData.contractid,
          property: property,
          statusdate: oldDto.deliveryDate
            ? moment(oldDto.deliveryDate).format("YYYY-MM-DD hh:mm:ss A")
            : "",
          status: "BGI",
          impstatus: "W",
        };
        let dataSendCRM: any = {
          action: "contract01",
          data: [data],
        };
        // send data sync erp
        await this.syncErpClient.sendDataPromise(
          dataSendCRM,
          CmdPatternConst.SYNC_ERP.SEND_REQUEST_TO_ERP
        );
      }
    }
    // Cập nhật đã bàn giao căn hộ
    let updatePropery = {
      query: { id: oldDto.primaryTransaction.propertyUnit.id },
      model: { $set: { "contract.handoverStatus": HandoverStatusEnum.handed } },
    };
    this.propertyClient.sendDataPromise(
      updatePropery,
      CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY
    );

    // Xác nhận bàn giao
    this.addHistoriesHandover(
      oldDto,
      user,
      dto.deliveryDate,
      HandoverScheduleStatusNameConst.HANDED_OVER,
      HandoverScheduleActionNameConst.ACCEPT_HANDOVER
    );

    return await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
  }

  async addHistoriesHandover(
    contract,
    user,
    handoverStartTime,
    propertyStatusName,
    actionName
  ) {
    let historiesHandover = {
      propertyStatusName: propertyStatusName,
      actionName: actionName, // Hành động
      modifiedBy: user.id,
      modifiedByName: user.name, // Tên người thực hiện
      handoverStartTime: handoverStartTime,
    };
    let updatePropery = {
      query: { id: contract.primaryTransaction.propertyUnit.id },
      model: { $push: { historiesHandover: historiesHandover } },
    };
    this.propertyClient.sendDataPromise(
      updatePropery,
      CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY
    );
  }

  async updateContract(
    user: any,
    dto: UpdatePrimaryContractDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, clc.green("update service"));
    let amount,
      price = 0,
      housePrice = 0,
      landPrice = 0;
    let receipt = [];
    let projectSetting = {};
    const oldDto: any = await this.repository.findOne({ id: dto.id });

    if (!oldDto
      // !oldDto ||
      // (oldDto.status === StatusContractEnum.APPROVED &&
      //   !user.roles.includes(
      //     PermissionEnum.PRIMARY_CONTRACT_UPDATE_APPROVEMENT
      //   ))
    ) {
      throw new BadRequestException();
    }

    let primaryTransaction: any;
    if (dto.primaryTransactionId) {
      primaryTransaction = await this.propertyClient.sendDataPromise(
        {
          action:
            CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
              .GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
          id: dto.primaryTransactionId,
        },
        CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
          .GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID
      );
      if (!primaryTransaction) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0005" },
          HttpStatus.OK
        );
      }

      // if(primaryTransaction.contract && primaryTransaction.contract.id !== dto.id){
      //   throw new BadRequestException({
      //     errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.primaryTransactionId)
      //   });
      // }

      if (dto.customer2 && dto.customer2.name) {
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(
          dto.customer2
        );
      }

      if (dto.calcContractPrice) {
        price = primaryTransaction.propertyUnit.contractPrice;
        housePrice = 0;
        landPrice = 0;
      } else {
        price = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.priceVat
          : primaryTransaction.propertyUnit.price;
        housePrice = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.housePriceVat
          : primaryTransaction.propertyUnit.housePrice;
        landPrice = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.landPriceVat
          : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee = {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee:
          primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
      };

      amount = primaryTransaction.amount;
      receipt = primaryTransaction.reciept;
      projectSetting = primaryTransaction.project.setting;
      oldDto.primaryTransaction = primaryTransaction;
    } else {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0005" },
        HttpStatus.OK
      );
    }

    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIdsForContract(
        dto.policyDiscountIds,
        primaryTransaction.project.id
      );
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0007" },
          HttpStatus.OK
        );
      }

      // Chỉ được áp dụng chiết khấu nhà hoặc đất nếu có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (
          policyDiscounts.some(
            (e) =>
              e.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
          )
        ) {
          throw new HttpException(
            { errorCode: "PRIMARYCONTRACT0009" },
            HttpStatus.OK
          );
        }
      } else {
        if (
          policyDiscounts.some(
            (e) =>
              !e.typeRealEstate ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
          )
        ) {
          throw new HttpException(
            { errorCode: "PRIMARYCONTRACT0008" },
            HttpStatus.OK
          );
        }
      }
      oldDto.policyDiscounts = policyDiscounts;
    } else {
      oldDto.policyDiscounts = [];
    }

    oldDto.calcCurrencyFirst = dto.calcCurrencyFirst;
    oldDto.calcPriceVat = dto.calcPriceVat;
    oldDto.calcContractPrice = dto.calcContractPrice;
    oldDto.maintenanceFee = dto.maintenanceFee;
    oldDto.changeInstallment = dto.changeInstallment;
    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findOne({
        id: dto.policyPaymentId,
        type: PolicyTypeEnum.PAYMENT,
        active: true,
        "project.id": primaryTransaction.project.id
      });
      if (!policyPayment) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0010" },
          HttpStatus.OK
        );
      }

      const dataAftertransformPolicyPayment = this.transformPolicyPayment(
        oldDto,
        price,
        projectSetting,
        dto.signedDate,
        receipt,
        policyPayment,
        housePrice,
        landPrice,
        dto.maintenanceFee
      );

      oldDto.policyPayment = dataAftertransformPolicyPayment.policyPayment;
      oldDto.discountValue = dataAftertransformPolicyPayment.discountValue;
      oldDto.issuedPrice = (primaryTransaction.propertyUnit.landPrice && primaryTransaction.propertyUnit.housePrice) ?
        primaryTransaction.propertyUnit.housePrice + primaryTransaction.propertyUnit.landPrice : primaryTransaction.propertyUnit.price;
      oldDto.productPrice = oldDto.policyPayment.schedule.installments.reduce((sum, x) => sum + x.totalAmount, 0);
    }

    oldDto.startDate = dto.startDate;
    oldDto.expiredDate = dto.expiredDate;
    oldDto.signedDate = dto.signedDate;
    oldDto.transferType = dto.transferType;
    oldDto.isDebtRemind = dto.isDebtRemind;
    oldDto.isShowedInstallment = dto.isShowedInstallment;
    oldDto.files = dto.files;
    oldDto.companyInformation = dto.companyInformation;
    oldDto.releaseStartDate = dto.releaseStartDate;
    oldDto.releaseEndDate = dto.releaseEndDate;
    oldDto.salesPolicy = dto.salesPolicy;
    oldDto.staffsInvolved = dto.staffsInvolved;
    oldDto.changeInstallment = dto.changeInstallment;
    oldDto.isAddCoOwnerShipInfo = dto.isAddCoOwnerShipInfo;
    oldDto.isAddCompanyInfo = dto.isAddCompanyInfo;
    oldDto.poNumber = dto.poNumber;
    oldDto.businessArea = dto.businessArea;
    oldDto.distributionChannel = dto.distributionChannel;
    oldDto.productCategory = dto.productCategory;
    oldDto.loanType = dto.loanType;
    oldDto.loanBankInfo = dto.loanBankInfo;
    oldDto.loanAmount = dto.loanAmount;
    oldDto.loanTermYear = dto.loanTermYear;

    if (oldDto.policyPayment) {
      oldDto.paymentPercent = this.calPaymentPercentage(
        {
          installments: oldDto.policyPayment.schedule.installments,
        },
        oldDto
      );
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;

    let result = await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
    // update hợp đồng mua bán
    this.updateMany(
      { "deposit.id": dto.id },
      {
        releaseStartDate: dto.releaseStartDate,
        releaseEndDate: dto.releaseEndDate,
      }
    );

    // Lưu vào lịch sử sản phẩm.
    let histories = {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: dto.reason,
      contractStatus:
        oldDto.status === StatusContractEnum.APPROVED
          ? "Hợp đồng: " + ContractTypeEnum.APPROVED
          : "Hợp đồng: " + ContractTypeEnum.CREATED,
      actionName: ActionContractName.EDIT_CONTRACT,
      modifiedDate: new Date(),
    };
    await this.propertyClient.sendDataPromise(
      histories,
      CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT
    );
    return result || true;
  }

  async updatePurchaseContract(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green("update service"));
    let amount,
      price = 0,
      housePrice = 0,
      landPrice = 0;
    let receipt = [];
    let projectSetting = {};
    const oldDto: any = await this.repository.findOne({ id: dto.id });

    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.NOT_FOUND,
          "purchase",
          "id",
          dto.id
        ),
      });
    }

    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND),
      });
    }

    if (
      oldDto.status === StatusContractEnum.APPROVED &&
      !user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_UPDATE_APPROVEMENT)
    ) {
      throw new BadRequestException(ErrorConst.UNAUTHORIZED);
    }

    const depositDto: any = await this.repository.findOne({
      "deposit.id": dto.depositId,
      type: { $in: [ContractEnum.PURCHASE, ContractEnum.RENT] },
    });
    if (
      dto.deposit &&
      dto.depositId !== dto.deposit.id &&
      depositDto &&
      depositDto.id !== dto.depositId
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.EXISTED,
          "Hợp đồng cọc",
          "id",
          dto.depositId
        ),
      });
    }

    // check trường hợp có  primary transaction
    let primaryTransaction: any;
    if (dto.primaryTransactionId) {
      primaryTransaction = await this.propertyClient.sendDataPromise(
        {
          action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
          id: dto.primaryTransactionId,
        },
        CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID
      );
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(
            ErrorConst.NOT_FOUND,
            "transactionId",
            "id",
            dto.primaryTransactionId
          ),
        });
      }

      // if(primaryTransaction.contract && primaryTransaction.contract.id !== dto.id){
      //   throw new BadRequestException({
      //     errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.primaryTransactionId)
      //   });
      // }

      if (dto.customer2 && dto.customer2.name) {
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(
          dto.customer2
        );
      }

      if (dto.calcContractPrice) {
        price = primaryTransaction.propertyUnit.contractPrice;
        housePrice = 0;
        landPrice = 0;
      } else {
        price = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.priceVat
          : primaryTransaction.propertyUnit.price;
        housePrice = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.housePriceVat
          : primaryTransaction.propertyUnit.housePrice;
        landPrice = dto.calcPriceVat
          ? primaryTransaction.propertyUnit.landPriceVat
          : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee = {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee:
          primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
      };

      amount = primaryTransaction.amount;
      receipt = primaryTransaction.reciept;
      projectSetting = primaryTransaction.project.setting;
      oldDto.primaryTransaction = primaryTransaction;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.NOT_FOUND,
          "transactionId",
          "id",
          "null"
        ),
      });
    }

    price = oldDto.primaryTransaction.propertyUnit.price;

    // validate lại chiết khấu được áp dụng cho hợp đồng mua bán
    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIdsForContract(
        dto.policyDiscountIds,
        primaryTransaction.project.id
      );
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(
            ErrorConst.NOT_FOUND,
            "discountIds",
            "id",
            dto.policyDiscountId
          ),
        });
      }

      // Chỉ được áp dụng chiết khấu nhà hoặc đất nếu có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (
          policyDiscounts.some(
            (e) =>
              e.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
          )
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(
              ErrorConst.DISCOUNT_POLICY_INVALID,
              "discountIds",
              "id",
              dto.policyDiscountId
            ),
          });
        }
      } else {
        if (
          policyDiscounts.some(
            (e) =>
              !e.typeRealEstate ||
              e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
          )
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(
              ErrorConst.DISCOUNT_POLICY_INVALID,
              "discountIds",
              "id",
              dto.policyDiscountId
            ),
          });
        }
      }
      oldDto.policyDiscounts = policyDiscounts;
    } else {
      oldDto.policyDiscounts = [];
    }

    oldDto.calcCurrencyFirst = dto.calcCurrencyFirst;
    oldDto.calcPriceVat = dto.calcPriceVat;
    oldDto.calcContractPrice = dto.calcContractPrice;
    oldDto.maintenanceFee = dto.maintenanceFee; // thay đổi đợt thu phí bảo trì cho hợp đồng mua bán
    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findOne({
        id: dto.policyPaymentId,
        type: PolicyTypeEnum.PAYMENT,
        active: true,
        "project.id": primaryTransaction.project.id
      });
      if (!policyPayment) {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0010" },
          HttpStatus.OK
        );
      }
      // chỉ tính lại khi thay đổi chính sách
      if (dto.policyPaymentId !== oldDto.policyPayment.id) {
        const receiptList = this.getTransferredReceiptList(oldDto);
        const dataAftertransformPolicyPayment = this.transformPolicyPayment(
          oldDto,
          price,
          {},
          dto.signedDate,
          receiptList,
          policyPayment,
          oldDto.primaryTransaction.propertyUnit.housePrice,
          oldDto.primaryTransaction.propertyUnit.landPrice
        );

        oldDto.policyPayment = dataAftertransformPolicyPayment.policyPayment;
        oldDto.discountValue = dataAftertransformPolicyPayment.discountValue;
        oldDto.issuedPrice = (primaryTransaction.propertyUnit.landPrice && primaryTransaction.propertyUnit.housePrice) ?
          primaryTransaction.propertyUnit.housePrice + primaryTransaction.propertyUnit.landPrice : primaryTransaction.propertyUnit.price;
        oldDto.productPrice = oldDto.policyPayment.schedule.installments.reduce((sum, x) => sum + x.totalAmount, 0);
      }
    } else {
      oldDto.policyPayment = null;
    }

    oldDto.deposit = {
      id: dto.depositId,
      code: dto.depositCode,
      name: dto.depositName,
    };
    oldDto.name = `${oldDto.code}-${dto.primaryTransaction.customer.personalInfo.name}`;

    oldDto.signedDate = dto.signedDate;
    oldDto.isDebtRemind = dto.isDebtRemind;
    oldDto.isShowedInstallment = dto.isShowedInstallment;
    oldDto.files = dto.files;
    oldDto.maintenanceFee = dto.maintenanceFee;
    oldDto.releaseStartDate = dto.releaseStartDate;
    oldDto.releaseEndDate = dto.releaseEndDate;
    oldDto.salesPolicy = dto.salesPolicy;
    oldDto.staffsInvolved = dto.staffsInvolved;
    oldDto.changeInstallment = dto.changeInstallment;
    oldDto.isAddCoOwnerShipInfo = dto.isAddCoOwnerShipInfo;
    oldDto.isAddCompanyInfo = dto.isAddCompanyInfo;

    if (oldDto.policyPayment) {
      oldDto.paymentPercent = this.calPaymentPercentage(
        {
          installments: oldDto.policyPayment.schedule.installments,
        },
        oldDto
      );
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;

    await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );

    if (oldDto.deposit && oldDto.deposit.id) {
      // update hợp đồng mua bán
      return await this.updateMany(
        { id: oldDto.deposit.id },
        {
          releaseStartDate: dto.releaseStartDate,
          releaseEndDate: dto.releaseEndDate,
        }
      );
    }
    return true;
  }

  async updateInterestCalculations() {
    const contracts = await this.repository.findOverdueContracts();
    if (!contracts || contracts?.length === 0) return;

    for (let i = 0; i < contracts.length; i++) {
      const isSuccess = await this.updateInterestCalculation(contracts[i])
      if (!isSuccess) continue;
    }
  }

  async updateInterestCalculationsWhenUpdateDebtPenalty(projectId) {
    const contracts = await this.repository.findNotOverdueContracts(projectId);
    if (!contracts || contracts?.length === 0) return;

    for (let i = 0; i < contracts.length; i++) {
      const isSuccess = await this.updateInterestCalculation(contracts[i])
      if (!isSuccess) continue;
    }
  }

  async updateInterestCalculation(
    dto: any, isContractFullInfo: boolean = false, //contract
  ) {
    try {
      let contract: any;
      if (isContractFullInfo) { //tránh lỗi lúc gọi ở đoạn cập nhật phiếu thu
        contract = dto
      }
      else contract = await this.repository.findOne({ id: dto.id });

      if (!contract) return false;
      if (!contract.primaryTransaction?.project) return false;

      const projectInfo = await this.propertyClient.sendDataPromise(
        contract.primaryTransaction.project,
        CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
      );

      let today = new Date();
      let lateDay: number;
      let currentDebtage: any;
      let paymentDue: any;
      // lấy ra các đợt thanh toán trễ hạn
      const pastInstallment = contract.policyPayment.schedule.installments
        .filter(item => {
          const dueDate = new Date(item.paymentDueDate);
          const dueDatePlusOne = new Date(dueDate.getTime() + 24 * 60 * 60 * 1000);
          return dueDatePlusOne <= new Date();
        });

      let currentInstallment = pastInstallment.length === 0 ?
        contract.policyPayment.schedule.installments[0] :
        pastInstallment.reduce((farthest, current) => {
          return new Date(current.paymentDueDate).getTime() < new Date(farthest.paymentDueDate).getTime()
            ? current
            : farthest;
        });

      let currentInterestCalculation = contract.interestCalculations.find(x => x.installmentName === currentInstallment?.name);
      if (currentInterestCalculation && currentInterestCalculation.status === InterestCalculationStatusEnum.transfered) {
        const installments = contract.policyPayment.schedule.installments;
        const currentIndex = installments.findIndex(
          x => x.name === currentInstallment.name
        );

        if (currentIndex !== -1) {
          // Tìm đợt tiếp theo chưa transfered
          let nextInstallment = null;
          for (let i = currentIndex + 1; i < installments.length; i++) {
            const nextCalc = contract.interestCalculations.find(
              x => x.installmentName === installments[i].name
            );
            if (!nextCalc || nextCalc.status !== InterestCalculationStatusEnum.transfered) {
              nextInstallment = installments[i];
              break;
            }
          }

          if (nextInstallment) {
            currentInstallment = nextInstallment;
            currentInterestCalculation = contract.interestCalculations.find(x => x.installmentName === currentInstallment.name);
          } else {
            return null; // Không có đợt nào hợp lệ
          }
        } else {
          return null;
        }
      }

      if (!projectInfo?.setting?.debtage) return false;
      else {
        const ONE_DAY = 1000 * 60 * 60 * 24;
        paymentDue = new Date(currentInstallment.paymentDueDate);
        paymentDue.setDate(paymentDue.getDate() + 1);
        if (projectInfo?.setting?.onlyWorkday) {
          const numberOfSatSun = countWeekendDays(paymentDue, today)
          lateDay = today.getTime() - paymentDue.getTime() + ONE_DAY - (numberOfSatSun * ONE_DAY);
        }
        else
          lateDay = today.getTime() - paymentDue.getTime() + ONE_DAY;
        lateDay = Math.floor(lateDay / ONE_DAY);
        currentDebtage = projectInfo?.setting?.debtage.find(item => {
          if (item.isFinalRange) {
            return lateDay >= item.fromDay;
          }
          return lateDay >= item.fromDay && lateDay <= item.toDay;
        });
      }

      let interestCalculation = new InterestCalculationDto();
      interestCalculation.id = currentInterestCalculation.id;
      interestCalculation.installmentName = currentInstallment.name;
      interestCalculation.isStartCalcInterest = currentDebtage ? true : false;
      interestCalculation.createdDate = new Date();
      interestCalculation.code = currentInterestCalculation.code;
      interestCalculation.title = currentInterestCalculation.title;
      interestCalculation.debtage = contract.isInterestExemption === true ? null : currentDebtage;
      interestCalculation.debtType = (currentDebtage?.isBadDebt === null || currentDebtage?.isBadDebt === undefined) ? null : (currentDebtage.isBadDebt ? DebtTypeEnum.baddebt : DebtTypeEnum.projectdebt);
      interestCalculation.needTransfer = currentInstallment.totalAmount - currentInstallment.totalTransfered;
      interestCalculation.delayFrom = currentDebtage ? paymentDue : null;
      interestCalculation.delayTo = currentDebtage ? new Date() : null;
      interestCalculation.totalDelayDate = currentDebtage ? lateDay : null;
      interestCalculation.interestRate = currentDebtage?.interestRate;
      interestCalculation.interestAmount = contract.isInterestExemption === true ? 0 : this.calculateInterest(projectInfo, currentInstallment, paymentDue, lateDay);
      interestCalculation.interestAmountTransferred = currentInstallment?.receipts?.reduce((sum, receipt) => sum + receipt.amount, 0) || 0;
      interestCalculation.eapStatus = currentInterestCalculation.eapStatus;
      interestCalculation.eapCode = currentInterestCalculation.eapCode;
      interestCalculation.proposal = currentInterestCalculation.proposal;
      interestCalculation.principalAmount = interestCalculation.needTransfer;
      interestCalculation.interestReductionAmountEap = currentInterestCalculation.interestReductionAmountEap;
      interestCalculation.interestReductionAmount = currentInterestCalculation.interestReductionAmount;
      interestCalculation.interestReductionReason = currentInterestCalculation.interestReductionReason;
      interestCalculation.remainingAmount = contract.isInterestExemption === true ? 0 :
        interestCalculation.interestAmount -
        currentInterestCalculation.interestReductionAmount;
      interestCalculation.latePaymentFee = paymentDue > new Date() ? null : (contract.isInterestExemption ? 0 : projectInfo?.setting?.latePaymentFee);
      interestCalculation.totalSettlementAmount = interestCalculation.needTransfer + interestCalculation.remainingAmount + interestCalculation.latePaymentFee;
      interestCalculation.totalSettlementAmount = interestCalculation.totalSettlementAmount < 0 ? 0 : interestCalculation.totalSettlementAmount;
      interestCalculation.custommer = contract.primaryTransaction?.customer;
      interestCalculation.status = InterestCalculationStatusEnum.init;
      interestCalculation.remainingAmount = interestCalculation.remainingAmount > 0 ? interestCalculation.remainingAmount : 0;

      contract.interestCalculations = contract.interestCalculations.filter(item => item.id !== currentInterestCalculation.id);
      contract.interestCalculations.push(interestCalculation);

      this.commandId = uuid.v4();
      await this.executeCommand(
        Action.UPDATE,
        Action.UPDATE,
        this.commandId,
        contract.toObject()
      );
      return true;
    } catch (error) {
      console.log("Error: " + error);
      console.log("Contract Id: " + dto.id);
      return false
    }
  }

  async createInterestCalculations(contract) {
    try {
      let interestCalculations = [];
      const prefix = await GenPrefix(CommonConst.INTEREST_PREFIX, contract.createdBy, this.stsClient);

      const projectInfo = await this.propertyClient.sendDataPromise(
        contract.primaryTransaction.project,
        CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
      );

      let today = new Date();
      let lateDay: number;
      let currentDebtage: any;
      let paymentDue: any;

      // Lấy đợt thanh toán xa nhất chưa thanh toán ở quá khứ
      let currentInstallment = contract.policyPayment.schedule.installments
        .filter(i => new Date(i.paymentDueDate).getTime() < today.getTime()); // Chỉ lấy quá khứ

      currentInstallment = currentInstallment.length === 0 ?
        contract.policyPayment.schedule.installments[0] :
        currentInstallment.reduce((farthest, current) => {
          return new Date(current.paymentDueDate).getTime() < new Date(farthest.paymentDueDate).getTime()
            ? current
            : farthest;
        });

      const currentInterestCalculation = contract.interestCalculations?.find(x => x.installmentName === currentInstallment.name);
      if (currentInterestCalculation && currentInterestCalculation.status === InterestCalculationStatusEnum.transfered) {
        const installments = contract.policyPayment.schedule.installments;
        const currentIndex = installments.findIndex(
          x => x.name === currentInstallment.name
        );

        if (currentIndex !== -1) {
          // Tìm đợt tiếp theo chưa transfered
          let nextInstallment = null;
          for (let i = currentIndex + 1; i < installments.length; i++) {
            const nextCalc = contract.interestCalculations.find(
              x => x.installmentName === installments[i].name
            );
            if (!nextCalc || nextCalc.status !== InterestCalculationStatusEnum.transfered) {
              nextInstallment = installments[i];
              break;
            }
          }

          if (nextInstallment) {
            currentInstallment = nextInstallment;
          } else {
            return null; // Không có đợt nào hợp lệ
          }
        } else {
          return null;
        }
      }


      if (!projectInfo?.setting?.debtage) return false;
      else {
        paymentDue = new Date(currentInstallment.paymentDueDate);
        paymentDue.setDate(paymentDue.getDate() + 1);
        if (projectInfo?.setting?.onlyWorkday) {
          const numberOfSatSun = countWeekendDays(paymentDue, today)
          lateDay = today.getTime() - paymentDue.getTime() + this.ONE_DAY - (numberOfSatSun * this.ONE_DAY);
        }
        else lateDay = today.getTime() - paymentDue.getTime() + this.ONE_DAY;
        lateDay = Math.floor(lateDay / this.ONE_DAY);
        currentDebtage = projectInfo?.setting?.debtage.find(item => {
          if (item.isFinalRange) {
            return lateDay >= item.fromDay;
          }
          return lateDay >= item.fromDay && lateDay <= item.toDay;
        });
      }

      let interestCalculation = new InterestCalculationDto();
      interestCalculation.id = uuid.v4();
      interestCalculation.installmentName = currentInstallment.name;
      interestCalculation.isStartCalcInterest = currentDebtage ? true : false;
      interestCalculation.createdDate = new Date();
      interestCalculation.code = await this.codeGenerateService.generateCode2("", prefix);
      interestCalculation.title = interestCalculation.code + "/" + contract.name;
      interestCalculation.debtage = currentDebtage;
      interestCalculation.debtType = (currentDebtage?.isBadDebt === null || currentDebtage?.isBadDebt === undefined) ? null : (currentDebtage.isBadDebt ? DebtTypeEnum.baddebt : DebtTypeEnum.projectdebt);
      interestCalculation.needTransfer = currentInstallment.totalAmount - currentInstallment.totalTransfered;
      interestCalculation.delayFrom = paymentDue > new Date() ? null : paymentDue;
      interestCalculation.delayTo = paymentDue > new Date() ? null : new Date();
      interestCalculation.totalDelayDate = currentDebtage ? lateDay : null;
      interestCalculation.interestRate = currentDebtage?.interestRate;
      interestCalculation.principalAmount = interestCalculation.needTransfer;
      interestCalculation.interestAmount = paymentDue > new Date() ? null : this.calculateInterest(projectInfo, currentInstallment, paymentDue, lateDay);
      interestCalculation.interestAmountTransferred = currentInstallment?.receipts?.reduce((sum, receipt) => sum + receipt.amount, 0) || 0;
      interestCalculation.interestReductionAmount = null;
      interestCalculation.remainingAmount = paymentDue > new Date() ? null :
        interestCalculation.interestAmount -
        interestCalculation.interestReductionAmount;
      interestCalculation.latePaymentFee = paymentDue > new Date() ? null : (contract.isInterestExemption ? 0 : projectInfo?.setting?.latePaymentFee);
      interestCalculation.totalSettlementAmount = interestCalculation.needTransfer + interestCalculation.remainingAmount + interestCalculation.latePaymentFee;
      interestCalculation.status = InterestCalculationStatusEnum.init;
      interestCalculation.custommer = contract.primaryTransaction?.customer;
      interestCalculation.remainingAmount = interestCalculation.remainingAmount > 0 ? interestCalculation.remainingAmount : 0;
      interestCalculations.push(interestCalculation);
      return interestCalculations;
    } catch (error) {
      return [];
    }
  }

  async approveInterestCalculation(
    user: any,
    dto: UpdateInterestCalculationDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "approve interest calculation");
    this.commandId = uuid.v4();
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0004" },
        HttpStatus.OK
      );
    }

    if (!dto.interestCalculation.id) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0023" },
        HttpStatus.OK
      );
    }

    let idx = oldDto.interestCalculations.findIndex(
      (i) => i.id === dto.interestCalculation.id
    );
    if (idx > -1) {
      if (oldDto.interestCalculations[idx].status == InterestCalculationStatusEnum.init) {
        oldDto.interestCalculations[idx].status = InterestCalculationStatusEnum.transfered;
        oldDto.interestCalculations[idx].transferedDate = new Date();
        oldDto.interestCalculations[idx].transferedBy = user.id;
      } else {
        throw new HttpException(
          { errorCode: "PRIMARYCONTRACT0024" },
          HttpStatus.OK
        );
      }
    }

    const nextInterestCalculation: any = await this.createInterestCalculations(oldDto);
    if (Array.isArray(nextInterestCalculation)) {
      oldDto.interestCalculations = [
        ...oldDto.interestCalculations,
        ...nextInterestCalculation,
      ];
    }
    this.commandId = uuid.v4();
    await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
    return true;
  }

  async updatePropertyUnits(units: any[]) {
    const contracts = await this.repository.find({
      "primaryTransaction.propertyUnit.id": {
        $in: units.map((e) => e.id),
      },
    });

    const update = contracts.map(async (contract: any) => {
      const newUnit = units.find(
        (e) => e.id === contract.primaryTransaction.propertyUnit.id
      );
      Object.assign(contract.primaryTransaction.propertyUnit, newUnit);
      if (
        contract?.policyPayment?.schedule?.installments &&
        contract?.policyPayment?.schedule?.installments?.length
      ) {
        if (
          contract.maintenanceFee &&
          contract.maintenanceFee.type === DiscountTypeEnum.PERCENT
        ) {
          let price = 0,
            housePrice = 0,
            landPrice = 0;
          if (contract.calcContractPrice) {
            price = contract.primaryTransaction.propertyUnit.contractPrice;
            housePrice = 0;
            landPrice = 0;
          } else {
            price = contract.calcPriceVat
              ? contract.primaryTransaction.propertyUnit.priceVat
              : contract.primaryTransaction.propertyUnit.price;
            housePrice = contract.calcPriceVat
              ? contract.primaryTransaction.propertyUnit.housePriceVat
              : contract.primaryTransaction.propertyUnit.housePrice;
            landPrice = contract.calcPriceVat
              ? contract.primaryTransaction.propertyUnit.landPriceVat
              : contract.primaryTransaction.propertyUnit.landPrice;
          }

          const policyPayment = await this.policyQueryService.findOne({
            id: contract.policyPayment?.id,
            type: PolicyTypeEnum.PAYMENT,
          });
          if (policyPayment) {
            const newPaymentPolicy = (this.transformPolicyPayment(
              contract,
              price,
              contract.primaryTransaction.project.setting,
              contract.signedDate,
              [],
              policyPayment,
              housePrice,
              landPrice,
              {
                ...contract.maintenanceFee.toJSON(),
                contractPriceForMaintenanceFee:
                  newUnit.contractPriceForMaintenanceFee,
              }
            )).policyPayment;

            for (
              let index = 0;
              index < contract.policyPayment.schedule.installments.length;
              index++
            ) {
              const element =
                contract.policyPayment.schedule.installments[index];
              element.totalAmount =
                newPaymentPolicy.schedule.installments[index].totalAmount;
            }
          }
        }
      }

      return this.repository.update(contract);
    });
    await Promise.all(update);
  }

  async updateManyPrimaryContract(
    user: any,
    dto: UpdateManyPrimaryContract,
    actionName: string
  ) {
    this.loggerService.log(this.context, clc.green("update service"));
    if (dto.lstIdPrimaryContract && dto.lstIdPrimaryContract.length > 0) {
      await this.updateMany(
        { id: { $in: dto.lstIdPrimaryContract } },
        {
          releaseStartDate: dto.startDate,
          releaseEndDate: dto.endDate,
        }
      );
      // update hợp đồng mua bán
      return await this.updateMany(
        { "deposit.id": { $in: dto.lstIdPrimaryContract } },
        {
          releaseStartDate: dto.startDate,
          releaseEndDate: dto.endDate,
        }
      );
    }
  }

  public transformPolicyPayment(
    contract,
    price,
    projectSetting,
    signedDate,
    receiptList,
    newPolicyPayment: any = {},
    housePrice = 0,
    landPrice = 0,
    maintenanceFee: any = {}
  ) {
    // Kiểm tra số đợt thanh toán luôn phải >= index của đợt thanh toán cuối cùng có receipt (không có receipt thì là -1)
    if (
      (contract.policyPayment?.schedule?.installments || []).reduce(
        (acc, curr, index) =>
          (acc = curr.receipts && curr.receipts.length ? index : acc),
        -1
      ) > (newPolicyPayment?.schedule?.installments?.length || 0)
    ) {
      throw new HttpException(
        { errorCode: "PRIMARYCONTRACT0021" },
        HttpStatus.OK
      );
    }

    let priceBeforeDiscount = price;
    let housePriceBeforeDiscount = housePrice;
    let landPriceBeforeDiscount = landPrice;

    // check chiết khấu
    if (contract.policyDiscounts && contract.policyDiscounts.length) {
      // Trường hợp không có giá nhà & đất => áp dụng chính sách chiết khấu mặc định
      if (price > 0 && !housePrice && !landPrice) {
        let currencyValue = contract.policyDiscounts
          .filter(
            (e) =>
              e.typeDiscount === DiscountTypeEnum.CURRENCY &&
              e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
          )
          .reduce((acc, curr) => acc + parseFloat(curr.value), 0);
        let percentValue = contract.policyDiscounts
          .filter(
            (e) =>
              e.typeDiscount === DiscountTypeEnum.PERCENT &&
              e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
          )
          .reduce((acc, curr) => acc + parseFloat(curr.value), 0);

        if (contract.calcCurrencyFirst) {
          price -= currencyValue;
          price -= (percentValue / 100) * price;
        } else {
          price -= (percentValue / 100) * price;
          price -= currencyValue;
        }
      } else {
        let currencyValueHouse = contract.policyDiscounts
          .filter(
            (e) =>
              e.typeDiscount === DiscountTypeEnum.CURRENCY &&
              e.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE
          )
          .reduce((acc, curr) => acc + parseFloat(curr.value), 0);
        let percentValueHouse = contract.policyDiscounts
          .filter(
            (e) =>
              e.typeDiscount === DiscountTypeEnum.PERCENT &&
              e.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE
          )
          .reduce((acc, curr) => acc + parseFloat(curr.value), 0);

        let currencyValueLand = contract.policyDiscounts
          .filter(
            (e) =>
              e.typeDiscount === DiscountTypeEnum.CURRENCY &&
              e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
          )
          .reduce((acc, curr) => acc + parseFloat(curr.value), 0);
        let percentValueLand = contract.policyDiscounts
          .filter(
            (e) =>
              e.typeDiscount === DiscountTypeEnum.PERCENT &&
              e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
          )
          .reduce((acc, curr) => acc + parseFloat(curr.value), 0);

        if (contract.calcCurrencyFirst) {
          housePrice -= currencyValueHouse;
          housePrice -= (percentValueHouse / 100) * housePrice;

          landPrice -= currencyValueLand;
          landPrice -= (percentValueLand / 100) * landPrice;
        } else {
          housePrice -= (percentValueHouse / 100) * housePrice;
          housePrice -= currencyValueHouse;

          landPrice -= (percentValueLand / 100) * landPrice;
          landPrice -= currencyValueLand;
        }
      }

      // check trường hợp nhập nhầm chiết khấu quá cao khiến giá bị âm
      if (price < 0) price = 0;
      if (housePrice < 0) housePrice = 0;
      if (landPrice < 0) landPrice = 0;
    }
    let discountValue = (landPrice && housePrice) ? (housePriceBeforeDiscount - housePrice) + (landPriceBeforeDiscount - landPrice) : priceBeforeDiscount - price;
    let totalCurrencyBeforeToContract = 0;
    signedDate = signedDate ? moment(signedDate) : moment();
    let nextDate = moment();
    let goContractIdx = newPolicyPayment.schedule.installments.length;
    let totalAmount = 0;

    // không update lại đợt thanh toán nếu update và changeInstallment = true
    if (contract.id && contract.changeInstallment) {
      return { policyPayment: contract.policyPayment, discountValue }
    }

    const hasMaintenanceFromErp = newPolicyPayment.schedule.installments.some(
      (i) => i.name === "99"
    );

    newPolicyPayment.schedule.installments.map((i, index) => {
      if (i.isToContract) {
        goContractIdx = index;
      }

      if ((housePrice > 0 || landPrice > 0) && !i.type2) {
        i.type2 = i.type;
        i.value2 = i.value;
      }

      // Trường hợp tách thanh toán nhà & đất
      if (i.type2) {
        // kiểm tra đã trừ đi tiền mặt đã đóng các đợt trước
        let flagSubTotalCurrencyBeforeToContract = false;
        i.totalAmount = 0;
        if (i.type === ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount += i.value;
          totalCurrencyBeforeToContract += i.value;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount += i.isToContract
            ? (i.value * housePrice) / 100 - totalCurrencyBeforeToContract
            : (i.value * housePrice) / 100;
          flagSubTotalCurrencyBeforeToContract = i.isToContract;
        }
        if (i.type2 === ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount += i.value2;
          totalCurrencyBeforeToContract += i.value2;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount +=
            i.isToContract && !flagSubTotalCurrencyBeforeToContract
              ? (i.value2 * landPrice) / 100 - totalCurrencyBeforeToContract
              : (i.value2 * landPrice) / 100;
        }
      } else {
        if (i.type === ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount = i.value;
          totalCurrencyBeforeToContract += i.value;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount = i.isToContract
            ? (i.value * price) / 100 - totalCurrencyBeforeToContract
            : (i.value * price) / 100;
        }
      }

      if (
        (index === newPolicyPayment.schedule.installments.length - 1 &&
          !hasMaintenanceFromErp) ||
        (hasMaintenanceFromErp &&
          index === newPolicyPayment.schedule.installments.length - 2)
      ) {
        i.totalAmount =
          (landPrice + housePrice > 0 ? landPrice + housePrice : price) -
          totalAmount;
      } else {
        i.totalAmount = Math.round(i.totalAmount);
        totalAmount += i.totalAmount;
      }

      if (
        maintenanceFee.stagePayment &&
        maintenanceFee.stagePayment == i.name &&
        maintenanceFee.type &&
        maintenanceFee.value
      ) {
        i.name = `${i.name} (Bao gồm Phí bảo trì)`;
        if (maintenanceFee.type === DiscountTypeEnum.CURRENCY) {
          i.totalAmount += maintenanceFee.value;
        } else if (maintenanceFee.type === DiscountTypeEnum.PERCENT) {
          const maintenanceValue: any =
            (maintenanceFee.contractPriceForMaintenanceFee *
              maintenanceFee.value) /
            100;
          i.totalAmount += Math.round(maintenanceValue);
        }
      }

      i.totalTransfered = 0;
      i.receipts = [];
      let needTransferred = i.totalAmount - i.totalTransfered;

      // khi thay đổi chính sách thanh toán, đem các phiếu thu đã thanh toán sang chính sách mới
      if (receiptList.length > 0) {
        receiptList.map((r, idx) => {
          if (needTransferred > 0 && r.amount > 0) {
            if (r.amount <= needTransferred) {
              i.receipts.push(Object.assign({}, { ...r }));
              i.totalTransfered += r.amount;
              needTransferred -= r.amount;
              receiptList[idx].amount = 0;
            } else {
              receiptList[idx].amount = r.amount - needTransferred;
              i.receipts.push(
                Object.assign({}, { ...r }, { amount: needTransferred })
              );
              i.totalTransfered += needTransferred;
              needTransferred = 0;
            }
          }
        });
      }

      if (contract.type === ContractEnum.DEPOSIT) {
        if (index === 0) {
          // tính ngày hạn thanh toán của Đợt 1
          if (i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            i.paymentDueDate = moment(i.exactDays).toDate();
            nextDate = moment(i.exactDays);
          } else {
            const projectBonusDate = projectSetting.dateToIssue || 0;
            i.paymentDueDate =
              projectBonusDate !== 0
                ? signedDate.add(projectBonusDate, "days").toDate()
                : signedDate.toDate();
            nextDate = signedDate.clone();
          }
        } else {
          // tính ngày hạn thanh toán của các đợt khác
          if (i.expiredDateType !== expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            nextDate = nextDate.add(i.expiredDays, "days");
            i.paymentDueDate = nextDate.toDate();
          } else if (
            i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT
          ) {
            nextDate = moment(i.exactDays);
            i.paymentDueDate = nextDate.toDate();
          }
        }
      } else {
        // HĐ không phải HĐ cọc
        if (index > goContractIdx) {
          if (index === goContractIdx + 1) {
            // tính ngày hạn thanh toán của Đợt tiếp sau khi ra HĐMB
            if (i.expiredDateType !== expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate = nextDate.add(i.expiredDays, "days");
              i.paymentDueDate = nextDate.toDate();
            } else if (
              i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT
            ) {
              nextDate = moment(i.exactDays);
              i.paymentDueDate = nextDate.toDate();
            }
          } else {
            // tính ngày hạn thanh toán của các đợt khác
            if (i.expiredDateType !== expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate = nextDate.add(i.expiredDays, "days");
              i.paymentDueDate = nextDate.toDate();
            } else if (
              i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT
            ) {
              nextDate = moment(i.exactDays);
              i.paymentDueDate = nextDate.toDate();
            }
          }
        } else {
          // lấy hạn thanh toán của chính sách cũ
          i.paymentDueDate =
            contract.policyPayment.schedule.installments[index].paymentDueDate;
        }
      }

      return i;
    });

    // Nếu HĐ đã duyệt, chuyển hết phiếu thu cũ sang các đợt tương ứng
    (contract.policyPayment?.schedule?.installments || []).forEach(
      (element, index) => {
        if (element.receipts && element.receipts.length) {
          newPolicyPayment.schedule.installments[index].receipts =
            element.receipts;
          newPolicyPayment.schedule.installments[index].totalTransfered =
            element.totalTransfered;
        }
      }
    );

    return { policyPayment: newPolicyPayment, discountValue };
  }

  private getTransferredReceiptList(contract) {
    // lấy các phiếu thu đã duyệt
    let transferredReceipt = [];
    contract.policyPayment.schedule.installments.map((i, index) => {
      i.receipts = i.receipts || [];
      i.receipts.map((r) => {
        if (r.status === "TRANSFERED") {
          const idx = transferredReceipt.findIndex((re) => re.id === r.id);
          if (idx !== -1) {
            transferredReceipt[idx].amount += r.amount;
          } else {
            transferredReceipt.push(Object.assign({}, { ...r }));
          }
        }
      });
    });
    return transferredReceipt;
  }

  async deleteContract(user: any, id: string, actionName: string) {
    this.loggerService.log(this.context, clc.green("delete service"));
    const oldDto: any = await this.repository.findOne({ id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", id),
      });
    }
    if (oldDto.status !== StatusContractEnum.INIT) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus"),
      });
    }
    let model: any = { id };

    if (oldDto.primaryTransaction) {
      model.primaryTransactionId = oldDto.primaryTransaction.id;
    }

    if (oldDto.deposit) {
      await this.repository.updateDeleteFieldPurchase(oldDto.deposit.id);
    }

    this.commandId = uuid.v4();
    model.modifiedBy = user.id;

    await this.executeCommand(
      Action.DELETE,
      actionName,
      this.commandId,
      model
    );
    return true;
  }

  async deleteInterestCalculation(
    user: any,
    dto: UpdateInterestCalculationDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, clc.green("delete service"));
    const oldDto: any = await this.repository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id),
      });
    }

    if (!dto.interestCalculation.id) {
      throw new BadRequestException({
        errors: ErrorConst.Error(
          ErrorConst.INVALID,
          "interest",
          "ID",
          dto.interestCalculation.id
        ),
      });
    }

    let idx = oldDto.interestCalculations.findIndex(
      (i) => i.id === dto.interestCalculation.id
    );
    if (idx > -1) {
      if (
        oldDto.interestCalculations[idx].status ==
        InterestCalculationStatusEnum.init
      ) {
        oldDto.interestCalculations.splice(idx, 1);
      } else {
        throw new BadRequestException({
          errors: ErrorConst.Error(
            ErrorConst.INVALID_INPUT,
            "interest calculation status"
          ),
        });
      }
    }

    this.commandId = uuid.v4();
    return await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto.toObject()
    );
  }

  async syncContractTransaction(transaction: any) {
    const contract: any = await this.repository.findOne({
      id: transaction.contractId,
    });
    if (
      !contract ||
      !contract.policyPayment ||
      !contract.policyPayment.schedule
    ) {
      return null;
    }
    let updatePropertyContract = false;
    let installments = contract.policyPayment.schedule.installments || [];
    let interestCalculations = contract.interestCalculations || [];
    if (installments.length > 0) {
      const existedInstallmentIndex = (
        contract.policyPayment.schedule.installments as Array<any>
      ).findIndex((item) => item.name === transaction.paymentBatch);
      const existedInstallmentToContractIndex = (
        contract.policyPayment.schedule.installments as Array<any>
      ).findIndex((item) => item.isToContract === true);
      // transform lại data
      transaction.amount = transaction.money;
      transaction.receiptNum = transaction.code;
      transaction.receiptDate = transaction.collectMoneyDate;
      const simpleTransaction: any = (({
        id,
        amount,
        code,
        status,
        type,
        receiptNum,
        receiptDate,
      }) => ({ id, amount, code, status, type, receiptNum, receiptDate }))(
        transaction
      );
      if (existedInstallmentIndex !== -1) {
        // Chọn Đợt
        // lưu phiếu thu vào Đợt
        // check tồn tại phiếu thu cùng id chưa
        const existedTransactionIndex = installments[
          existedInstallmentIndex
        ].receipts.findIndex((item) => item.id === transaction.id);
        if (existedTransactionIndex === -1) {
          // chưa tồn tại, thêm mới
          installments[existedInstallmentIndex].receipts.push(
            Object.assign({}, simpleTransaction)
          );
        } else {
          // reject phiếu đã thu
          if (
            simpleTransaction.status === TransactionStatusEnum.processing &&
            installments[existedInstallmentIndex].receipts[
              existedTransactionIndex
            ].status === TransactionStatusEnum.transfered
          ) {
            installments[existedInstallmentIndex].totalTransfered -=
              simpleTransaction.amount;
          }
          // đã tồn tại, update trạng thái
          installments[existedInstallmentIndex].receipts[
            existedTransactionIndex
          ].status = simpleTransaction.status;
        }
        // điều chỉnh số tiền đã thu, chỉ khi đã duyệt
        if (simpleTransaction.status === TransactionStatusEnum.transfered) {
          installments[existedInstallmentIndex].totalTransfered =
            installments[existedInstallmentIndex].totalTransfered || 0;
          installments[existedInstallmentIndex].totalTransfered +=
            transaction.amount;
          if (contract.type === ContractEnum.DEPOSIT) {
            // HĐ cọc
            // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
            if (
              existedInstallmentIndex === 0 &&
              installments[0].totalTransfered >= installments[0].totalAmount
            ) {
              contract.status = contract.isTransferred
                ? contract.status
                : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          } else {
            // HDMB/Thuê
            // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
            if (
              existedInstallmentIndex === existedInstallmentToContractIndex &&
              installments[existedInstallmentToContractIndex].totalTransfered >=
              installments[existedInstallmentToContractIndex].totalAmount
            ) {
              contract.status = contract.isTransferred
                ? contract.status
                : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
              contract.transactionSuccess = true;
            }
          }
          const installment = installments.find(x => x.transactionSuccessful === true);
          if (installment && installment.totalTransfered >= installment.totalAmount && contract.status === StatusContractEnum.APPROVED) contract.transactionSuccess = true;
          // kiểm tra có thanh toán lãi hay không.
          // if (transaction.isInterest) {
          //   const ids = transaction.interestCalculations.map((i) => i.id);
          //   contract.interestCalculations = interestCalculations.map((i) => {
          //     if (
          //       ids.includes(i.id) &&
          //       i.installmentName === transaction.paymentBatch
          //     ) {
          //       i.status = InterestCalculationStatusEnum.transfered;
          //       i.receipts = simpleTransaction;
          //     }
          //     return i;
          //   });
          // }
          if (transaction.amount !== 0) {
            // gửi thông báo thanh toán thành công CarePlus
            const customer = await this.careClient.sendDataPromise(
              {
                identityNumber:
                  contract.primaryTransaction.customer.identities[0].value,
              },
              CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY
            );

            if (customer && customer.id) {
              this.notificationClient.createNotificationCare(
                "care_PrimaryContract_InstallmentPaymentSuccess",
                null,
                customer.id,
                "primary-contract",
                contract.id,
                {
                  installment: existedInstallmentIndex + 1,
                  productCode: contract.primaryTransaction.propertyUnit.code,
                  projectCode: contract.primaryTransaction.project.code,
                  amount: transaction.amount
                    .toLocaleString()
                    .split(",")
                    .join("."),
                  receiptCode: simpleTransaction.code,
                }
              );
            }
          }
        }
      } else {
        // Đợt "Khác"
        let money = transaction.money;
        installments = installments.map((i, idx) => {
          i.totalTransfered = i.totalTransfered || 0;
          // Nếu đợt chưa thanh toán đủ tiền
          if (money > 0 && i.totalAmount > i.totalTransfered) {
            // số tiền cần thanh toán
            const needTransfer = i.totalAmount - i.totalTransfered;
            // số tiền có thể thanh toán
            const canTransfer = money >= needTransfer ? needTransfer : money;
            // điều chỉnh đúng số tiền và lưu phiếu thu vào Đợt
            simpleTransaction.amount = canTransfer;
            // check tồn tại phiếu thu cùng id chưa
            const existedTransactionIndex = i.receipts.findIndex(
              (item) => item.id === transaction.id
            );
            if (existedTransactionIndex === -1) {
              // chưa tồn tại, thêm mới
              i.receipts.push(Object.assign({}, simpleTransaction));
            } else {
              // reject phiếu đã thu
              if (
                simpleTransaction.status === TransactionStatusEnum.processing &&
                i.receipts[existedTransactionIndex].status ===
                TransactionStatusEnum.transfered
              ) {
                i.totalTransfered -= canTransfer;
              }
              // đã tồn tại, update trạng thái
              i.receipts[existedTransactionIndex].status =
                simpleTransaction.status;
            }
            // điều chỉnh số tiền đã thu, chỉ khi đã duyệt
            if (simpleTransaction.status === TransactionStatusEnum.transfered) {
              i.totalTransfered += canTransfer;
            }
            money -= canTransfer;
          }

          if (contract.type === ContractEnum.DEPOSIT) {
            // HĐ cọc
            // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
            if (idx === 0 && i.totalTransfered >= i.totalAmount) {
              contract.status = contract.isTransferred
                ? contract.status
                : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          } else {
            // HDMB/Thuê
            // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
            if (
              idx === existedInstallmentToContractIndex &&
              i.totalTransfered >= i.totalAmount
            ) {
              contract.status = contract.isTransferred
                ? contract.status
                : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          }
          return i;
        });
        const installment = installments.find(x => x.transactionSuccessful === true);
        if (installment && installment.totalTransfered >= installment.totalAmount && contract.status === StatusContractEnum.APPROVED) contract.transactionSuccess = true;
        if (
          simpleTransaction.status === TransactionStatusEnum.transfered &&
          transaction.amount !== 0
        ) {
          // gửi thông báo thanh toán thành công CarePlus
          const customer = await this.careClient.sendDataPromise(
            {
              identityNumber:
                contract.primaryTransaction.customer.identities[0].value,
            },
            CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY
          );

          if (customer && customer.id) {
            this.notificationClient.createNotificationCare(
              "care_PrimaryContract_MultiPaymentSuccess",
              null,
              customer.id,
              "primary-contract",
              contract.id,
              {
                productCode: contract.primaryTransaction.propertyUnit.code,
                projectCode: contract.primaryTransaction.project.code,
                amount: transaction.amount
                  .toLocaleString()
                  .split(",")
                  .join("."),
                receiptCode: simpleTransaction.code,
              }
            );
          }
        }
      }
      // reverse lại đúng thứ tự và save
      contract.policyPayment.schedule.installments = installments;
      if (contract.deposit) {
        // nếu là HĐMB, update Lịch sử thanh toán cho HĐC
        const depositContract = await this.repository.findOne({
          id: contract.deposit.id,
        });
        // chỉ update khi cùng Chính sách thanh toán
        if (
          depositContract &&
          depositContract.policyPayment &&
          depositContract.policyPayment.id === contract.policyPayment.id
        ) {
          depositContract.policyPayment = contract.policyPayment;
        }
        if (contract.status === StatusContractEnum.APPROVED) {
          depositContract.purchase = {
            id: contract.id,
            code: contract.code,
            name: contract.name,
          };
          depositContract.isDebtRemind = false;
        }
        this.executeCommand(
          Action.UPDATE,
          null,
          this.commandId,
          depositContract
        );
      }

      contract.paymentPercent = this.calPaymentPercentage(
        {
          installments: contract.policyPayment.schedule.installments,
        },
        contract
      );
    }

    await this.executeCommand(Action.UPDATE, null, this.commandId, contract);
    await this.updateInterestCalculation(contract, true);
    if (updatePropertyContract) {
      // update thông tin contract vào sản phẩm
      const propertyUnitId = contract.primaryTransaction.propertyUnit.id;
      this.propertyClient.sendDataPromise(
        {
          id: propertyUnitId,
          contract: {
            id: contract.id,
            code: contract.code,
            type: contract.type,
            isTransferred: contract.isTransferred,
          },
        },
        CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
          .UPDATE_CONTRACT_IN_PROPERTY_UNIT
      );
    }
    return contract;
  }

  calPaymentPercentage(params: { installments: any[] }, contract?) {
    const { installments = [] } = params;

    // cal total amount
    const price = installments.reduce(function (prev, cur) {
      return prev + cur.totalAmount;
    }, 0);

    let totalPercentage = 0;
    let currentTotalTransfered = 0;

    let resetedTotalPercent = false;
    for (let index = 0; index < installments.length; index++) {
      const inst = installments[index];
      currentTotalTransfered =
        currentTotalTransfered + Number(inst.totalTransfered);

      if (
        inst.totalTransfered < inst.totalAmount ||
        inst.type == "currency" ||
        inst.totalAmount === 0
      ) {
        if (inst.type === "percent" && index > 0) {
          const oldInstallments = installments.slice(0, index + 1);

          let remainAmount = oldInstallments.reduce(function (prev, cur) {
            return prev + cur.totalTransfered - +cur.totalAmount;
          }, 0);
          remainAmount = remainAmount > 0 ? remainAmount : 0;
          resetedTotalPercent = true;
          if (inst.totalTransfered + remainAmount < inst.totalAmount) {
            totalPercentage =
              price !== 0
                ? totalPercentage +
                Math.round(
                  (Number(inst.totalTransfered + remainAmount) * 100) / price
                )
                : 0;
          } else {
            totalPercentage = totalPercentage + Number(inst.value);
          }
        } else {
          totalPercentage =
            price !== 0
              ? totalPercentage +
              Math.round((Number(inst.totalTransfered) * 100) / price)
              : 0;
        }
      } else {
        if (resetedTotalPercent) {
          totalPercentage = totalPercentage + Number(inst.value);
        } else {
          resetedTotalPercent = true;
          totalPercentage = totalPercentage + Number(inst.value);
        }
      }
    }

    if (installments.length === 1) {
      totalPercentage =
        price !== 0
          ? totalPercentage + Math.round((currentTotalTransfered * 100) / price)
          : 0;
    }

    // Đồng bộ % thanh toán hợp đồng => sản phẩm
    this.syncTotalPaymentToProperty(totalPercentage, contract);

    return totalPercentage;
  }

  // Đồng bộ % thanh toán hợp đồng  => sản phẩm
  async syncTotalPaymentToProperty(totalPercentage, contract) {
    let updatePropery = {
      query: { id: contract.primaryTransaction.propertyUnit.id },
      model: { $set: { totalPercentageContract: totalPercentage } },
    };
    this.propertyClient.sendDataPromise(
      updatePropery,
      CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY
    );
  }

  async sendDeliveryNotify(
    user,
    dto: SendPrimaryContractDeliveryNotifyDto,
    actionName
  ) {
    this.loggerService.log(this.context, "send Delivery Notify");

    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({
      id: dto.id,
      status: true,
    });

    if (!handOverSetting) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Thiết lập"),
      });
    }

    const project = await this.propertyClient.sendDataPromise(
      { id: handOverSetting.project.id },
      CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
    );

    if (!project) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, "Dự án"),
      });
    }

    let contracts = dto.contracts;
    const ids = contracts.map((contract) => contract.id);
    const notifyData = [];
    const mailerData = [];

    // Người dùng chỉ có thể chọn các căn có trạng thái "Đã lên lịch"
    const listContractSchedule = await this.repository.find({
      id: { $in: ids },
      handoverStatus: HandoverStatusEnum.scheduled,
    });
    const listContractScheduleIds = listContractSchedule.map((x) => x.id);
    contracts = contracts.filter((x) => listContractScheduleIds.includes(x.id));

    for (let contract of contracts) {
      const time = contract.deliveryDate
        ? moment(contract.deliveryDate).format("HH:mm")
        : "";
      const date = contract.deliveryDate
        ? moment(contract.deliveryDate).format("DD/MM/YYYY")
        : "";
      const { code, identityNumber, customerName } = contract;

      const session = handOverSetting.sessions.find(
        (ses) => ses.name === contract.session && ses.status
      );
      const smsTemplate = handOverSetting ? handOverSetting.smsTemplate : "";
      const smsBrandName = handOverSetting ? handOverSetting.smsBrandName : "";
      const emailTitle = handOverSetting ? handOverSetting.emailTitle : "";
      const emailFrom = handOverSetting ? handOverSetting.emailFrom : "";
      const emailCC = handOverSetting ? handOverSetting.emailCC : "";
      let emailBCC = handOverSetting ? handOverSetting.emailBCC : "";
      const emailTemplate =
        handOverSetting && handOverSetting.emailTemplate
          ? handOverSetting.emailTemplate.replace(/(?:\r\n|\r|\n)/g, "<br>")
          : "";
      const hotline = handOverSetting.hotline ? handOverSetting.hotline : "";
      const investorName = project.careInvestor
        ? project.careInvestor.name
        : "";
      const investorAddress = project.careInvestor
        ? project.careInvestor.address
        : "";
      const investorWebsite = project.careInvestor
        ? project.careInvestor.website
        : "";

      // gửi noti cho app
      this.notificationClient.createNotificationCare(
        "care_deliveryReminderSend",
        null,
        identityNumber,
        "primary-contract",
        contract.id,
        { code, time, date, hotline, identityNumber }
      );

      notifyData.push({
        phone: contract.phone,
        contentSMS: smsTemplate,
        brandName: smsBrandName,
        code,
        time,
        date,
        hotline,
        customerName,
      });

      // Thông tin nhân viên bàn giao
      let handoverSchedule =
        await this.handoverScheduleQueryService.findOneByQuery({
          "handoverApartment.id": contract.id,
        });
      if (handoverSchedule) {
        emailBCC = `${emailBCC};${handoverSchedule.supportEmployee.email}`;
      }

      mailerData.push({
        email: contract.email,
        emailTitle,
        emailFrom,
        emailCC,
        emailBCC,
        contentMail: emailTemplate,
        code,
        time,
        date,
        hotline,
        investorName,
        investorAddress,
        investorWebsite,
        customerName,
      });
    }

    // gửi SMS
    console.log("SEND_SMS_DELIVERY", notifyData);
    this.notificationClient.sendDeliverySMS({ notifyData }, user);

    //gửi email
    console.log("SEND_MAIL_DELIVERY", mailerData);
    this.mailerClient.sendDataPromise(
      { mailerData },
      CmdPatternConst.MAILER.SEND_MAIL_DELIVERY,
      user
    );

    // update trạng thái đã gửi thông báo
    return await this.updateMany(
      { id: { $in: ids } },
      {
        isSendDelivery: true,
        modifiedDate: new Date(),
        modifiedBy: user.id,
      }
    );
  }

  async updateMany(query, updateQuery) {
    return await this.repository.updateMany(query, updateQuery);
  }

  async updateDepositConfirm(data) {
    this.loggerService.log(this.context, clc.green("updateDepositConfirm"));
    const oldDto: any = await this.repository.findOne({ id: data.id });
    if (!oldDto) {
      return;
    }
    oldDto.depositConfirmFromCustomer = data.status;

    this.commandId = uuid.v4();
    return await this.executeCommand(
      Action.UPDATE,
      null,
      this.commandId,
      oldDto
    );
  }

  async updateTransferConfirm(data) {
    this.loggerService.log(this.context, clc.green("updateTransferConfirm"));
    const oldDto: any = await this.repository.findOne({ id: data.id });
    if (!oldDto) {
      return;
    }
    oldDto.transferConfirmFromCustomer = data.status;

    this.commandId = uuid.v4();
    return await this.executeCommand(
      Action.UPDATE,
      null,
      this.commandId,
      oldDto
    );
  }

  async updateShowReceipt(dto: UpdateShowReceiptDto) {
    this.loggerService.log(this.context, clc.green("updateShowReceipt"));
    let oldData = await this.repository.findOne({
      id: dto.id,
      "policyPayment.schedule.installments.receipts.id": dto.receiptId,
    });

    let installments: any[] =
      oldData?.policyPayment?.schedule?.installments || [];
    if (installments.length) {
      for (let i of installments) {
        let receipts: any[] = i.receipts || [];
        const index = receipts.findIndex((r) => r.id === dto.receiptId);
        if (index > -1) {
          receipts[index].isShowedReceipt = dto.isShowedReceipt;
          i.receipts = receipts;
        }
      }
      oldData.policyPayment.schedule.installments = installments;
    }

    this.commandId = uuid.v4();
    return this.executeCommand(Action.UPDATE, null, this.commandId, oldData);
  }

  async importFiles(user, dto, files, actionName: string) {
    this.loggerService.log(this.context, clc.green("create service"));
    let contracts = await CommonUtils.convertToJson(files);
    let unitsReject: any[] = [];
    let unitsImport: any[] = [];

    const history = [];
    for (const contract of contracts) {
      let unit = contract;
      let amount,
        price = 0,
        landPrice = 0,
        housePrice = 0;
      let receipt = {};
      let projectSetting = {};
      const model: any = Object.assign({}, contract, {
        isDebtRemind: contract["isDebtRemind"] === "x",
        calcCurrencyFirst: contract["calcCurrencyFirst"] === "x",
        type: ContractEnum.DEPOSIT,
      });

      if (contract["startDate"]) {
        if (!moment(contract.startDate, "DD/MM/YYYY", true).isValid()) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai định dạng "DD/MM/YYYY": Ngày bắt đầu`,
          });
        } else {
          model.startDate = moment(
            contract["startDate"],
            "DD/MM/YYYY"
          ).toDate();
        }
      }
      if (contract["expiredDate"]) {
        if (!moment(contract.expiredDate, "DD/MM/YYYY", true).isValid()) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai định dạng "DD/MM/YYYY": Ngày kết thúc`,
          });
        } else {
          model.expiredDate = moment(
            contract["expiredDate"],
            "DD/MM/YYYY"
          ).toDate();
        }
      }
      if (contract["maintenanceFee"] && contract["maintenanceFee"]["type"]) {
        model.maintenanceFee.type =
          contract["maintenanceFee"]["type"] === "VND" ? "currency" : "percent";
      }
      if (contract["maintenanceFee"] && contract["maintenanceFee"]["value"]) {
        model.maintenanceFee.value = parseInt(
          contract["maintenanceFee"]["value"]
        );
      }

      this.commandId = uuid.v4();

      model.modifiedBy = user.id;
      model.createdBy = user.id;
      model.createdDate = new Date();

      model.calcPriceVat = false;
      model.calcContractPrice = false;

      if (contract["calcPrice"] === "Giá có VAT") {
        model.calcPriceVat = true;
      } else if (contract["calcPrice"] === "Giá không có VAT") {
      } else if (
        contract["calcPrice"] === "Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT"
      ) {
        model.calcContractPrice = true;
      }

      if (!contract.signedDate) {
        unit = null;
        history.push({
          line: `${contract.stt}`,
          error: `Thiếu trường bắt buộc: Ngày ký kết`,
        });
      } else {
        if (!moment(contract.signedDate, "DD/MM/YYYY", true).isValid()) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai định dạng "DD/MM/YYYY": Ngày ký kết`,
          });
        } else {
          model.signedDate = moment(
            contract["signedDate"],
            "DD/MM/YYYY"
          ).toISOString();
        }
      }

      if (contract.transferType) {
        model.transferType = this.getTransferType(contract.transferType);
        if (!model.transferType) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai kiểu dữ liệu: Hình thức thanh toán`,
          });
        }
      }

      let primaryTransaction;
      if (!contract.primaryTransactionCode) {
        unit = null;
        history.push({
          line: `${contract.stt}`,
          error: `Thiếu trường bắt buộc: Mã phiếu YCDCO`,
        });
      } else {
        primaryTransaction = await this.propertyClient.sendDataPromise(
          {
            code: contract.primaryTransactionCode,
          },
          CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
            .GET_PROPERTY_PRIMARY_TRANSACTION_BY_CODE
        );
        if (!primaryTransaction) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Không tìm thấy phiếu YCDCO`,
          });
        } else if (primaryTransaction.status !== "SUCCESS") {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Phiếu YCDCO trạng thái không hợp lệ`,
          });
        } else if (primaryTransaction.contract) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Phiếu YCDCO đã có hợp đồng`,
          });
        }
        if (!unit) {
          unitsReject.push(contract);
          continue;
        }
        if (contract.hasCustomer2 && contract.customer2.name) {
          unit = await this.checkErrorCustomer(
            history,
            contract,
            contract.customer2,
            unit
          );
          let address = {
            country: contract.customer2["country"],
            province: contract.customer2["province"],
            district: contract.customer2["district"],
            ward: contract.customer2["ward"],
            address: contract.customer2["address"],
            fullAddress: "",
          };
          let rootAddress = {
            country: contract.customer2["rootCountry"],
            province: contract.customer2["rootProvince"],
            district: contract.customer2["rootDistrict"],
            ward: contract.customer2["rootWard"],
            address: contract.customer2["rootAddress"],
            fullAddress: "",
          };

          let customer2: any = {
            name: contract.customer2["name"],
            gender: contract.customer2["gender"] === "Nam" ? "male" : "female",
            birthday:
              contract.customer2["onlyYear"] === "x"
                ? contract.customer2["dob"]
                : moment(contract.customer2["dob"], "DD/MM/YYYY"),
            birthdayYear: contract.customer2["dob"],
            onlyYear: contract.customer2["onlyYear"] === "x",
            phone: contract.customer2["phone"],
            email: contract.customer2["email"],
            identityNumber: contract.customer2["identityNumber"],
            identityIssueDate: contract.customer2["identityDate"],
            identityIssueLocation: contract.customer2["identityLocation"],
            taxCode: contract.customer2["taxCode"]
              ? contract.customer2["taxCode"]
              : "",
            bankInfo: {
              code: contract["bankCode"],
              value: contract["bankValue"],
            },
            address: address,
            rootAddress: rootAddress,
            code: "",
            company: "",
            position: "",
            type: "",
          };
          primaryTransaction.customer2 =
            CommonUtils.getCustomerMapping(customer2);
        }
        model.primaryTransaction = primaryTransaction;

        if (model.calcContractPrice) {
          price = primaryTransaction.propertyUnit.contractPrice;
          housePrice = 0;
          landPrice = 0;
        } else {
          price = model.calcPriceVat
            ? primaryTransaction.propertyUnit.priceVat
            : primaryTransaction.propertyUnit.price;
          housePrice = model.calcPriceVat
            ? primaryTransaction.propertyUnit.housePriceVat
            : primaryTransaction.propertyUnit.housePrice;
          landPrice = model.calcPriceVat
            ? primaryTransaction.propertyUnit.landPriceVat
            : primaryTransaction.propertyUnit.landPrice;
        }
        model.maintenanceFee = {
          ...model.maintenanceFee,
          contractPriceForMaintenanceFee:
            primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee ||
            0,
        };

        receipt = primaryTransaction.reciept;
        amount = primaryTransaction.amount;
        projectSetting = primaryTransaction.project.setting;
      }

      if (contract.policyDiscountCodes) {
        const codes = contract.policyDiscountCodes.split("|");
        const policyDiscounts: any[] =
          await this.policyQueryService.findByCodes(codes);
        if (!policyDiscounts || !policyDiscounts.length) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Không tìm thấy chính sách chiết khấu`,
          });
        } else {
          model.policyDiscounts = policyDiscounts;

          // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
          if (price > 0 && !housePrice && !landPrice) {
            if (
              policyDiscounts.some(
                (e) =>
                  e.typeRealEstate ===
                  DiscountTypeRealEstateEnum.HOUSE ||
                  e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
              )
            ) {
              unit = null;
              history.push({
                line: `${contract.stt}`,
                error: `Chính sách chiết khẩu không phù hợp`,
              });
            }
          } else {
            if (
              policyDiscounts.some(
                (e) =>
                  !e.typeRealEstate ||
                  e.typeRealEstate ===
                  DiscountTypeRealEstateEnum.DEFAULT
              )
            ) {
              unit = null;
              history.push({
                line: `${contract.stt}`,
                error: `Chính sách chiết khẩu không phù hợp`,
              });
            }
          }
        }
      } else {
        model.policyDiscounts = [];
      }

      if (contract.policyPaymentCode) {
        const policyPayment = await this.policyQueryService.findOne({
          code: contract.policyPaymentCode,
          type: PolicyTypeEnum.PAYMENT,
        });
        if (!policyPayment) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Không tìm thấy chính sách thanh toán`,
          });
        } else {
          model.policyPayment = (this.transformPolicyPayment(
            model,
            price,
            projectSetting,
            model.signedDate,
            receipt,
            policyPayment,
            housePrice,
            landPrice,
            model.maintenanceFee
          )).policyPayment;
        }
      } else {
        unit = null;
        history.push({
          line: `${contract.stt}`,
          error: `Thiếu trường bắt buộc: Mã chính sách thanh toán`,
        });
      }

      if (!unit) {
        unitsReject.push(contract);
        continue;
      }

      if (!model.code) {
        const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-`;
        model.code = await this.codeGenerateService.generateCode(
          CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
          prefix
        );
      }
      model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`;

      if (model.policyPayment && !model.paymentPercent) {
        let totalPercentage = 0;
        let currentTotalTransfered = 0;

        model.policyPayment.schedule.installments.forEach((inst, index) => {
          currentTotalTransfered =
            currentTotalTransfered + inst.totalTransfered;
          if (index > 0) {
            // bắt đầu tính từ đợt 1;
            if (
              inst.totalTransfered < inst.totalAmount ||
              inst.type === "currency"
            ) {
              totalPercentage =
                totalPercentage +
                Math.round((Number(currentTotalTransfered) * 100) / price);
            } else {
              totalPercentage = totalPercentage + inst.value;
            }
          }
        });

        if (model.policyPayment.schedule.installments.length === 1) {
          totalPercentage =
            totalPercentage +
            Math.round((Number(currentTotalTransfered) * 100) / price);
        }

        model.paymentPercent = totalPercentage;
      }

      if (
        contract.companyInformation &&
        contract.companyInformation.haveValue
      ) {
        model.companyInformation = Object.assign(
          {},
          contract.companyInformation,
          {
            haveValue: true,
            dateOfIssue: contract.companyInformation.dateOfIssue
              ? moment(
                contract.companyInformation.dateOfIssue,
                "DD/MM/YYYY"
              ).toDate()
              : "",
          }
        );
      }

      if (contract.receipt && contract.receipt.length) {
        contract.receipt = contract.receipt.filter((r) => !!r.amount);
        const receipts = [];
        contract.receipt.forEach(function (receipt, idx) {
          const installmentName =
            model.policyPayment.schedule.installments[idx].name;
          const amounts = receipt.amount.split("|") || [];
          const dates = receipt.date.split("|") || [];
          const receiptNums = receipt.receiptNum.split("|") || [];
          if (amounts.length) {
            amounts.forEach(function (amount, aIdx) {
              receipts.push({
                amount: amount,
                date: dates[aIdx],
                receiptNum: receiptNums[aIdx],
                installmentName,
              });
            });
          }
        });
        model.receipts = receipts;
        model.user = user;
      }

      unitsImport.push(contract);

      model.status = StatusContractEnum.ACCOUNTANT_WAITING;

      //  create resident
      let projectInfo: any = {};
      projectInfo = await this.propertyClient.sendDataPromise(
        model.primaryTransaction.project,
        CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
      );
      let newObject = {};
      if (projectInfo) {
        newObject = {
          ...model.primaryTransaction,
          project: projectInfo,
          customer: model.primaryTransaction.customer,
          propertyUnit: model.primaryTransaction.propertyUnit,
          contractId: this.commandId,
        };
      }

      // send transaction to care service
      await this.careClient.sendDataPromise(
        {
          primaryTransaction: newObject,
        },
        CmdPatternConst.CARE.GET_TRANSACTION
      );

      await this.socialClient.sendDataPromise(
        {
          projectId: model.primaryTransaction.project.id,
        },
        CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP
      );

      const { personalInfo, info } =
        model["primaryTransaction"].customer || {} || {};
      const { email, phone, name, identities } = personalInfo;

      // if(email && phone ) {
      //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)=> {

      //     if(!customer) {
      //       const payload = { personalInfo: { name, email, phone, identities }, accessSystem: user.notiSystem ? [user.notiSystem] : null  };
      //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

      //     }else if(!customer.isActive) {

      //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
      //     }

      //   })
      // }

      model.id = this.commandId;
      await this.repository.create(model);
      await this.updateContractInTracsaction(model);

      if (model.receipts && model.receipts.length) {
        const transactionData = {
          action: "property-ticketCreated",
          receipts: model.receipts,
          contractId: model.id,
          user: model.user,
        };
        await this.transactionClient.sendDataPromise(
          transactionData,
          CmdPatternConst.LISTENER
            .PRIMARY_CONTRACT_CREATE_TRANSFERRED_TICKET_LISTENER
        );
      }

      console.log("model", model);
    }

    // Tạo history.
    const file = await this.uploadClient.sendData(
      files[0].originalname,
      files[0].buffer
    );
    const historyModel = {
      fileName: files[0].originalname,
      createdBy: user.id,
      fail: unitsReject.length,
      success: unitsImport.length,
      description: history,
      type: "IMPORT_CONTRACT_DEPOSIT",
      eventName: "importHDC",
      file,
    };
    await this.historyRepository.create(historyModel);
  }

  updateContractInTracsaction(event) {
    try {
      let data;
      data = {
        primaryTransactionId: event.primaryTransaction.id,
        id: event.id,
        code: event.code,
        type: event.type,
        isTransferred: event.isTransferred,
      };

      if (event.oldPrimaryTransaction)
        data.oldPrimaryTransactionId = event.oldPrimaryTransaction;

      // khi HĐC đã sang HĐMB thì không update sang primary transaction nữa
      if (!event.purchase) {
        this.propertyClient.sendData(
          data,
          CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
            .UPDATE_CONTRACT_IN_TRANSACTION_BY_ID
        );
      }
    } catch (error) {
      console.log("[Error] Update primary transaction failed, detail: ", error);
    }
  }

  getTransferType(type) {
    switch (type) {
      case "Tiền mặt":
        return "CASH";
      case "Chuyển khoản":
        return "TRANSFER";
      case "Khác":
        return "OTHER";
      default:
        return "";
    }
  }

  checkErrorCustomer(history, ticket, customer, unit) {
    let customer2Name = "(đồng sở hữu)";

    // Check đồng sở hữu
    if (ticket.hasCustomer2 === "x" && !ticket.customer2) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu thông tin đồng sở hữu`,
      });
      return unit;
    }

    // Check error
    if (!customer.name) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Tên khách hàng` + customer2Name,
      });
    }
    // if (!customer.dob) {
    //   unit = null;
    //   history.push({
    //     line: `${ticket.stt}`,
    //     error: `Thiếu trường bắt buộc: Ngày sinh` + customer2Name
    //   });
    // }
    if (!customer.gender) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Giới tính` + customer2Name,
      });
    }
    // if (!customer.phone) {
    //   unit = null;
    //   history.push({
    //     line: `${ticket.stt}`,
    //     error: `Thiếu trường bắt buộc: Số điện thoại` + customer2Name
    //   });
    // }
    if (
      customer.phone &&
      !customer.phone.toString().match(CommonConst.REGEX_VN_PHONE)
    ) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Số điện thoại không hợp lệ` + customer2Name,
      });
    }
    // if (!customer.email) {
    //   unit = null;
    //   history.push({
    //     line: `${ticket.stt}`,
    //     error: `Thiếu trường bắt buộc: Địa chỉ Email` + customer2Name
    //   });
    // }
    if (
      customer.email &&
      !customer.email.toString().match(CommonConst.REGEX_EMAIL)
    ) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Email: Sai định dạng email` + customer2Name,
      });
    }
    if (!customer.identityNumber) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Số CMND/Hộ chiếu` + customer2Name,
      });
    }
    if (!customer.identityDate) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Ngày cấp` + customer2Name,
      });
    }
    if (!customer.identityLocation) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Nơi cấp` + customer2Name,
      });
    }
    return unit;
  }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.UPDATE:
        commandObject = new UpdatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.DELETE:
        commandObject = new DeletePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  checkErrorCustomer2SyncErp(history, customer2, unit, isCustomer2 = false) {
    let customer2Name = isCustomer2 ? "(đồng sở hữu)" : "";

    // Check error
    if (!customer2.name) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Tên khách hàng` + customer2Name,
      });
    }
    if (!customer2.birthday && !isCustomer2) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Ngày sinh` + customer2Name,
      });
    }
    if (!customer2.gender && !isCustomer2) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Giới tính` + customer2Name,
      });
    }
    if (!customer2.phone && !isCustomer2) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Số điện thoại` + customer2Name,
      });
    }
    if (
      customer2.phone &&
      !customer2.phone.toString().match(CommonConst.REGEX_VN_PHONE)
    ) {
      unit = null;
      history.push({
        error: `Số điện thoại không hợp lệ` + customer2Name,
      });
    }
    // if (!customer2.email) {
    //   unit = null;
    //   history.push({
    //     error: `Thiếu trường bắt buộc: Địa chỉ Email` + customer2Name
    //   });
    // }
    if (
      customer2.email &&
      !customer2.email.toString().match(CommonConst.REGEX_EMAIL)
    ) {
      unit = null;
      history.push({
        error: `Email: Sai định dạng email` + customer2Name,
      });
    }
    if (!customer2.identityNumber) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Số CMND/Hộ chiếu` + customer2Name,
      });
    }
    if (!customer2.identityIssueDate) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Ngày cấp` + customer2Name,
      });
    }
    if (!customer2.identityIssueLocation) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Nơi cấp` + customer2Name,
      });
    }
    return unit;
  }

  async createdContractSyncErp(contract) {
    this.loggerService.log(
      this.context,
      clc.green("created contract sync ERP")
    );
    let historyModel: any = {};
    const history = [];
    let unit = contract;
    let amount,
      price = 0,
      landPrice = 0,
      housePrice = 0;
    let receipt = [];
    let projectSetting = {};
    let updatePrimaryTransactionQuery;

    let oldContract;
    // check trùng
    if (contract.contractid) {
      oldContract = await this.repository.findOne({
        "syncErpData.contractid": contract.contractid,
      });
      if (oldContract) {
        for (const installment of oldContract.policyPayment.schedule
          .installments) {
          if (installment.receipts && installment.receipts.length) {
            receipt = [...receipt, ...installment.receipts];
          }
        }
      }
    }

    let paymentConfig, discountConfig;
    if (!contract.project) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: mã dự án ERP`,
      });

      // Return báo lỗi
      historyModel = {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }

    let configErp = await this.syncErpClient.sendDataPromise(
      contract.project,
      CmdPatternConst.LISTENER.SEARCH_CONFIG_ERP_BY_CAMPAIGN
    );
    if (!configErp) {
      unit = null;
      history.push({
        error: `Không tìm thấy cấu hình ERP`,
      });
    } else {
      paymentConfig =
        configErp.paymentMethod && configErp.paymentMethod.length > 0
          ? configErp.paymentMethod.find(
            (e) => e.paymentMethodCRM === contract.pymtterm
          )
          : null;
      if (!paymentConfig) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách thanh toán`,
        });
      }
      discountConfig =
        configErp.discountTable && configErp.discountTable.length > 0
          ? configErp.discountTable.find(
            (e) => e.discountTableCRM === contract.policyDiscountCodes
          )
          : null;
      if (contract.policyDiscountCodes && !discountConfig) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách chiết khấu`,
        });
      }
    }

    // Return báo lỗi
    if (!unit) {
      historyModel = {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }

    const model: any = Object.assign({}, contract, {
      isDebtRemind: false,
      calcCurrencyFirst: false,
      type: ContractEnum.DEPOSIT,
    });

    if (!contract.startdate) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Ngày ký kết`,
      });
    } else {
      model.startDate = new Date(
        momentTz(contract.startdate, "DD/MM/YYYY HH:mm:ss").tz(
          "Asia/Ho_Chi_Minh"
        )
      );
      model.signedDate = new Date(
        momentTz(contract.startdate, "DD/MM/YYYY HH:mm:ss").tz(
          "Asia/Ho_Chi_Minh"
        )
      );
    }
    if (contract.enddate) {
      model.expiredDate = new Date(
        momentTz(contract.enddate, "DD/MM/YYYY HH:mm:ss").tz("Asia/Ho_Chi_Minh")
      );
    }

    this.commandId = uuid.v4();

    // model.modifiedBy = user.id;
    // model.createdBy = user.id;
    if (!oldContract) {
      model.createdDate = new Date();
    }

    model.calcPriceVat = false;
    model.calcContractPrice = true;
    model.transferType = null;

    let primaryTransaction;
    // Tìm YCDCO theo systemno
    if (!contract.anal_pct5) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Mã phiếu YCDCO`,
      });
    } else {
      primaryTransaction = await this.propertyClient.sendDataPromise(
        {
          "syncErpData.systemno": contract.anal_pct5,
        },
        CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
          .GET_PROPERTY_PRIMARY_TRANSACTION_BY_QUERY
      );
      if (!primaryTransaction) {
        unit = null;
        history.push({
          error: `Không tìm thấy phiếu YCDCO`,
        });
      } else if (primaryTransaction.status !== "SUCCESS") {
        unit = null;
        history.push({
          error: `Phiếu YCDCO trạng thái không hợp lệ`,
        });
      } else if (
        primaryTransaction.contract &&
        (!oldContract || primaryTransaction.contract.id !== oldContract.id)
      ) {
        unit = null;
        history.push({
          error: `Phiếu YCDCO đã có hợp đồng`,
        });
      }

      // Return báo lỗi
      if (!unit) {
        historyModel = {
          isSuccess: false,
          description: history,
        };
        return historyModel;
      }

      // update primary transaction
      updatePrimaryTransactionQuery = {
        id: primaryTransaction.id,
      };

      // Khách hàng
      let customerAddress = contract.address || "";
      let customerRootAddress = contract.contactaddress || "";
      let address = {
        country: "Việt Nam",
        province: customerAddress.split(",")[3],
        district: customerAddress.split(",")[2],
        ward: customerAddress.split(",")[1],
        address: customerAddress.split(",")[0],
        fullAddress: customerAddress,
      };
      let rootAddress = {
        country: "Việt Nam",
        province: customerRootAddress.split(",")[3],
        district: customerRootAddress.split(",")[2],
        ward: customerRootAddress.split(",")[1],
        address: customerRootAddress.split(",")[0],
        fullAddress: customerRootAddress,
      };
      let birthArr = contract.birthdate.split("/");
      birthArr = birthArr.filter((e) => e != "0");
      let customer: any = {
        name: contract.clientname,
        gender: contract.gender === "M" ? "male" : "female",
        birthday: moment(contract.birthdate, "DD/MM/YYYY"),
        birthdayYear: birthArr[birthArr.length - 1],
        onlyYear: birthArr.length < 3,
        phone: contract.telephone,
        email: contract.email,
        identityNumber: contract.idcard,
        identityIssueDate: moment(contract.issueddate, "DD/MM/YYYY"),
        identityIssueLocation: contract.issuedplace,
        address: address,
        rootAddress: rootAddress,
        company: "",
        position: "",
        type: "",
        taxCode: contract.taxcode,
      };
      unit = await this.checkErrorCustomer2SyncErp(history, customer, unit);
      primaryTransaction.customer = CommonUtils.getCustomerMapping(customer);
      updatePrimaryTransactionQuery.customer = primaryTransaction.customer;

      const members = (contract.membername || "").split(" | ");
      const hasCustomer2 = members.length > 1;

      // Đồng sở hữu
      if (hasCustomer2) {
        let customer2Address = contract.maddress.split(" | ").pop() || "";
        let customer2RootAddress =
          contract.mcontactaddress.split(" | ").pop() || "";
        let address = {
          country: "Việt Nam",
          province: customer2Address.split(",")[3],
          district: customer2Address.split(",")[2],
          ward: customer2Address.split(",")[1],
          address: customer2Address.split(",")[0],
          fullAddress: customer2Address,
        };
        let rootAddress = {
          country: "Việt Nam",
          province: customer2RootAddress.split(",")[3],
          district: customer2RootAddress.split(",")[2],
          ward: customer2RootAddress.split(",")[1],
          address: customer2RootAddress.split(",")[0],
          fullAddress: customer2RootAddress,
        };
        let birthArr = [
          contract.mbirthday.split(" | ").pop(),
          contract.mbirthmonth.split(" | ").pop(),
          contract.mbirthyear.split(" | ").pop(),
        ];
        birthArr = birthArr.filter((e) => e !== "0");
        const onlyYear = birthArr.length < 3;
        let customer2: any = {
          name: contract.membername.split(" | ").pop(),
          gender:
            contract.mgender.split(" | ").pop() === "M" ? "male" : "female",
          birthday: onlyYear
            ? birthArr[birthArr.length - 1]
            : moment(
              `${contract.mbirthday.split(" | ").pop()}/${contract.mbirthmonth
                .split(" | ")
                .pop()}/${contract.mbirthyear.split(" | ").pop()}`,
              "DD/MM/YYYY"
            ),
          birthdayYear: birthArr[birthArr.length - 1],
          onlyYear: onlyYear,
          phone: contract.mtelephone.split(" | ").pop(),
          email: contract.memail.split(" | ").pop(),
          identityNumber: contract.midcard.split(" | ").pop(),
          identityIssueDate: moment(
            contract.missueddate.split(" | ").pop(),
            "DD/MM/YYYY"
          ),
          identityIssueLocation: contract.missuedplace.split(" | ").pop(),
          address: address,
          rootAddress: rootAddress,
          company: "",
          position: "",
          type: "",
        };
        unit = await this.checkErrorCustomer2SyncErp(
          history,
          customer2,
          unit,
          true
        );
        primaryTransaction.customer2 =
          CommonUtils.getCustomerMapping(customer2);
        updatePrimaryTransactionQuery.customer2 = primaryTransaction.customer2;
      }

      // Return báo lỗi
      if (!unit) {
        historyModel = {
          isSuccess: false,
          description: history,
        };
        return historyModel;
      }

      model.primaryTransaction = primaryTransaction;

      if (model.calcContractPrice) {
        price = parseInt(contract.contractvalue);
        housePrice = 0;
        landPrice = 0;
      }
      model.maintenanceFee = {
        ...model.maintenanceFee,
        contractPriceForMaintenanceFee:
          primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
      };

      receipt = [...receipt, ...primaryTransaction.reciept];
      projectSetting = primaryTransaction.project.setting;
    }

    // Chính sách chiết khấu
    if (discountConfig && discountConfig.discountTableDXG) {
      const policyDiscounts: any[] = await this.policyQueryService.findByCodes([
        discountConfig.discountTableDXG,
      ]);
      if (!policyDiscounts || !policyDiscounts.length) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách chiết khấu`,
        });
      } else {
        model.policyDiscounts = policyDiscounts;

        // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
        if (price > 0 && !housePrice && !landPrice) {
          if (
            policyDiscounts.some(
              (e) =>
                e.typeRealEstate ===
                DiscountTypeRealEstateEnum.HOUSE ||
                e.typeRealEstate === DiscountTypeRealEstateEnum.LAND
            )
          ) {
            unit = null;
            history.push({
              error: `Chính sách chiết khẩu không phù hợp`,
            });
          }
        } else {
          if (
            policyDiscounts.some(
              (e) =>
                !e.typeRealEstate ||
                e.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT
            )
          ) {
            unit = null;
            history.push({
              error: `Chính sách chiết khẩu không phù hợp`,
            });
          }
        }
      }
    } else {
      model.policyDiscounts = [];
    }

    // Chính sách thanh toán
    if (paymentConfig && paymentConfig.paymentMethodDXG) {
      const policyPayment = await this.policyQueryService.findOne({
        code: paymentConfig.paymentMethodDXG,
        type: PolicyTypeEnum.PAYMENT,
      });
      if (!policyPayment) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách thanh toán`,
        });
      } else {
        model.policyPayment = (this.transformPolicyPayment(
          model,
          price,
          projectSetting,
          model.signedDate,
          receipt,
          policyPayment,
          housePrice,
          landPrice,
          model.maintenanceFee
        )).policyPayment;
      }
    } else {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Mã chính sách thanh toán`,
      });
    }

    // Return báo lỗi
    if (!unit) {
      historyModel = {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }
    if (!oldContract) {
      if (!model.code) {
        const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-`;
        model.code = await this.codeGenerateService.generateCode(
          CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME,
          prefix
        );
      }
      model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`;
    } else {
      model.name = oldContract.name;
      model.code = oldContract.code;
    }
    if (model.policyPayment && !model.paymentPercent) {
      let totalPercentage = 0;
      let currentTotalTransfered = 0;

      model.policyPayment.schedule.installments.forEach((inst, index) => {
        currentTotalTransfered = currentTotalTransfered + inst.totalTransfered;
        if (index > 0) {
          // bắt đầu tính từ đợt 1;
          if (
            inst.totalTransfered < inst.totalAmount ||
            inst.type === "currency"
          ) {
            totalPercentage =
              totalPercentage +
              Math.round((Number(currentTotalTransfered) * 100) / price);
          } else {
            totalPercentage = totalPercentage + inst.value;
          }
        }
      });

      if (model.policyPayment.schedule.installments.length === 1) {
        totalPercentage =
          totalPercentage +
          Math.round((Number(currentTotalTransfered) * 100) / price);
      }

      model.paymentPercent = totalPercentage;
    }

    if (contract.companyInformation && contract.companyInformation.haveValue) {
      model.companyInformation = Object.assign(
        {},
        contract.companyInformation,
        {
          haveValue: true,
          dateOfIssue: contract.companyInformation.dateOfIssue
            ? moment(
              contract.companyInformation.dateOfIssue,
              "DD/MM/YYYY"
            ).toDate()
            : "",
        }
      );
    }

    if (!oldContract) {
      model.status = StatusContractEnum.ACCOUNTANT_WAITING;
    } else {
      delete model.status;
    }

    //  create resident
    let projectInfo: any = {};
    projectInfo = await this.propertyClient.sendDataPromise(
      model.primaryTransaction.project,
      CmdPatternConst.LISTENER.GET_PROJECT_BY_ID
    );
    let newObject = {};
    if (projectInfo) {
      newObject = {
        ...model.primaryTransaction,
        project: projectInfo,
        customer: model.primaryTransaction.customer,
        propertyUnit: model.primaryTransaction.propertyUnit,
        contractId: this.commandId,
      };
    }

    // send transaction to care service
    await this.careClient.sendDataPromise(
      {
        primaryTransaction: newObject,
      },
      CmdPatternConst.CARE.GET_TRANSACTION
    );

    await this.socialClient.sendDataPromise(
      {
        projectId: model.primaryTransaction.project.id,
      },
      CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP
    );

    const { personalInfo, info } =
      model["primaryTransaction"].customer || {} || {};
    const { email, phone, name, identities } = personalInfo;

    // if(email && phone ) {
    //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)=> {

    //     if(!customer) {
    //       const payload = { personalInfo: { name, email, phone, identities }, accessSystem: "DX_AGENT"};
    //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

    //     }else if(!customer.isActive) {

    //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
    //     }

    //   })
    // }
    model.syncErpData = contract;
    // update price
    model.primaryTransaction.propertyUnit.contractPrice = price;
    updatePrimaryTransactionQuery.contractPrice = price;
    updatePrimaryTransactionQuery.propetyUnitId =
      primaryTransaction.propertyUnit.id;

    if (updatePrimaryTransactionQuery) {
      await this.propertyClient.sendDataPromise(
        updatePrimaryTransactionQuery,
        CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION
          .UPDATE_CUSTOMER_FROM_CONTRACT
      );
    }
    if (!oldContract) {
      model.id = this.commandId;
      await this.repository.create(model);
      await this.updateContractInTracsaction(model);
    } else {
      model.id = oldContract.id;
      await this.repository.update(model);
    }

    // Return báo hoàn thành
    historyModel = {
      isSuccess: true,
      description: history,
    };
    return historyModel;
  }

  async handover(
    user: any,
    dto: HandoverPrimaryContractDto,
    actionName: string
  ) {
    this.loggerService.log(this.context, "update contract handover");

    if (
      ![HandoverStatusEnum.handed, HandoverStatusEnum.later].includes(
        dto.handoverStatus
      )
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "Trạng thái bàn giao"),
      });
    }

    const oldDto: any = await this.repository.findOne({
      id: dto.id,
      handoverStatus: {
        $in: [HandoverStatusEnum.scheduled, HandoverStatusEnum.later],
      },
      status: StatusContractEnum.APPROVED,
    });
    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "Tài sản"),
      });
    }
    oldDto.deliveryDate = new Date(dto.deliveryDate) || new Date();
    // lấy thông tin bàn giao
    const handOver = await this.handoverQueryService.findOneByQuery({
      status: true,
      "project.id": oldDto.primaryTransaction.project.id,
    });
    if (!handOver) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.INVALID, "Thiết lập bàn giao"),
      });
    }

    this.commandId = uuid.v4();
    if (dto.handoverStatus === HandoverStatusEnum.handed) {
      dto.deliveryResult.deliveryDate = oldDto.deliveryDate;
      oldDto.deliveryResult = dto.deliveryResult;
      if (
        oldDto.handoverStatus &&
        oldDto.handoverStatus != HandoverStatusEnum.handed
      ) {
        oldDto.handoverStatus = HandoverStatusEnum.handed;
        let syncConfig = await this.syncErpClient.sendDataPromise(
          oldDto.primaryTransaction.project.id,
          CmdPatternConst.SYNC_ERP.GET_CAMPAIGN_ERP_BY_PROJECT_ID
        );
        if (syncConfig && oldDto.syncErpData && oldDto.syncErpData.contractid) {
          let property: any = "";
          if (
            oldDto.primaryTransaction &&
            oldDto.primaryTransaction.propertyUnit &&
            oldDto.primaryTransaction.propertyUnit.attributes &&
            oldDto.primaryTransaction.propertyUnit.attributes.length > 21
          ) {
            if (oldDto.primaryTransaction.propertyUnit.attributes[21].value) {
              property =
                oldDto.primaryTransaction.propertyUnit.attributes[21].value;
            }
          }

          let data: any = {
            formid: "contract01",
            contractid: oldDto.syncErpData.contractid,
            property: property,
            statusdate: moment(oldDto.deliveryDate).format(
              "YYYY-MM-DD hh:mm:ss A"
            ),
            status: "BGI",
            impstatus: "W",
          };
          let dataSendCRM: any = {
            action: "contract01",
            data: [data],
          };
          // send data sync erp
          await this.syncErpClient.sendDataPromise(
            dataSendCRM,
            CmdPatternConst.SYNC_ERP.SEND_REQUEST_TO_ERP
          );
        }
      }

      // save rating to project
      const rating = {
        userId: user.id,
        rate: dto.deliveryResult.rate,
      };
      this.propertyClient.sendDataPromise(
        { id: oldDto.primaryTransaction.project.id, rating },
        CmdPatternConst.PROJECT.RATING
      );

      // send notification to customer
      this.notificationClient.createNotificationCare(
        "care_deliveryHanded",
        null,
        user.id,
        "primary-contract",
        oldDto.id,
        {
          code: oldDto.primaryTransaction.propertyUnit.code,
          time: oldDto.deliveryDate
            ? moment(oldDto.deliveryDate).format("HH:MM")
            : "",
          date: oldDto.deliveryDate
            ? moment(oldDto.deliveryDate).format("DD/MM/YYYY")
            : "",
        }
      );

      // Cập nhật đã bàn giao căn hộ
      let updatePropery = {
        query: { id: oldDto.primaryTransaction.propertyUnit.id },
        model: {
          $set: { "contract.handoverStatus": HandoverStatusEnum.handed },
        },
      };
      this.propertyClient.sendDataPromise(
        updatePropery,
        CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY
      );
    } else {
      // send notification to customer
      this.notificationClient.createNotificationCare(
        "care_deliveryLater",
        null,
        user.id,
        "primary-contract",
        oldDto.id,
        {
          code: oldDto.primaryTransaction.propertyUnit.code,
          time: oldDto.deliveryDate
            ? moment(oldDto.deliveryDate).format("HH:MM")
            : "",
          date: oldDto.deliveryDate
            ? moment(oldDto.deliveryDate).format("DD/MM/YYYY")
            : "",
          nextTime: oldDto.deliveryHistories
            ? oldDto.deliveryHistories.length + 2
            : 2,
          hotline: handOver.hotline ? handOver.hotline : "",
        }
      );
      oldDto.isSendDelivery = false;

      // later delete handover schedule
      if (dto.handoverStatus === HandoverStatusEnum.later) {
        this.handoverScheduleDomainService.deleteHandoverSchedule(
          user,
          oldDto.id,
          actionName
        );
      }
    }
    oldDto.handoverStatus = dto.handoverStatus;
    oldDto.deliveryItems = dto.deliveryItems;
    oldDto.files = dto.files;

    const history = {
      deliveryDate: new Date(oldDto.deliveryDate),
      isPass: dto.handoverStatus === HandoverStatusEnum.handed,
      items: dto.deliveryItems,
    };

    oldDto.deliveryHistories = oldDto.deliveryHistories
      ? oldDto.deliveryHistories.push(history)
      : [dto.deliveryItems];
    oldDto.modifiedDate = new Date();

    // Xác nhận bàn giao
    this.addHistoriesHandover(
      oldDto,
      user,
      new Date(),
      HandoverScheduleStatusNameConst.HANDED_OVER,
      HandoverScheduleActionNameConst.ACCEPT_HANDOVER
    );
    return await this.executeCommand(
      Action.UPDATE,
      actionName,
      this.commandId,
      oldDto
    );
  }
  async updateIsInterestExemptionIsTrue(dto: any) {
    this.loggerService.log(this.context, clc.blue('update for eapp service'));

    const contract = await this.repository.findOne({ id: dto.contract?.id });
    if (!contract) return this.getResponse('PRIMARYCONTRACT0004');

    this.commandId = uuid.v4();
    // contract.eappNumber = dto.eappNumber;
    // contract.eappUrl = dto.urlEapp;
    contract.eapStatus = dto.status;
    if (dto.id && dto.code) {
      contract.proposal = dto;
    }
    else contract.proposal = null
    contract.restoreinterestReason = dto.contract?.restoreinterestReason;
    contract.isInterestExemption = true;
    if (contract.eapStatus === ProposalStatusEnum.RETURNED.code || contract.eapStatus === ProposalStatusEnum.REJECTED.code || contract.eapStatus === ProposalStatusEnum.CANCELED.code) {
      contract.isInterestExemption = false;
    }

    await this.repository.update(contract);
    await this.updateInterestCalculation(contract, true);
    return this.getResponse(0);
  }
  async updateIsInterestExemptionIsFalse(dto: any) {
    this.loggerService.log(this.context, clc.blue('update for eapp service'));

    const contract = await this.repository.findOne({ id: dto.contract?.id });
    if (!contract) return this.getResponse('PRIMARYCONTRACT0004');

    this.commandId = uuid.v4();
    // contract.eappNumber = dto.eappNumber;
    contract.eapStatus = dto.status;
    if (dto.id && dto.code) {
      contract.proposal = dto;
    }
    else contract.proposal = null;
    contract.interestExemptionReason = dto.contract?.interestExemptionReason;
    // contract.eappUrl = dto.urlEapp;
    contract.isInterestExemption = false;
    if (contract.eapStatus === ProposalStatusEnum.RETURNED.code || contract.eapStatus === ProposalStatusEnum.REJECTED.code || contract.eapStatus === ProposalStatusEnum.CANCELED.code) {
      contract.isInterestExemption = true;
    }
    await this.repository.update(contract);
    await this.updateInterestCalculation(contract, true);
    return this.getResponse(0);
  }
  async updateInterestCalculationDetail(dto: any) {
    this.loggerService.log(this.context, clc.blue('update for eapp service'));

    // Bước 1: Tìm hợp đồng chứa ít nhất 1 interestCalculation có code khớp
    const contract = await this.repository.findOne({
      id: dto.interestCalculation?.contractId,
      'interestCalculations.id': dto.interestCalculation?.id,
    });

    if (!contract) return this.getResponse('PRIMARYCONTRACT0004');
    // Bước 2: Tìm đúng interestCalculation để update
    for (let item of contract.interestCalculations) {
      if (item.id === dto.interestCalculation?.id) {
        item.eapStatus = dto.status;
        item.eapCode = dto.eappNumber;
        if (dto.id && dto.code) {
          item.proposal = { id: dto.id, code: dto.code };
        }
        else item.proposal = null;
        item.interestReductionAmountEap = dto.interestCalculation?.interestReductionAmountEap;
        if (item.eapStatus === ProposalStatusEnum.APPROVED.code) {
          item.interestReductionAmount = dto.interestCalculation?.interestReductionAmountEap;
        }
        else {
          item.interestReductionAmount = 0;
        }
        item.interestReductionReason = dto.interestCalculation?.interestReductionReason;
        break; // Dừng luôn sau khi tìm thấy
      }
    }
    // Bước 4: Gọi lệnh cập nhật
    await this.repository.updateOne(
      { id: contract.id },
      { $set: { interestCalculations: contract.interestCalculations } }
    );
    await this.updateInterestCalculation(contract, true);
    return this.getResponse(0);
  }

  async getPrimaryContractById(id: string) {
    return await this.repository.findOne({ id });
  }

  async checkConditionToUpdateDebtPenalty(data: any) {

    const contracts = await this.repository.validateContract(data);
    console.log('contracts', contracts);

    if (contracts && contracts.length > 0) {
      return false;
    }

    return true;
  }

  async getDropdownProjects(userLogged, queryParams: any) {
    const { page = 1, pageSize = 10, search = '' } = queryParams;
    const limit = parseInt(pageSize, 10) || 10;
    const skip = (parseInt(page, 10) - 1) * limit;
    const projects = await this.propertyClient.sendDataPromise(
      {
        query: {},
        fields: { _id: 0, id: 1, name: 1, code: 1 }
      },
      cmd.PROJECT.GET_PROJECT_DROPDOWN_LIST
    );

    if (!projects || !Array.isArray(projects)) {
      return {
        rows: [],
        page: parseInt(page, 10),
        pageSize: limit,
        total: 0,
        totalPages: Math.ceil(0 / limit),
      };
    }

    // Lọc dữ liệu trong mảng
    let filteredData = projects.filter(item => {
      const name = item.name ? item.name.toString().toLowerCase() : "";
      const code = item.code ? item.code.toString().toLowerCase() : "";
      const keyword = search.toLowerCase()
      return name.includes(keyword) || code.includes(keyword);
    });

    // Tính tổng số phần tử sau khi lọc
    const total = filteredData.length;

    // Phân trang
    const rows = filteredData.slice(skip, skip + limit);

    return {
      rows,
      page: parseInt(page, 10),
      pageSize: limit,
      total,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getContractByQuery(query): Promise<any> {
    const queryParams: any = {};
    if (query.policyPayment) {
      queryParams['policyPayment.id'] = query.policyPayment;
    }
    if (query.policyDiscounts) {
      queryParams['policyDiscounts.id'] = query.policyDiscounts;
    }
    return await this.repository.find(queryParams);
  }

  private handleContractNotification({
    contract,
    handoverSchedule,
    setting,
    data,
    notifications,
    notifyData,
    hotline
  }: {
    contract: any;
    handoverSchedule: any;
    setting: any;
    data: any;
    notifications: notificationPayloadDto[];
    notifyData: any[];
    hotline: string;
  }) {
    const time = contract.deliveryDate ? moment(contract.deliveryDate).format('HH:mm') : '';
    const date = contract.deliveryDate ? moment(contract.deliveryDate).format('DD/MM/YYYY') : '';
    const { code, identityNumber, customerName } = contract;

    const smsTemplate = setting?.smsTemplate || '';
    const smsBrandName = setting?.smsBrandName || '';
    const emailTitle = setting?.emailTitle || '';
    const emailTemplateRaw = setting?.emailTemplate || '';
    const emailTemplate = this.renderTemplate(emailTemplateRaw.replace(/(?:\r\n|\r|\n)/g, '<br>'), data);

    const emailCC: string[] = (setting?.emailCC || '')
      .split(';')
      .map(e => e.trim())
      .filter(e => e !== '');
    const emailFrom: string[] = (setting?.emailFrom || '')
      .split(';')
      .map(e => e.trim())
      .filter(e => e !== '');
    let emailBCC: string[] = (setting?.emailBCC || '')
      .split(';')
      .map(e => e.trim())
      .filter(e => e !== '');

    if (handoverSchedule?.supportEmployee?.email) {
      emailBCC.push(handoverSchedule.supportEmployee.email);
    }

    // Gửi notify app
    this.notificationClient.createNotificationCare(
      "care_deliveryReminderSend",
      null,
      identityNumber,
      "primary-contract",
      contract.id,
      { code, time, date, hotline, identityNumber }
    );

    // Gửi notify SMS
    notifyData.push({
      phone: contract.phone,
      contentSMS: smsTemplate,
      brandName: smsBrandName,
      code, time, date, hotline, customerName
    });

    // Gửi notify Email

    const payload = Object.assign(new notificationPayloadDto(), {
      subject: emailTitle,
      content: emailTemplate,
      receives: emailFrom,
      ccReceives: emailCC,
      bccReceives: emailBCC,
    });

    notifications.push(payload);
  }

  renderTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/{{(.*?)}}/g, (_, key) => {
      const path = key.trim().split('.');
      let value = data;

      for (const segment of path) {
        if (value === null || value === undefined) return '';
        value = value[segment];
      }

      return (value !== null && value !== undefined) ? String(value) : '';
    });
  }

  private calculateInterest(projectInfo, installment, paymentDue, actualLateDate) {
    let result = 0;
    let needTransfers = [];
    const groupedReceipts = installment.receipts.reduce((acc, receipt) => {
      const dateKey = new Date(receipt.receiptDate).toISOString().split('T')[0]; // Lấy phần YYYY-MM-DD
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(receipt);
      return acc;
    }, {} as Record<string, typeof installment.receipts>);

    for (const dateKey in groupedReceipts) {
      let lateDay: any;
      let receiptDate = new Date(groupedReceipts[dateKey][0].receiptDate);
      if (projectInfo?.setting?.onlyWorkday) {
        const numberOfSatSun = countWeekendDays(paymentDue, receiptDate)
        lateDay = receiptDate.getTime() - paymentDue.getTime() + this.ONE_DAY - (numberOfSatSun * this.ONE_DAY);
      }
      else lateDay = receiptDate.getTime() - paymentDue.getTime() + this.ONE_DAY;
      lateDay = Math.floor(lateDay / this.ONE_DAY);
      const totalReceipts = groupedReceipts[dateKey].reduce(
        (sum, receipt) => sum + receipt.amount,
        0
      );
      const totalBeforeReceipts = installment.receipts.filter(x => x.receiptDate && new Date(x.receiptDate) < new Date(receiptDate)).reduce(
        (sum, receipt) => sum + receipt.amount,
        0
      );

      const date = new Date(dateKey);
      date.setDate(date.getDate() + 1);
      const nextDateKey = date.toISOString().split('T')[0];
      if (!groupedReceipts[nextDateKey]){
        const needTransfer1 = (installment.totalAmount - totalBeforeReceipts);
        const needTransfer2 = (installment.totalAmount - totalBeforeReceipts) - totalReceipts;
        needTransfers.push({ needTransfer: needTransfer1, lateDay: lateDay })
        needTransfers.push({ needTransfer: needTransfer2, lateDay: lateDay + 1 })
      }
      else {
        const needTransfer = (installment.totalAmount - totalBeforeReceipts);
        needTransfers.push({ needTransfer: needTransfer, lateDay: lateDay })
      }
    }

    for (let i = 0; i <= actualLateDate - 1; i++) {
      const day = i + 1;
      const currentDebtage = projectInfo.setting?.debtage.find(item => {
        if (item.isFinalRange) {
          return day >= item.fromDay;
        }
        return day >= item.fromDay && day <= item.toDay;
      });
      if (!currentDebtage) result += 0;
      else {
        let needTransfer = 0;
        let needTransferBeforeInterest = needTransfers
          .filter(x => x.lateDay <= 0)
          .reduce((sum, x) => sum + x.needTransfer, 0);
        needTransferBeforeInterest = needTransferBeforeInterest === 0 ? installment.totalAmount : needTransferBeforeInterest;
        
        //needTransfer sẽ bị thay đổi theo mỗi lần thanh toán nên phải làm thế này, trường hợp không có tức phiếu thu đó được thanh toán trước hạn => tiền gốc phải là số tiền đầu tiên
        const filtered = needTransfers.filter(x => {
          if (currentDebtage.fromDay > currentDebtage.toDay) {
            return x.lateDay >= currentDebtage.fromDay && x.lateDay <= day;
          } else {
            return (
              x.lateDay >= currentDebtage.fromDay &&
              x.lateDay <= currentDebtage.toDay &&
              x.lateDay <= day
            );
          }
        });

        if (filtered.length === 0) {
          needTransfer = needTransferBeforeInterest;
        }
        else {
          // Tìm phần tử có lateDay gần nhất với "day"
          const closest = filtered.reduce((closestItem, currentItem) => {
            const currentDiff = Math.abs(currentItem.lateDay - day);
            const closestDiff = Math.abs(closestItem.lateDay - day);
            return currentDiff < closestDiff ? currentItem : closestItem;
          });

          needTransfer = closest.needTransfer
        }
        result += needTransfer * (currentDebtage?.interestRate || 0) / 100 / projectInfo?.setting?.daysPerYear;
      }
    }

    return Math.round(result);
  }

  private async createNewCustomer(model: any, headers) {
    try {
      const backendUrl = this.configService.get("BACKEND_URL");
      const businessAreas: any = await this.httpService.getAsync(
          `${backendUrl}/msx-masterdata-producer/api/v1/business-area/get-all`
        )
      const businessArea = businessAreas.data.rows.find(x => x.code === model.primaryTransaction.project.businessArea.code)

      let newCustomer = model.primaryTransaction.customer;
      newCustomer.orgCode = businessArea?.companyCode;
      newCustomer.orgName = businessArea?.nameVN;

      const oldCustomer = await this.checkCumstomerExist(
        model.primaryTransaction.customer, businessArea?.companyCode
      );

      if (!oldCustomer) {
        const response = await this.httpService.postAsync(
          `${backendUrl}/msx-customer/api/domain/v1/customer/create-from-ycdco`,
          {
            ycdco: {customer: newCustomer}
          },
          {
            headers: {
              Authorization: headers.authorization,
              'Content-Type': 'application/json',
            },
          },
        )

        console.log(response);
      }

      model.primaryTransaction.customer = newCustomer;
      return model;
    } catch (error) {
      return model;
    }
  }

  public async checkCumstomerExist(customerInfo, orgCode) {
    let query = {};
    if (customerInfo.code) {
      query = { code: customerInfo.code };
    } else {
      if (customerInfo.type === CustomerTypeEnum.INDIVIDUAL && customerInfo.personalInfo.identities[0].value){
        query = {
          "personalInfo.identities.value":
            customerInfo.personalInfo.identities[0].value,
          "personalInfo.identities.type":
            customerInfo.personalInfo.identities[0].type,
        };
      }
      else {
        query = {
          taxCode: customerInfo.taxCode
        };
      }
    }

    const customer = await this.customerClient.sendDataPromise(
      query,
      cmd.CUSTOMER.GET_INFO_WITH_QUERY
    );

    if (customer && Object.keys(customer).length > 0 && customer.orgCode.substring(0, 4) === orgCode)
      return customer;
    else {
      return null;
    }
  }
}
