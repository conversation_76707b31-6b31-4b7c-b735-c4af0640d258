import * as mongoose from 'mongoose';
import { AdjustmentVersion } from '../../shared/schemas/common.schema';
const uuid = require('uuid');

const ProjectSchema = new mongoose.Schema({
  id: { type: String, index: true },
  code: { type: String, index: true },
  name: { type: String, index: true },
}, { _id: false });

export const DebtCommissionSchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  isPublish: { type: Boolean, default: false },

  code: { type: String, index: true },
  name: { type: String, default: null },
  periodFrom: { type: String, default: null },
  periodTo: { type: String, default: null },
  period: { type: String, default: null },
  year: { type: String, default: null },
  type: { type: String, default: null },
  status: { type: String, default: null },
  project: { type: ProjectSchema, default: null },
  modifiedDate: { type: Date, default: () => Date.now() },
  modifiedBy: { type: String },
  createdBy: { type: String },
  createdDate: { type: Date, default: () => Date.now() },
  commissionPolicy: { type: Object },

  // save all adjustment upload versions
  adjustmentVersions: { type: [AdjustmentVersion] },

  isActive: { type: Number, default: 1 },
  softDelete: { type: Boolean, default: false },
  softDeleteReason: { type: String, default: '' },
});

DebtCommissionSchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
