import { Injectable } from "@nestjs/common";
import { PropertyClient } from "../mgs-sender/property.client";
import { CmdPatternConst } from "../shared/constant";
import { StsClient } from "../mgs-sender/sts.client";
const momentTz = require('moment-timezone');
import { BaseService, CmdPatternConst as CmdPatternConstNew, ErrorService } from "../../../shared-modules";
import _ = require('lodash');
import { OwnershipCertificateQueryRepository } from "./repository/ownership-certificate.query.repository";
import { Action } from "rxjs/internal/scheduler/Action";
import { StatusEnum } from "../shared/enum/primary-contract.enum";
import { CreateOwnershipCertificateDto, UpdateOwnershipCertificateDto } from "./dto/ownership-certificate.dto";
import { HandoverScheduleQueryService } from "../handover-schedule.queryside/service";
const uuid = require("uuid");
@Injectable()
export class OwnershipCertificateQueryService extends BaseService {
  private commandId: string;

  constructor(private readonly repository: OwnershipCertificateQueryRepository,
    private readonly propertyClient: PropertyClient,
    private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
    private readonly stsClient: StsClient,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }
  async findById(id: string, query?) {
    let ownershipCertificateConfig: any = await this.repository.findOne({ id: id, softDelete: false });
    if (!ownershipCertificateConfig) {
      return this.getResponse('OwnershipCertificate0004');
    }
    // if (query && query.supportEmployeeId && query.handOverDateFrom && query.handOverDateTo) {
    //       let queryEmployeeBusy = {
    //         "id": { $ne: query.handoverScheduleId },
    //         "supportEmployee.id": query.supportEmployeeId,
    //         "handoverStartTime": { $gte: new Date(momentTz(query.handOverDateFrom).tz('Asia/Ho_Chi_Minh')) },
    //         "handoverEndTime": { $lte: new Date(momentTz(query.handOverDateTo).tz('Asia/Ho_Chi_Minh')) }
    //       }
    //       const scheduleEmployeeSupport: any = await this.handoverScheduleQueryService.findAllByQuery(queryEmployeeBusy);

    //       // Xóa khung giờ mà nv bận
    //       scheduleEmployeeSupport.forEach((element: any) => {

    //         let dayWeek = momentTz(element.handoverStartTime).tz('Asia/Ho_Chi_Minh').day();
    //         let startTime = momentTz(element.handoverStartTime).tz('Asia/Ho_Chi_Minh').format('HH:mm');
    //         let endTime = momentTz().tz('Asia/Ho_Chi_Minh').format('HH:mm');
    //         switch (dayWeek) {
    //           case 0: {
    //             ownershipCertificateConfig.sundayFrame = ownershipCertificateConfig.sundayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //           case 1: {
    //             ownershipCertificateConfig.mondayFrame = ownershipCertificateConfig.mondayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //           case 2: {
    //             ownershipCertificateConfig.tuesdayFrame = ownershipCertificateConfig.tuesdayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //           case 3: {
    //             ownershipCertificateConfig.wednesdayFrame = ownershipCertificateConfig.wednesdayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //           case 4: {
    //             ownershipCertificateConfig.thursdayFrame = ownershipCertificateConfig.thursdayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //           case 5: {
    //             ownershipCertificateConfig.fridayFrame = ownershipCertificateConfig.fridayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //           case 6: {
    //             ownershipCertificateConfig.saturdayFrame = ownershipCertificateConfig.saturdayFrame.filter(x => !(x.startTime === startTime && x.endTime === endTime));
    //             break;
    //           }
    //         }
    //       });
    //     }
    return ownershipCertificateConfig;
  }
  async findAll(query, user = null) {
    const { search, startCreatedDate, endCreatedDate, project, createdBy } = query;
    let _query: any = {
      softDelete: false,
    };

    _query._fields = "project,orgCharts,isActive,createdDate,createdBy,updatedDate,updatedBy";

    if (search && search.trim() !== '') {
      _query.$or = [
        { 'project.name': { $regex: new RegExp(search), $options: 'i' } },
        // { 'project.code': { $regex: new RegExp(search), $options: 'i' } },
      ];
    }
    if (project && project.trim() !== '') {
      _query.project.id = project
    }
    if (createdBy) {
      const ids = createdBy.includes(',')
        ? createdBy.split(',').map(id => id.trim())
        : [createdBy.trim()];
      _query['createdBy.id'] = { $in: ids };
    }
    if (startCreatedDate && endCreatedDate) {
      _query.createdDate = {
        $gte: new Date(new Date(startCreatedDate).setHours(0, 0, 0, 0)),
        $lte: new Date(new Date(endCreatedDate).setHours(23, 59, 59, 999))
      };
    } else if (startCreatedDate) {
      _query.createdDate = {
        $gte: new Date(new Date(startCreatedDate).setHours(0, 0, 0, 0)),
        $lte: new Date()
      };
    } else if (endCreatedDate) {
      _query.createdDate = {
        $lte: new Date(new Date(endCreatedDate).setHours(23, 59, 59, 999))
      };
    }


    const page = parseInt(query["page"]) || 1;
    const pageSize = parseInt(query["pageSize"]) || 10;

    const OwnershipCertificateConfig = await this.repository.findAll(page, pageSize, _query);

    if (_.isEmpty(OwnershipCertificateConfig.rows)) {
      return {
        rows: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      };
    }
    OwnershipCertificateConfig.rows.forEach(row => {
      // Kiểm tra nếu items có giá trị (không phải null hoặc undefined)
      if (row.items) {
        row.items = row.items.length;  // hoặc row.items = row.items.length.toString(); nếu bạn muốn string
      } else {
        row.items = 0;  // Nếu items là null hoặc undefined thì gán giá trị mặc định là 0
      }
    });

    return OwnershipCertificateConfig;
  }
  async create(user: any, dto: CreateOwnershipCertificateDto) {
    const model: any = { ...dto };

    if (model.isActive === StatusEnum.ACTIVE) {
      const ownershipCertificateExist = await this.repository.findOne({ "project.id": model.project.id, isActive: StatusEnum.ACTIVE, softDelete: false });
      if (ownershipCertificateExist) {
        return this.getResponse("OWNERSHIPCETIFICATE0006");
      }
    }
    const errorCode = this.validateOwnershipCertificateCommon(dto);
    if (errorCode) {
      return this.getResponse(errorCode);
    }

    this.commandId = uuid.v4();
    model.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    model.createdBy =
    {
      id: user.id,
      name: user.fullName,
      username: user.username,
    };
    model.createdDate = new Date();

    const projects = await this.propertyClient.sendDataPromise(
      {
        query: { id: model.project.id },
        fields: { id: 1, imageUrl: 1, code: 1, name: 1, type: 1 }
      },
      CmdPatternConstNew.PROJECT.GET_PROJECT_DROPDOWN_LIST
    );

    if (Array.isArray(projects) && projects.length > 0) {
      model.project = projects[0];
    }
    else {
      return this.getResponse("OWNERSHIPCETIFICATE0004"); // Ví dụ mã lỗi mới
    }
    model.id = this.commandId;
    await this.repository.create(model);
    return this.getResponse(0, { id: model.id });
  }
  async update(user: any, dto: UpdateOwnershipCertificateDto) {

    const model: any = { ...dto };
    const ownershipCertificateOld: any = await this.repository.findOne({ id: dto.id, softDelete: false });
    if (!ownershipCertificateOld) {
      return this.getResponse("OWNERSHIPCETIFICATE0005");
    }
    if (model.isActive === StatusEnum.ACTIVE) {
      const ownershipCertificateExist = await this.repository.findOne({ "project.id": model.project.id, isActive: StatusEnum.ACTIVE, id: { $ne: model.id }, softDelete: false });
      if (ownershipCertificateExist) {
        return this.getResponse("OWNERSHIPCETIFICATE0006");
      }
    }

    const errorCode = this.validateOwnershipCertificateCommon(dto);
    if (errorCode) {
      return this.getResponse(errorCode);
    }

    this.commandId = uuid.v4();
    model.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    model.updatedDate = new Date();
    model.createdBy = ownershipCertificateOld.createdBy;
    model.createdDate = ownershipCertificateOld.createdDate;
    model.project = ownershipCertificateOld.project;
    await this.repository.update(model);
    return this.getResponse(0, { id: model.id });

  }
  private validateOwnershipCertificateCommon(dto: any) {
    // Validate hotline
    if (!/^\d+$/.test(dto.hotline)) {
      return "OWNERSHIPCETIFICATE0001";
    }

    // Validate khung giờ có tồn tại
    const FRAME_DAYS = [
      "mondayFrame", "tuesdayFrame", "wednesdayFrame",
      "thursdayFrame", "fridayFrame", "saturdayFrame", "sundayFrame"
    ];
    const hasAnyFrame = FRAME_DAYS.some(day => dto[day]?.length > 0);
    if (!hasAnyFrame) {
      return "OWNERSHIPCETIFICATE0002";
    }

    // Validate không overlap khung giờ
    for (const dayKey of FRAME_DAYS) {
      const frames = dto[dayKey];
      if (frames?.length > 1 && !this.validateFrame(frames)) {
        return "OWNERSHIPCETIFICATE0007";
      }
    }

    // Validate items
    const itemGroups = [
      dto.itemsCerInProcess,
      dto.itemsForCerReadyHandover,
      dto.itemsForEligible
    ];
    for (const group of itemGroups) {
      if (group?.some(item => item.name == null)) {
        return "OWNERSHIPCETIFICATE0003";
      }
    }
    return null;
  }

  async delete(user: any, dto: any) {
    const ownershipCertificateOld: any = await this.repository.findOne({ id: dto.id, softDelete: false });
    // const ownershipCertificateOld: any = await this.repository.findOne({ id: dto.id, softDelete: false, 'createdBy.id': user.id });
    if (!ownershipCertificateOld) {
      return this.getResponse("OWNERSHIPCETIFICATE0005");
    }
    this.commandId = uuid.v4();
    ownershipCertificateOld.softDelete = true;
    ownershipCertificateOld.reasonDelete = dto.reasonDelete;
    ownershipCertificateOld.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    ownershipCertificateOld.updatedDate = new Date();
    await this.repository.update(ownershipCertificateOld);
    return this.getResponse(0);
  }
  async changeStatus(user: any, dto: any) {
    const ownershipCertificateOld: any = await this.repository.findOne({ id: dto.id, softDelete: false });
    if (!ownershipCertificateOld) {
      return this.getResponse("OWNERSHIPCETIFICATE0005");
    }

    if (ownershipCertificateOld.isActive === StatusEnum.INACTIVE) {
      const ownershipCertificateExist = await this.repository.findOne({ "project.id": ownershipCertificateOld.project.id, isActive: StatusEnum.ACTIVE, id: { $ne: ownershipCertificateOld.id }, softDelete: false });
      if (ownershipCertificateExist) {
        return this.getResponse("OWNERSHIPCETIFICATE0006");
      }
    }

    this.commandId = uuid.v4();
    function toggleStatus(currentStatus: StatusEnum): StatusEnum {
      return currentStatus === StatusEnum.ACTIVE ? StatusEnum.INACTIVE : StatusEnum.ACTIVE;
    }

    ownershipCertificateOld.isActive = toggleStatus(ownershipCertificateOld.isActive);

    ownershipCertificateOld.updatedBy = {
      id: user.id,
      name: user.fullName,
      username: user.username,
    }
    ownershipCertificateOld.updatedDate = new Date();
    await this.repository.update(ownershipCertificateOld);
    return this.getResponse(0, { id: ownershipCertificateOld.id });
  }

  validateFrame(frame) {
    // Chuyển thời gian HH:mm sang phút
    const toMinutes = (time) => {
      const [h, m] = time.split(':').map(Number);
      return h * 60 + m;
    };

    // Sắp xếp theo startTime để đảm bảo thứ tự
    const frames = [...frame].sort(
      (a, b) => toMinutes(a.startTime) - toMinutes(b.startTime)
    );

    for (let i = 0; i < frames.length - 1; i++) {
      const currentEnd = toMinutes(frames[i].endTime);
      const nextStart = toMinutes(frames[i + 1].startTime);

      if (currentEnd >= nextStart) {
        return false
      }
    }
    return true
  }
}
