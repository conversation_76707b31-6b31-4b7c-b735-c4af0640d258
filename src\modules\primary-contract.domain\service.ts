import { LiquidationStatusEnum, LiquidationTypeEnum } from './../shared/enum/liquidation.enum';
import { BadRequestException, HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { CommandBus } from "@nestjs/cqrs";
import { CreatePrimaryContractCommand } from "./commands/impl/create-primary-contract.cmd";
import { UpdatePrimaryContractCommand } from "./commands/impl/update-primary-contract.cmd";
import { DeletePrimaryContractCommand } from "./commands/impl/delete-primary-contract.cmd";
import { PrimaryContractQueryRepository } from "../primary-contract.queryside/repository/primary-contract-query.repository";
import { MsxLoggerService } from "../logger/logger.service";
import { CodeGenerateService } from "../code-generate/service";
import { CmdPatternConst, CommonConst } from "../shared/constant";
import { Action } from "../shared/enum/action.enum";
import {
  CreatePrimaryContractDto,
  PrimaryContractStatusDto, SendPrimaryContractDeliveryNotifyDto,
  UpdateDepositConfirmDto,
  UpdateInterestCalculationDto, UpdateManyPrimaryContract, UpdatePrimaryContractDeliveryDateDto,
  UpdatePrimaryContractDto, UpdatePrimaryContractFileDto, UpdateShowReceiptDto
} from "./dto/primary-contract.dto";
import { PropertyClient } from "../mgs-sender/property.client";
import { ErrorConst } from "../shared/constant/error.const";
import { PolicyQueryService } from "../policy.queryside/service";
import {
  ActionContractName,
  ContractEnum,
  ContractTypeEnum,
  DiscountTypeEnum,
  DiscountTypeRealEstateEnum,
  PolicyTypeEnum,
  ScheduleInstallmentEnum,
  StatusContractEnum,
  StatusEnum
} from "../shared/enum/primary-contract.enum";
import { CommonUtils } from "../shared/classes/class-utils";
import { HandoverStatusEnum, InterestCalculationStatusEnum, TransactionStatusEnum } from "../shared/enum/status.enum";
import moment = require("moment");
const momentTz = require('moment-timezone');
import { NotificationClient } from "../mgs-sender/notification.client";
import { CareClient } from "../mgs-sender/care.client";
import { HandoverQueryService } from "../handover.queryside/service";
import { MailerClient } from "../mgs-sender/mailer.client";
import { LiquidationQueryRepository } from "../liquidation.queryside/repository/liquidation.query.repository";
import { TransferHistoryRepository } from "../transfer-history/repository/transfer-history.query.repository";
import * as _ from "lodash";
import { PermissionEnum } from '../shared/enum/permission.enum';
import { UploadClient } from "../mgs-sender/uploader.client";
import { HistoryImportQueryRepository } from "../import-history.queryside/repository/query.repository";
import { TransactionClient } from "../mgs-sender/transaction.client";
import { SyncErpClient } from '../mgs-sender/syncErp.client';
import { SocialClient } from "../mgs-sender/social.client";
import { HandoverScheduleQueryService } from "../handover-schedule.queryside/service";
import { HandoverScheduleActionNameConst, HandoverScheduleStatusNameConst } from '../shared/constant/handover.const';
import { expiredDateType } from '../shared/enum/policies.enum';
import { OwnershipCertificateQueryRepository } from '../ownership-certificate/repository/ownership-certificate.query.repository';
import { notificationDto, notificationPayloadDto } from '../../../shared-modules/core/noti/noti.dto';
import { NotiService } from '../../../shared-modules/core/noti/service';
import { BaseService, ErrorService } from '../../../shared-modules';

const uuid = require("uuid");
const clc = require("cli-color");

@Injectable()
export class PrimaryContractDomainService extends BaseService {
  private readonly context = PrimaryContractDomainService.name;
  private commandId: string;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly loggerService: MsxLoggerService,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly queryRepository: PrimaryContractQueryRepository,
    private readonly propertyClient: PropertyClient,
    private readonly careClient: CareClient,
    private readonly policyQueryService: PolicyQueryService,
    private readonly notificationClient: NotificationClient,
    private readonly handoverQueryService: HandoverQueryService,
    private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
    private readonly liquidationQueryRepository: LiquidationQueryRepository,
    private readonly mailerClient: MailerClient,
    private readonly transferHistoryRepository: TransferHistoryRepository,
    private readonly historyRepository: HistoryImportQueryRepository,
    private readonly uploadClient: UploadClient,
    private readonly transactionClient: TransactionClient,
    private readonly syncErpClient: SyncErpClient,
    private readonly ownershipCertificateQueryRepository: OwnershipCertificateQueryRepository,
    private readonly socialClient: SocialClient,
    private readonly notiService: NotiService,
    public readonly errorService: ErrorService,
  ) {
    super(errorService);
  }
  async createContract(user: any, dto: CreatePrimaryContractDto, actionName: string) {
    this.loggerService.log(this.context, clc.green('create service'));
    let amount, price = 0, landPrice = 0, housePrice = 0;
    let receipt = [];
    let projectSetting = {};
    const model: any = { ...dto };
    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    model.createdBy = user.id;
    model.createdDate = new Date();
    let primaryTransaction;
    if (dto.primaryTransactionId) {
      primaryTransaction = await this.propertyClient.sendDataPromise({
        action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
        id: dto.primaryTransactionId
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID);
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'transactionId', 'id', dto.primaryTransactionId)
        });
      }
      if (primaryTransaction.contract) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.primaryTransactionId)
        });
      }
      if (dto.customer2 && dto.customer2.name) {
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(dto.customer2);
      }
      model.primaryTransaction = primaryTransaction;


      if (dto.calcContractPrice) {
        price = primaryTransaction.propertyUnit.contractPrice;
        housePrice = 0;
        landPrice = 0;
      } else {
        price = dto.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
        housePrice = dto.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
        landPrice = dto.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee = {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      receipt = primaryTransaction.reciept ? primaryTransaction.reciept : [];
      receipt = receipt.filter(r => r.status === 'TRANSFERED');

      amount = primaryTransaction.amount;
      projectSetting = primaryTransaction.project.setting;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'transactionId', 'id', 'null')
      });
    }

    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIds(dto.policyDiscountIds);
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'discountIds', 'id', dto.policyDiscountId)
        });
      }
      model.policyDiscounts = policyDiscounts;


      // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (
          policyDiscounts.some(
            e => e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
              e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, 'discountIds', 'id', dto.policyDiscountId)
          });
        }
      } else {
        if (
          policyDiscounts.some(e => !e.discount.typeRealEstate || e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, 'discountIds', 'id', dto.policyDiscountId)
          });
        }
      }
    } else {
      model.policyDiscounts = [];
    }
    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findOne({ id: dto.policyPaymentId, type: PolicyTypeEnum.PAYMENT });
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'scheduleId', 'id', dto.policyPaymentId)
        });
      }
      model.policyPayment = this.transformPolicyPayment(
        model,
        price,
        projectSetting,
        dto.signedDate,
        receipt,
        policyPayment,
        housePrice,
        landPrice,
        dto.maintenanceFee);
    }

    if (!model.code) {
      const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-`;
      model.code = await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    }
    model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`
    if (model.policyPayment) {
      model.paymentPercent = this.calPaymentPercentage({
        installments: model.policyPayment.schedule.installments
      }, model);

    }

    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);
    let histories = {
      propertyUnitId: primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: "",
      contractStatus: "Hợp đồng: " + ContractTypeEnum.CREATED,
      actionName: ActionContractName.CREATE_CONTRACT,
      modifiedDate: new Date(),
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return { id: model.id };
  }

  async createPurchaseContract(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green('create service'));

    const depositDto: any = await this.queryRepository.findOne({
      'deposit.id': dto.id,
      'type': { $in: [ContractEnum.PURCHASE, ContractEnum.RENT] }
    });

    if (depositDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng cọc', 'id', dto.id)
      });
    }

    const model: any = { ...dto };
    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    model.createdBy = user.id;
    model.createdDate = new Date();
    let projectInfo: any = {};
    let _fields: any = {
      id: 1,
      ownership: 1,
    };
    const projectIds = [... new Set(model.primaryTransaction.project.id)];
    projectInfo = await this.propertyClient.sendDataPromise({ projectIds, _fields }, CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS);
    if (projectInfo && projectInfo.ownership && projectInfo.ownership === CommonConst.HINH_THUC.SO_HUU.SU_DUNG_VINH_VIEN) {
      model.type = ContractEnum.PURCHASE;
    } else {
      model.type = ContractEnum.RENT;
    }

    model.deposit = {
      id: dto.id,
      code: dto.code,
      name: dto.name
    };

    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findOne({ id: dto.policyPaymentId, type: PolicyTypeEnum.PAYMENT });
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'scheduleId', 'id', dto.policyPaymentId)
        });
      }
      // chỉ tính lại khi thay đổi chính sách
      if (dto.policyPaymentId !== model.policyPayment.id) {
        const receiptList = this.getTransferredReceiptList(model);
        model.policyPayment = this.transformPolicyPayment(
          model,
          model.primaryTransaction.propertyUnit.price,
          {},
          dto.signedDate,
          receiptList,
          policyPayment,
          model.primaryTransaction.propertyUnit.housePrice,
          model.primaryTransaction.propertyUnit.landPrice);
      }
    }

    let prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_PURCHASE_CONTRACT}${model.primaryTransaction.project.code}-`;
    model.code = await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`;
    model.status = StatusContractEnum.INIT;
    delete (model.id);
    delete (model.interestCalculations);

    if (model.policyPayment) {
      model.paymentPercent = this.calPaymentPercentage({
        installments: model.policyPayment.schedule.installments
      }, model);
    }

    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);
    return { id: model.id };
  }

  async requestApproveContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'request approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (oldDto.status !== StatusContractEnum.INIT && oldDto.status !== StatusContractEnum.REJECTED) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "oldDto-Status") });
    }
    if (dto.status !== StatusContractEnum.WAITING) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;

    let result = await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    // Lưu vào lịch sử sản phẩm
    let histories = {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      contractStatus: "Hợp đồng: " + ContractTypeEnum.WAITTING_APPROVAL,
      actionName: ActionContractName.REQUEST_APPROVAL_CONTRACT,
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result
  }

  async liquidationContract(userId: string, liquidation: any, actionName: string) {
    this.loggerService.log(this.context, 'request approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: liquidation.contract.id });
    if (!oldDto) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0018" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", liquidation.contract.id)});
    }

    if (oldDto.status !== StatusContractEnum.APPROVED) {
      throw new HttpException({ errorCode: "PRIMARYCONTRACT0017" }, HttpStatus.OK);
      // throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus")});
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = userId;
    if (liquidation.type === LiquidationTypeEnum.TRANSFER) {
      oldDto.status = StatusContractEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND;
    } else {
      oldDto.status = StatusContractEnum.LIQUIDATED;
      oldDto.liquidate = new Date()
    }
    oldDto.liquidation = liquidation;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto);
  }

  async cloneContract(userId: string, liquidationDto: any, actionName: string, newTicket) {
    this.loggerService.log(this.context, 'clone contract');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: liquidationDto.contract.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", liquidationDto.contract.id) });
    }

    if (oldDto.status !== StatusContractEnum.APPROVED) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }
    const model: any = _.cloneDeep(oldDto);
    this.commandId = uuid.v4();
    model.modifiedBy = userId;
    model.createdDate = new Date();
    model.modifiedDate = new Date();
    model.primaryTransaction = newTicket;
    model.liquidation = liquidationDto;
    model.transferConfirmFromCustomer = false;
    model.status = LiquidationStatusEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND;
    if (model.policyPayment?.schedule?.installments) {
      model.policyPayment.schedule.installments = model.policyPayment.schedule.installments.map(e => ({
        ...e,
        totalTransfered: 0,
        receipts: []
      }));
    }

    model.oldContract = {
      id: oldDto.id,
      code: oldDto.code,
      name: oldDto.name,
    }

    model.id = uuid.v4();
    model.isTransferred = true;

    // if (!model.code) {
    const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_TRANSFER_CONTRACT}${liquidationDto.proposal.escrowTicket.project.code}-`;
    model.code = await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    // }

    model.name = `${model.code}-${liquidationDto.proposal.customerTransfer.name}`
    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);

    // update thông tin contract vào sản phẩm
    const propertyUnitId = model.primaryTransaction.propertyUnit.id;
    this.propertyClient.sendDataPromise({
      id: propertyUnitId,
      contract: {
        id: model.id,
        code: model.code,
        type: model.type,
        isTransferred: model.isTransferred
      }
    }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CONTRACT_IN_PROPERTY_UNIT);

    return {
      id: model.id,
      installments: oldDto.policyPayment?.schedule?.installments,
      oldContract: model.oldContract,
    };
  }

  async approveContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (oldDto.status !== StatusContractEnum.WAITING) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }
    if (dto.status !== StatusContractEnum.ACCOUNTANT_WAITING && dto.status !== StatusContractEnum.REJECTED) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }

    if (dto.status === StatusContractEnum.ACCOUNTANT_WAITING) {
      //  create resident
      let projectInfo: any = {};
      projectInfo = await this.propertyClient.sendDataPromise(oldDto.primaryTransaction.project, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);
      let projectPassing: any = { ...oldDto.primaryTransaction.project };
      let newObject = {};
      if (projectInfo) {
        newObject = {
          ...oldDto.primaryTransaction, project: projectInfo,
          customer: oldDto.primaryTransaction.customer,
          propertyUnit: oldDto.primaryTransaction.propertyUnit,
          contractId: oldDto.id
        };
      }

      // send transaction to care service
      await this.careClient.sendDataPromise({
        primaryTransaction: newObject
      }, CmdPatternConst.CARE.GET_TRANSACTION);

      await this.socialClient.sendDataPromise({
        projectId: oldDto.primaryTransaction.project.id
      }, CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP);

      const customer = await this.careClient.sendDataPromise({
        identityNumber: oldDto.primaryTransaction.customer.identities[0].value
      }, CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY);

      if (customer) {
        this.notificationClient.createNotificationCare(
          "care_depositContract_Approved",
          null,
          customer.id,
          "primary-contract",
          oldDto.id,
          {
            code: oldDto.primaryTransaction.propertyUnit.code,
            projectCode: oldDto.primaryTransaction.project.code,
            projectName: oldDto.primaryTransaction.project.name
          }
        );
      }

      // const {personalInfo, info} = (oldDto['primaryTransaction'].customer || {}) || {};
      // const { email, phone, name, identities } = personalInfo;
      // const { address, birthday, gender } = info;

      // if(email && phone ) {
      //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)=> {

      //     if(!customer) {
      //       const payload = { personalInfo: { name, email, phone, identities }, accessSystem: user.notiSystem ? [user.notiSystem] : null  };
      //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

      //     }else if(!customer.isActive) {
      //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
      //     }
      //   })
      // }
    }

    // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
    if (oldDto.policyPayment && oldDto.policyPayment.schedule.installments[0].totalTransfered >= oldDto.policyPayment.schedule.installments[0].totalAmount) {
      oldDto.status = StatusContractEnum.APPROVED;
    } else {
      oldDto.status = dto.status;
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.reason = dto.reason;

    let result = await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    // Lưu vào lịch sử sản phẩm
    let histories = {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: oldDto.reason,
      contractStatus: oldDto.status === StatusContractEnum.ACCOUNTANT_WAITING ? "Hợp đồng: " + ContractTypeEnum.WAITING_COLLECT_MONEY : "Hợp đồng: " + ContractTypeEnum.REJECTED,
      actionName: oldDto.status === StatusContractEnum.ACCOUNTANT_WAITING ? ActionContractName.APPROVAL_CONTRACT : ActionContractName.REJECT_CONTRACT,
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result;
  }

  async approveLiquidationContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'approveLiquidationContract');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (oldDto.status !== StatusContractEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }
    if (dto.status !== StatusContractEnum.APPROVED && dto.status !== StatusContractEnum.LIQUIDATED) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.status = dto.status;
    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
    const liquidation = await this.liquidationQueryRepository.findOne({ "contract.id": dto.id })
    if (dto.status === StatusContractEnum.LIQUIDATED) {
      const newContract: any = await this.queryRepository.findOne({ 'oldContract.id': dto.id });
      // create history
      const history = {
        from: {
          contract: {
            id: oldDto.id,
            code: oldDto.code,
            name: oldDto.name,
          },
          customer: oldDto.primaryTransaction.customer,
          date: liquidation.liquidationDate
        },
        to: {
          contract: {
            id: newContract.id,
            code: newContract.code,
            name: newContract.name
          },
          customer: newContract.primaryTransaction.customer,
          date: liquidation.liquidationDate
        },
        propertyUnit: {
          id: newContract.primaryTransaction.propertyUnit.id,
          code: newContract.primaryTransaction.propertyUnit.code
        }
      };
      await this.transferHistoryRepository.create(history);
      await this.liquidationQueryRepository.updateStatus({
        "contract.id": dto.id
      }, StatusContractEnum.APPROVED);
    }
  }

  async approvePurchaseContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, 'approve ticket');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (oldDto.status !== StatusContractEnum.WAITING) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }
    if (dto.status !== StatusContractEnum.ACCOUNTANT_WAITING && dto.status !== StatusContractEnum.REJECTED) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }

    // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
    const existedInstallmentToContractIndex = oldDto.policyPayment ? (oldDto.policyPayment.schedule.installments as Array<any>).findIndex(item => item.isToContract === true) : -1;
    if (existedInstallmentToContractIndex >= 0 && oldDto.policyPayment.schedule.installments[existedInstallmentToContractIndex].totalTransfered >= oldDto.policyPayment.schedule.installments[existedInstallmentToContractIndex].totalAmount) {
      oldDto.status = StatusContractEnum.APPROVED;
    } else {
      oldDto.status = dto.status;
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.reason = dto.reason;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async updateContractFiles(user: any, dto: UpdatePrimaryContractFileDto, actionName: string) {
    this.loggerService.log(this.context, 'update contract file');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.files = dto.files;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async updateDeliveryDate(user: any, dto: UpdatePrimaryContractDeliveryDateDto, actionName: string) {
    this.loggerService.log(this.context, 'update contract delivery date');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;
    oldDto.deliveryDate = dto.deliveryDate;
    oldDto.filesDelivery = dto.filesDelivery;
    if (oldDto.handoverStatus && oldDto.handoverStatus != HandoverStatusEnum.handed) {
      oldDto.handoverStatus = HandoverStatusEnum.handed;
      let syncConfig = await this.syncErpClient.sendDataPromise(oldDto.primaryTransaction.project.id, CmdPatternConst.SYNC_ERP.GET_CAMPAIGN_ERP_BY_PROJECT_ID);
      if (syncConfig && oldDto.syncErpData && oldDto.syncErpData.contractid) {
        let property: any = '';
        if (oldDto.primaryTransaction && oldDto.primaryTransaction.propertyUnit && oldDto.primaryTransaction.propertyUnit.attributes && oldDto.primaryTransaction.propertyUnit.attributes.length > 21) {
          if (oldDto.primaryTransaction.propertyUnit.attributes[21].value) {
            property = oldDto.primaryTransaction.propertyUnit.attributes[21].value;
          }
        }

        let data: any = {
          formid: 'contract01',
          contractid: oldDto.syncErpData.contractid,
          property: property,
          statusdate: oldDto.deliveryDate ? moment(oldDto.deliveryDate).format('YYYY-MM-DD hh:mm:ss A') : '',
          status: 'BGI',
          impstatus: 'W'
        }
        let dataSendCRM: any = {
          action: 'contract01',
          data: [data]
        }
        // send data sync erp
        await this.syncErpClient.sendDataPromise(dataSendCRM, CmdPatternConst.SYNC_ERP.SEND_REQUEST_TO_ERP);
      }


    }
    // Cập nhật đã bàn giao căn hộ
    let updatePropery = {
      query: { id: oldDto.primaryTransaction.propertyUnit.id },
      model: { $set: { "contract.handoverStatus": HandoverStatusEnum.handed } }
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);

    // Xác nhận bàn giao
    this.addHistoriesHandover(oldDto, user, dto.deliveryDate, HandoverScheduleStatusNameConst.HANDED_OVER, HandoverScheduleActionNameConst.ACCEPT_HANDOVER);

    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async addHistoriesHandover(contract, user, handoverStartTime, propertyStatusName, actionName) {
    let historiesHandover = {
      propertyStatusName: propertyStatusName,
      actionName: actionName, // Hành động
      modifiedBy: user.id,
      modifiedByName: user.name, // Tên người thực hiện
      handoverStartTime: handoverStartTime
    }
    let updatePropery = {
      query: { id: contract.primaryTransaction.propertyUnit.id },
      model: { $push: { historiesHandover: historiesHandover } }
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
  }

  async updateContract(user: any, dto: UpdatePrimaryContractDto, actionName: string) {
    this.loggerService.log(this.context, clc.green('update service'));
    let amount, price = 0, housePrice = 0, landPrice = 0;
    let receipt = [];
    let projectSetting = {};
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });

    if (!oldDto
      || oldDto.status === StatusContractEnum.APPROVED && !user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_UPDATE_APPROVEMENT)) {
      throw new BadRequestException();
    }

    if (dto.primaryTransactionId) {
      const primaryTransaction = await this.propertyClient.sendDataPromise({
        action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
        id: dto.primaryTransactionId
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID);
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'transactionId', 'id', dto.primaryTransactionId)
        });
      }

      // if(primaryTransaction.contract && primaryTransaction.contract.id !== dto.id){
      //   throw new BadRequestException({
      //     errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.primaryTransactionId)
      //   });
      // }

      if (dto.customer2 && dto.customer2.name) {
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(dto.customer2);
      }

      if (dto.calcContractPrice) {
        price = primaryTransaction.propertyUnit.contractPrice;
        housePrice = 0;
        landPrice = 0;
      } else {
        price = dto.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
        housePrice = dto.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
        landPrice = dto.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee = {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      amount = primaryTransaction.amount;
      receipt = primaryTransaction.reciept;
      projectSetting = primaryTransaction.project.setting;
      oldDto.primaryTransaction = primaryTransaction;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'transactionId', 'id', 'null')
      });
    }

    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIds(dto.policyDiscountIds);
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'discountIds', 'id', dto.policyDiscountId)
        });
      }

      // Chỉ được áp dụng chiết khấu nhà hoặc đất nếu có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (policyDiscounts.some(
          e => e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
            e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, 'discountIds', 'id', dto.policyDiscountId)
          });
        }
      } else {
        if (
          policyDiscounts.some(e => !e.discount.typeRealEstate || e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, 'discountIds', 'id', dto.policyDiscountId)
          });
        }
      }
      oldDto.policyDiscounts = policyDiscounts;
    } else {
      oldDto.policyDiscounts = [];
    }

    oldDto.calcCurrencyFirst = dto.calcCurrencyFirst;
    oldDto.calcPriceVat = dto.calcPriceVat;
    oldDto.calcContractPrice = dto.calcContractPrice;
    oldDto.maintenanceFee = dto.maintenanceFee;
    oldDto.changeInstallment = dto.changeInstallment;
    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findById(dto.policyPaymentId);
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'scheduleId', 'id', dto.policyPaymentId)
        });
      }
      oldDto.policyPayment = this.transformPolicyPayment(
        oldDto,
        price,
        projectSetting,
        dto.signedDate,
        receipt,
        policyPayment,
        housePrice,
        landPrice,
        dto.maintenanceFee);
    }

    oldDto.startDate = dto.startDate;
    oldDto.expiredDate = dto.expiredDate;
    oldDto.signedDate = dto.signedDate;
    oldDto.transferType = dto.transferType;
    oldDto.isDebtRemind = dto.isDebtRemind;
    oldDto.isShowedInstallment = dto.isShowedInstallment;
    oldDto.files = dto.files;
    oldDto.companyInformation = dto.companyInformation;
    oldDto.releaseStartDate = dto.releaseStartDate;
    oldDto.releaseEndDate = dto.releaseEndDate;



    if (oldDto.policyPayment) {
      oldDto.paymentPercent = this.calPaymentPercentage({
        installments: oldDto.policyPayment.schedule.installments
      }, oldDto);
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;

    let result = await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
    // update hợp đồng mua bán
    this.updateMany({ 'deposit.id': dto.id }, {
      releaseStartDate: dto.releaseStartDate,
      releaseEndDate: dto.releaseEndDate
    });

    // Lưu vào lịch sử sản phẩm.
    let histories = {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: dto.reason,
      contractStatus: oldDto.status === StatusContractEnum.APPROVED ? "Hợp đồng: " + ContractTypeEnum.APPROVED : "Hợp đồng: " + ContractTypeEnum.CREATED,
      actionName: ActionContractName.EDIT_CONTRACT,
      modifiedDate: new Date(),
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result;
  }

  async updatePurchaseContract(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green('update service'));
    let amount, price = 0, housePrice = 0, landPrice = 0;
    let receipt = [];
    let projectSetting = {};
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });

    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'purchase', 'id', dto.id)
      });
    }

    if (!oldDto) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND)
      });
    }

    if (oldDto.status === StatusContractEnum.APPROVED && !user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_UPDATE_APPROVEMENT)) {
      throw new BadRequestException(ErrorConst.UNAUTHORIZED);
    }

    const depositDto: any = await this.queryRepository.findOne({
      'deposit.id': dto.depositId,
      'type': { $in: [ContractEnum.PURCHASE, ContractEnum.RENT] }
    });
    if (dto.deposit && dto.depositId !== dto.deposit.id && depositDto && depositDto.id !== dto.depositId) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng cọc', 'id', dto.depositId)
      });
    }

    // check trường hợp có  primary transaction
    if (dto.primaryTransactionId) {
      const primaryTransaction = await this.propertyClient.sendDataPromise({
        action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
        id: dto.primaryTransactionId
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID);
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'transactionId', 'id', dto.primaryTransactionId)
        });
      }

      // if(primaryTransaction.contract && primaryTransaction.contract.id !== dto.id){
      //   throw new BadRequestException({
      //     errors: ErrorConst.Error(ErrorConst.EXISTED, 'Hợp đồng', 'id', dto.primaryTransactionId)
      //   });
      // }

      if (dto.customer2 && dto.customer2.name) {
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(dto.customer2);
      }

      if (dto.calcContractPrice) {
        price = primaryTransaction.propertyUnit.contractPrice;
        housePrice = 0;
        landPrice = 0;
      } else {
        price = dto.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
        housePrice = dto.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
        landPrice = dto.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee = {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      amount = primaryTransaction.amount;
      receipt = primaryTransaction.reciept;
      projectSetting = primaryTransaction.project.setting;
      oldDto.primaryTransaction = primaryTransaction;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'transactionId', 'id', 'null')
      });
    }

    price = oldDto.primaryTransaction.propertyUnit.price;


    // validate lại chiết khấu được áp dụng cho hợp đồng mua bán
    if (dto.policyDiscountIds && dto.policyDiscountIds.length) {
      const policyDiscounts: any[] = await this.policyQueryService.findByIds(dto.policyDiscountIds);
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'discountIds', 'id', dto.policyDiscountId)
        });
      }

      // Chỉ được áp dụng chiết khấu nhà hoặc đất nếu có giá nhà & đất
      if (price > 0 && !housePrice && !landPrice) {
        if (
          policyDiscounts.some(
            e => e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
              e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, 'discountIds', 'id', dto.policyDiscountId)
          });
        }
      } else {
        if (
          policyDiscounts.some(e => !e.discount.typeRealEstate || e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, 'discountIds', 'id', dto.policyDiscountId)
          });
        }
      }
      oldDto.policyDiscounts = policyDiscounts;
    } else {
      oldDto.policyDiscounts = [];
    }


    oldDto.calcCurrencyFirst = dto.calcCurrencyFirst;
    oldDto.calcPriceVat = dto.calcPriceVat;
    oldDto.calcContractPrice = dto.calcContractPrice;
    oldDto.maintenanceFee = dto.maintenanceFee // thay đổi đợt thu phí bảo trì cho hợp đồng mua bán
    if (dto.policyPaymentId) {
      const policyPayment = await this.policyQueryService.findById(dto.policyPaymentId);
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'scheduleId', 'id', dto.policyPaymentId)
        });
      }
      // chỉ tính lại khi thay đổi chính sách
      if (dto.policyPaymentId !== oldDto.policyPayment.id) {
        const receiptList = this.getTransferredReceiptList(oldDto);
        oldDto.policyPayment = this.transformPolicyPayment(
          oldDto,
          price,
          {},
          dto.signedDate,
          receiptList,
          policyPayment,
          oldDto.primaryTransaction.propertyUnit.housePrice,
          oldDto.primaryTransaction.propertyUnit.landPrice);
      }
    } else {
      oldDto.policyPayment = null;
    }

    oldDto.deposit = {
      id: dto.depositId,
      code: dto.depositCode,
      name: dto.depositName
    };
    oldDto.name = `${oldDto.code}-${dto.primaryTransaction.customer.personalInfo.name}`;

    oldDto.signedDate = dto.signedDate;
    oldDto.isDebtRemind = dto.isDebtRemind;
    oldDto.isShowedInstallment = dto.isShowedInstallment;
    oldDto.files = dto.files;
    oldDto.maintenanceFee = dto.maintenanceFee;
    oldDto.releaseStartDate = dto.releaseStartDate;
    oldDto.releaseEndDate = dto.releaseEndDate;

    if (oldDto.policyPayment) {
      oldDto.paymentPercent = this.calPaymentPercentage({
        installments: oldDto.policyPayment.schedule.installments
      }, oldDto);
    }

    this.commandId = uuid.v4();
    oldDto.modifiedBy = user.id;

    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    if (oldDto.deposit && oldDto.deposit.id) {
      // update hợp đồng mua bán
      return await this.updateMany({ 'id': oldDto.deposit.id }, {
        releaseStartDate: dto.releaseStartDate,
        releaseEndDate: dto.releaseEndDate
      });
    }
    return;
  }

  async updateInterestCalculation(user: any, dto: UpdateInterestCalculationDto, actionName: string) {
    this.loggerService.log(this.context, clc.green('updated service'));
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (dto.interestCalculation.id) { // update
      let idx = oldDto.interestCalculations.findIndex(i => i.id === dto.interestCalculation.id);
      if (idx > -1) {
        oldDto.interestCalculations[idx].interestReductionAmount = dto.interestCalculation.interestReductionAmount;
        oldDto.interestCalculations[idx].remainingAmount = oldDto.interestCalculations[idx].interestAmount - oldDto.interestCalculations[idx].interestAmountTransferred - oldDto.interestCalculations[idx].interestReductionAmount;
      }
    } else {
      if (!moment(dto.interestCalculation.startDate).toDate()) {
        throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, "interestCalculation", "startDate", dto.interestCalculation.startDate.toString()) });
      }
      if (!moment(dto.interestCalculation.endDate).toDate()) {
        throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, "interestCalculation", "endDate", dto.interestCalculation.endDate.toString()) });
      }
      dto.interestCalculation.id = uuid.v4();
      dto.interestCalculation.createdDate = new Date();
      const prefix = CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_INTEREST;
      dto.interestCalculation.code = await this.codeGenerateService.generateCode("", prefix);
      dto.interestCalculation.title = dto.interestCalculation.code + '/' + oldDto.name;
      dto.interestCalculation.interestRate = oldDto.primaryTransaction?.project?.setting?.interestRate || 0;
      dto.interestCalculation.interestReductionAmount = 0;
      dto.interestCalculation.remainingAmount = dto.interestCalculation.interestAmount - dto.interestCalculation.interestAmountTransferred - dto.interestCalculation.interestReductionAmount;
      dto.interestCalculation.status = InterestCalculationStatusEnum.init;
      oldDto.interestCalculations.push(dto.interestCalculation)
    }

    this.commandId = uuid.v4();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async approveInterestCalculation(user: any, dto: UpdateInterestCalculationDto, actionName: string) {
    this.loggerService.log(this.context, 'approve interest calculation');
    this.commandId = uuid.v4();
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (!dto.interestCalculation.id) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "interest", "ID", dto.interestCalculation.id) });
    }

    let idx = oldDto.interestCalculations.findIndex(i => i.id === dto.interestCalculation.id);
    if (idx > -1) {
      if (oldDto.interestCalculations[idx].status == InterestCalculationStatusEnum.init) {
        oldDto.interestCalculations[idx].status = InterestCalculationStatusEnum.approved;
      } else {
        throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "interest calculation status") });
      }
    }

    this.commandId = uuid.v4();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async updatePropertyUnits(units: any[]) {
    const contracts = await this.queryRepository.find({
      "primaryTransaction.propertyUnit.id": {
        $in: units.map(e => e.id)
      }
    });

    const update = contracts.map(async (contract: any) => {
      const newUnit = units.find(e => e.id === contract.primaryTransaction.propertyUnit.id);
      Object.assign(contract.primaryTransaction.propertyUnit, newUnit);
      if (contract?.policyPayment?.schedule?.installments && contract?.policyPayment?.schedule?.installments?.length) {
        if (contract.maintenanceFee && contract.maintenanceFee.type === DiscountTypeEnum.PERCENT) {
          let price = 0, housePrice = 0, landPrice = 0;
          if (contract.calcContractPrice) {
            price = contract.primaryTransaction.propertyUnit.contractPrice;
            housePrice = 0;
            landPrice = 0;
          } else {
            price = contract.calcPriceVat ? contract.primaryTransaction.propertyUnit.priceVat : contract.primaryTransaction.propertyUnit.price;
            housePrice = contract.calcPriceVat ? contract.primaryTransaction.propertyUnit.housePriceVat : contract.primaryTransaction.propertyUnit.housePrice;
            landPrice = contract.calcPriceVat ? contract.primaryTransaction.propertyUnit.landPriceVat : contract.primaryTransaction.propertyUnit.landPrice;
          }

          const policyPayment = await this.policyQueryService.findOne({ id: contract.policyPayment?.id, type: PolicyTypeEnum.PAYMENT });
          if (policyPayment) {
            const newPaymentPolicy = this.transformPolicyPayment(
              contract,
              price,
              contract.primaryTransaction.project.setting,
              contract.signedDate,
              [],
              policyPayment,
              housePrice,
              landPrice,
              {
                ...contract.maintenanceFee.toJSON(),
                contractPriceForMaintenanceFee: newUnit.contractPriceForMaintenanceFee
              }
            );

            for (let index = 0; index < contract.policyPayment.schedule.installments.length; index++) {
              const element = contract.policyPayment.schedule.installments[index];
              element.totalAmount = newPaymentPolicy.schedule.installments[index].totalAmount;
            }
          }
        }
      }

      return this.queryRepository.update(contract);
    });
    await Promise.all(update);
  }

  async updateManyPrimaryContract(user: any, dto: UpdateManyPrimaryContract, actionName: string) {
    this.loggerService.log(this.context, clc.green('update service'));
    if (dto.lstIdPrimaryContract && dto.lstIdPrimaryContract.length > 0) {
      await this.updateMany({ id: { $in: dto.lstIdPrimaryContract } }, {
        releaseStartDate: dto.startDate,
        releaseEndDate: dto.endDate
      });
      // update hợp đồng mua bán
      return await this.updateMany({ 'deposit.id': { $in: dto.lstIdPrimaryContract } }, {
        releaseStartDate: dto.startDate,
        releaseEndDate: dto.endDate
      });
    }
  }

  public transformPolicyPayment(contract, price, projectSetting, signedDate, receiptList, newPolicyPayment: any = {}, housePrice = 0, landPrice = 0, maintenanceFee: any = {}) {
    // không update lại đợt thanh toán nếu update và changeInstallment = true
    if (contract.id && contract.changeInstallment) {
      return contract.policyPayment;
    }

    // Kiểm tra nếu đã thanh toán đến đợt n thì CSTT mới phải có tối thiểu n đợt thanh toán
    if ((contract.policyPayment?.schedule?.installments || [])
      .reduce((acc, curr, index) => acc = curr.receipts && curr.receipts.length ? index : acc, -1) >
      (newPolicyPayment?.schedule?.installments?.length || 0)
    ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.PAYMENT_POLICY_INVALID, 'policyPaymentId', 'id', newPolicyPayment.id)
      });
    }


    // check chiết khấu
    if (contract.policyDiscounts && contract.policyDiscounts.length) {
      // Trường hợp không có giá nhà & đất => áp dụng chính sách chiết khấu mặc định
      if (price > 0 && !housePrice && !landPrice) {
        let currencyValue = contract.policyDiscounts
          .filter(e => e.discount.type === DiscountTypeEnum.CURRENCY && e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
          .reduce((acc, curr) => acc + parseFloat(curr.discount.value), 0);
        let percentValue = contract.policyDiscounts
          .filter(e => e.discount.type === DiscountTypeEnum.PERCENT && e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
          .reduce((acc, curr) => acc + parseFloat(curr.discount.value), 0);

        if (contract.calcCurrencyFirst) {
          price -= currencyValue;
          price -= ((percentValue / 100) * price);
        } else {
          price -= ((percentValue / 100) * price);
          price -= currencyValue;
        }
      } else {
        let currencyValueHouse = contract.policyDiscounts
          .filter(e => e.discount.type === DiscountTypeEnum.CURRENCY && e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE)
          .reduce((acc, curr) => acc + parseFloat(curr.discount.value), 0);
        let percentValueHouse = contract.policyDiscounts
          .filter(e => e.discount.type === DiscountTypeEnum.PERCENT && e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE)
          .reduce((acc, curr) => acc + parseFloat(curr.discount.value), 0);

        let currencyValueLand = contract.policyDiscounts
          .filter(e => e.discount.type === DiscountTypeEnum.CURRENCY && e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
          .reduce((acc, curr) => acc + parseFloat(curr.discount.value), 0);
        let percentValueLand = contract.policyDiscounts
          .filter(e => e.discount.type === DiscountTypeEnum.PERCENT && e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
          .reduce((acc, curr) => acc + parseFloat(curr.discount.value), 0);

        if (contract.calcCurrencyFirst) {
          housePrice -= currencyValueHouse;
          housePrice -= ((percentValueHouse / 100) * housePrice);

          landPrice -= currencyValueLand;
          landPrice -= ((percentValueLand / 100) * landPrice);
        } else {
          housePrice -= ((percentValueHouse / 100) * housePrice);
          housePrice -= currencyValueHouse;

          landPrice -= ((percentValueLand / 100) * landPrice);
          landPrice -= currencyValueLand;
        }
      }

      // check trường hợp nhập nhầm chiết khấu quá cao khiến giá bị âm
      if (price < 0) price = 0;
      if (housePrice < 0) housePrice = 0;
      if (landPrice < 0) landPrice = 0;
    }
    let totalCurrencyBeforeToContract = 0;
    signedDate = signedDate ? moment(signedDate) : moment();
    let nextDate = moment();
    let goContractIdx = newPolicyPayment.schedule.installments.length;
    let totalAmount = 0;

    const hasMaintenanceFromErp = newPolicyPayment.schedule.installments.some(i => i.name === '99');

    newPolicyPayment.schedule.installments.map((i, index) => {
      if (i.isToContract) {
        goContractIdx = index;
      }

      if ((housePrice > 0 || landPrice > 0) && !i.type2) {
        i.type2 = i.type;
        i.value2 = i.value;
      }

      // Trường hợp tách thanh toán nhà & đất
      if (i.type2) {
        // kiểm tra đã trừ đi tiền mặt đã đóng các đợt trước
        let flagSubTotalCurrencyBeforeToContract = false;
        i.totalAmount = 0;
        if (i.type === ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount += i.value;
          totalCurrencyBeforeToContract += i.value;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount += i.isToContract ? ((i.value * housePrice) / 100) - totalCurrencyBeforeToContract : (i.value * housePrice) / 100;
          flagSubTotalCurrencyBeforeToContract = i.isToContract;
        }
        if (i.type2 === ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount += i.value2;
          totalCurrencyBeforeToContract += i.value2;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount += i.isToContract && !flagSubTotalCurrencyBeforeToContract ? ((i.value2 * landPrice) / 100) - totalCurrencyBeforeToContract : (i.value2 * landPrice) / 100;
        }
      } else {
        if (i.type === ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount = i.value;
          totalCurrencyBeforeToContract += i.value;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount = i.isToContract ? ((i.value * price) / 100) - totalCurrencyBeforeToContract : (i.value * price) / 100;
        }
      }

      if ((index === newPolicyPayment.schedule.installments.length - 1 && !hasMaintenanceFromErp) || (hasMaintenanceFromErp && index === newPolicyPayment.schedule.installments.length - 2)) {
        i.totalAmount = (landPrice + housePrice > 0 ? landPrice + housePrice : price) - totalAmount
      } else {
        i.totalAmount = Math.round(i.totalAmount);
        totalAmount += i.totalAmount;
      }

      if (maintenanceFee.stagePayment && maintenanceFee.stagePayment == i.name && maintenanceFee.type && maintenanceFee.value) {
        i.name = `${i.name} (Bao gồm Phí bảo trì)`;
        if (maintenanceFee.type === DiscountTypeEnum.CURRENCY) {
          i.totalAmount += maintenanceFee.value;
        } else if (maintenanceFee.type === DiscountTypeEnum.PERCENT) {
          const maintenanceValue: any = maintenanceFee.contractPriceForMaintenanceFee * maintenanceFee.value / 100;
          i.totalAmount += Math.round(maintenanceValue);
        }
      }

      i.totalTransfered = 0;
      i.receipts = [];
      let needTransferred = i.totalAmount - i.totalTransfered;

      // khi thay đổi chính sách thanh toán, đem các phiếu thu đã thanh toán sang chính sách mới
      if (receiptList.length > 0) {
        receiptList.map((r, idx) => {
          if (needTransferred > 0 && r.amount > 0) {
            if (r.amount <= needTransferred) {
              i.receipts.push(Object.assign({}, { ...r }));
              i.totalTransfered += r.amount;
              needTransferred -= r.amount;
              receiptList[idx].amount = 0;
            } else {
              receiptList[idx].amount = r.amount - needTransferred;
              i.receipts.push(Object.assign({}, { ...r }, { amount: needTransferred }));
              i.totalTransfered += needTransferred;
              needTransferred = 0;
            }
          }
        });
      }

      if (contract.type === ContractEnum.DEPOSIT) {
        if (index === 0) {
          // tính ngày hạn thanh toán của Đợt 1
          if (i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            i.paymentDueDate = moment(i.exactDays).toDate()
            nextDate = moment(i.exactDays)
          } else {
            const projectBonusDate = projectSetting.dateToIssue || 0;
            i.paymentDueDate = projectBonusDate !== 0 ? signedDate.add(projectBonusDate, 'days').toDate() : signedDate.toDate();
            nextDate = signedDate.clone();
          }

        } else {
          // tính ngày hạn thanh toán của các đợt khác
          if (i.expiredDateType !== expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            nextDate = nextDate.add(i.expiredDays, 'days');
            i.paymentDueDate = nextDate.toDate();
          } else if (i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            nextDate = moment(i.exactDays)
            i.paymentDueDate = nextDate.toDate();
          }

        }
      } else {
        // HĐ không phải HĐ cọc
        if (index > goContractIdx) {
          if (index === (goContractIdx + 1)) {
            // tính ngày hạn thanh toán của Đợt tiếp sau khi ra HĐMB
            if (i.expiredDateType !== expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate = nextDate.add(i.expiredDays, 'days');
              i.paymentDueDate = nextDate.toDate();
            } else if (i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate = moment(i.exactDays)
              i.paymentDueDate = nextDate.toDate();
            }
          } else {
            // tính ngày hạn thanh toán của các đợt khác
            if (i.expiredDateType !== expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate = nextDate.add(i.expiredDays, 'days');
              i.paymentDueDate = nextDate.toDate();
            } else if (i.expiredDateType === expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate = moment(i.exactDays)
              i.paymentDueDate = nextDate.toDate();
            }
          }
        } else {
          // lấy hạn thanh toán của chính sách cũ
          i.paymentDueDate = contract.policyPayment.schedule.installments[index].paymentDueDate;
        }
      }

      return i;
    });

    // Nếu HĐ đã duyệt, chuyển hết phiếu thu cũ sang các đợt tương ứng
    (contract.policyPayment?.schedule?.installments || []).forEach((element, index) => {
      if (element.receipts && element.receipts.length) {
        newPolicyPayment.schedule.installments[index].receipts = element.receipts;
        newPolicyPayment.schedule.installments[index].totalTransfered = element.totalTransfered;
      }
    });

    return newPolicyPayment;
  }

  private getTransferredReceiptList(contract) {
    // lấy các phiếu thu đã duyệt
    let transferredReceipt = [];
    contract.policyPayment.schedule.installments.map((i, index) => {
      i.receipts = i.receipts || [];
      i.receipts.map(r => {
        if (r.status === 'TRANSFERED') {
          const idx = transferredReceipt.findIndex(re => re.id === r.id);
          if (idx !== -1) {
            transferredReceipt[idx].amount += r.amount;
          } else {
            transferredReceipt.push(Object.assign({}, { ...r }));
          }
        }
      });
    });
    return transferredReceipt
  }

  async deleteContract(user: any, id: string, actionName: string) {
    this.loggerService.log(this.context, clc.green('delete service'));
    const oldDto: any = await this.queryRepository.findOne({ id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", id) });
    }
    if (oldDto.status !== StatusContractEnum.INIT) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "contractStatus") });
    }
    let model: any = { id };

    if (oldDto.primaryTransaction) {
      model.primaryTransactionId = oldDto.primaryTransaction.id;
    }

    this.commandId = uuid.v4();
    model.modifiedBy = user.id;
    return await this.executeCommand(Action.DELETE, actionName, this.commandId, model);
  }

  async deleteInterestCalculation(user: any, dto: UpdateInterestCalculationDto, actionName: string) {
    this.loggerService.log(this.context, clc.green('delete service'));
    const oldDto: any = await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "contract", "ID", dto.id) });
    }

    if (!dto.interestCalculation.id) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, "interest", "ID", dto.interestCalculation.id) });
    }

    let idx = oldDto.interestCalculations.findIndex(i => i.id === dto.interestCalculation.id);
    if (idx > -1) {
      if (oldDto.interestCalculations[idx].status == InterestCalculationStatusEnum.init) {
        oldDto.interestCalculations.splice(idx, 1);
      } else {
        throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, "interest calculation status") });
      }
    }

    this.commandId = uuid.v4();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async syncContractTransaction(transaction: any) {
    const contract: any = await this.queryRepository.findOne({ id: transaction.contractId });
    if (!contract || !contract.policyPayment || !contract.policyPayment.schedule) {
      return null;
    }
    let updatePropertyContract = false;
    let installments = contract.policyPayment.schedule.installments || [];
    let interestCalculations = contract.interestCalculations || [];
    if (installments.length > 0) {
      const existedInstallmentIndex = (contract.policyPayment.schedule.installments as Array<any>).findIndex(item => item.name === transaction.paymentBatch);
      const existedInstallmentToContractIndex = (contract.policyPayment.schedule.installments as Array<any>).findIndex(item => item.isToContract === true);
      // transform lại data
      transaction.amount = transaction.money;
      transaction.receiptNum = transaction.code;
      transaction.receiptDate = transaction.collectMoneyDate;
      const simpleTransaction: any = (({ id, amount, code, status, type, receiptNum, receiptDate }) => ({ id, amount, code, status, type, receiptNum, receiptDate }))(transaction);
      if (existedInstallmentIndex !== -1) {
        // Chọn Đợt
        // lưu phiếu thu vào Đợt
        // check tồn tại phiếu thu cùng id chưa
        const existedTransactionIndex = installments[existedInstallmentIndex].receipts.findIndex(item => item.id === transaction.id);
        if (existedTransactionIndex === -1) {
          // chưa tồn tại, thêm mới
          installments[existedInstallmentIndex].receipts.push(Object.assign({}, simpleTransaction));
        } else {

          // reject phiếu đã thu
          if (simpleTransaction.status === TransactionStatusEnum.processing &&
            installments[existedInstallmentIndex].receipts[existedTransactionIndex].status === TransactionStatusEnum.transfered) {
            installments[existedInstallmentIndex].totalTransfered -= simpleTransaction.amount;
          }
          // đã tồn tại, update trạng thái
          installments[existedInstallmentIndex].receipts[existedTransactionIndex].status = simpleTransaction.status;
        }
        // điều chỉnh số tiền đã thu, chỉ khi đã duyệt
        if (simpleTransaction.status === TransactionStatusEnum.transfered) {
          installments[existedInstallmentIndex].totalTransfered = installments[existedInstallmentIndex].totalTransfered || 0;
          installments[existedInstallmentIndex].totalTransfered += transaction.amount;
          if (contract.type === ContractEnum.DEPOSIT) { // HĐ cọc
            // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
            if (existedInstallmentIndex === 0 && installments[0].totalTransfered >= installments[0].totalAmount) {
              contract.status = contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          } else { // HDMB/Thuê
            // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
            if (existedInstallmentIndex === existedInstallmentToContractIndex && installments[existedInstallmentToContractIndex].totalTransfered >= installments[existedInstallmentToContractIndex].totalAmount) {
              contract.status = contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          }
          // kiểm tra có thanh toán lãi hay không.
          if (transaction.isInterest) {
            const ids = transaction.interestCalculations.map(i => i.id);
            contract.interestCalculations = interestCalculations.map(i => {
              if (ids.includes(i.id) && i.installmentName === transaction.paymentBatch) {
                i.status = InterestCalculationStatusEnum.transfered;
                i.receipts = simpleTransaction
              }
              return i;
            });
          }
          if (transaction.amount !== 0) {
            // gửi thông báo thanh toán thành công CarePlus
            const customer = await this.careClient.sendDataPromise({
              identityNumber: contract.primaryTransaction.customer.identities[0].value
            }, CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY);

            if (customer && customer.id) {
              this.notificationClient.createNotificationCare(
                "care_PrimaryContract_InstallmentPaymentSuccess",
                null,
                customer.id,
                "primary-contract",
                contract.id,
                {
                  installment: existedInstallmentIndex + 1,
                  productCode: contract.primaryTransaction.propertyUnit.code,
                  projectCode: contract.primaryTransaction.project.code,
                  amount: transaction.amount.toLocaleString().split(',').join('.'),
                  receiptCode: simpleTransaction.code,
                })
            }
          }
        }
      } else {
        // Đợt "Khác"
        let money = transaction.money;
        installments = installments.map((i, idx) => {
          i.totalTransfered = i.totalTransfered || 0;
          // Nếu đợt chưa thanh toán đủ tiền
          if (money > 0 && (i.totalAmount > i.totalTransfered)) {
            // số tiền cần thanh toán
            const needTransfer = i.totalAmount - i.totalTransfered;
            // số tiền có thể thanh toán
            const canTransfer = money >= needTransfer ? needTransfer : money;
            // điều chỉnh đúng số tiền và lưu phiếu thu vào Đợt
            simpleTransaction.amount = canTransfer;
            // check tồn tại phiếu thu cùng id chưa
            const existedTransactionIndex = i.receipts.findIndex(item => item.id === transaction.id);
            if (existedTransactionIndex === -1) {
              // chưa tồn tại, thêm mới
              i.receipts.push(Object.assign({}, simpleTransaction));
            } else {

              // reject phiếu đã thu
              if (simpleTransaction.status === TransactionStatusEnum.processing &&
                i.receipts[existedTransactionIndex].status === TransactionStatusEnum.transfered) {
                i.totalTransfered -= canTransfer;
              }
              // đã tồn tại, update trạng thái
              i.receipts[existedTransactionIndex].status = simpleTransaction.status;
            }
            // điều chỉnh số tiền đã thu, chỉ khi đã duyệt
            if (simpleTransaction.status === TransactionStatusEnum.transfered) {
              i.totalTransfered += canTransfer;
            }
            money -= canTransfer;
          }

          if (contract.type === ContractEnum.DEPOSIT) { // HĐ cọc
            // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
            if (idx === 0 && i.totalTransfered >= i.totalAmount) {
              contract.status = contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          } else { // HDMB/Thuê
            // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
            if (idx === existedInstallmentToContractIndex && i.totalTransfered >= i.totalAmount) {
              contract.status = contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract = true;
            }
          }
          return i;
        });
        if (simpleTransaction.status === TransactionStatusEnum.transfered && transaction.amount !== 0) {
          // gửi thông báo thanh toán thành công CarePlus
          const customer = await this.careClient.sendDataPromise({
            identityNumber: contract.primaryTransaction.customer.identities[0].value
          }, CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY);

          if (customer && customer.id) {
            this.notificationClient.createNotificationCare(
              "care_PrimaryContract_MultiPaymentSuccess",
              null,
              customer.id,
              "primary-contract",
              contract.id,
              {
                productCode: contract.primaryTransaction.propertyUnit.code,
                projectCode: contract.primaryTransaction.project.code,
                amount: transaction.amount.toLocaleString().split(',').join('.'),
                receiptCode: simpleTransaction.code,
              })
          }
        }
      }
      // reverse lại đúng thứ tự và save
      contract.policyPayment.schedule.installments = installments;
      if (contract.deposit) {
        // nếu là HĐMB, update Lịch sử thanh toán cho HĐC
        const depositContract = await this.queryRepository.findOne({ id: contract.deposit.id });
        // chỉ update khi cùng Chính sách thanh toán
        if (depositContract && depositContract.policyPayment && depositContract.policyPayment.id === contract.policyPayment.id) {
          depositContract.policyPayment = contract.policyPayment;
        }
        if (contract.status === StatusContractEnum.APPROVED) {
          depositContract.purchase = {
            id: contract.id,
            code: contract.code,
            name: contract.name,
          };
          depositContract.isDebtRemind = false;
        }
        this.executeCommand(Action.UPDATE, null, this.commandId, depositContract);
      }

      contract.paymentPercent = this.calPaymentPercentage({
        installments: contract.policyPayment.schedule.installments
      }, contract);

    }

    await this.executeCommand(Action.UPDATE, null, this.commandId, contract);
    if (updatePropertyContract) {
      // update thông tin contract vào sản phẩm
      const propertyUnitId = contract.primaryTransaction.propertyUnit.id;
      this.propertyClient.sendDataPromise({
        id: propertyUnitId,
        contract: {
          id: contract.id,
          code: contract.code,
          type: contract.type,
          isTransferred: contract.isTransferred
        }
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CONTRACT_IN_PROPERTY_UNIT)
    }
    return contract;
  }

  calPaymentPercentage(params: { installments: any[] }, contract?) {
    const { installments = [] } = params;

    // cal total amount
    const price = installments.reduce(function (prev, cur) {
      return prev + cur.totalAmount;
    }, 0);

    let totalPercentage = 0;
    let currentTotalTransfered = 0;

    let resetedTotalPercent = false;
    for (let index = 0; index < installments.length; index++) {
      const inst = installments[index];
      currentTotalTransfered = currentTotalTransfered + Number(inst.totalTransfered);

      if (inst.totalTransfered < inst.totalAmount || inst.type == 'currency' || inst.totalAmount === 0) {
        if (inst.type === 'percent' && index > 0) {
          const oldInstallments = installments.slice(0, index + 1);

          let remainAmount = oldInstallments.reduce(function (prev, cur) {
            return prev + cur.totalTransfered - + cur.totalAmount;
          }, 0);
          remainAmount = remainAmount > 0 ? remainAmount : 0;
          resetedTotalPercent = true;
          if ((inst.totalTransfered + remainAmount) < inst.totalAmount) {
            totalPercentage = price !== 0 ? totalPercentage + Math.round((Number(inst.totalTransfered + remainAmount) * 100) / price) : 0;
          } else {
            totalPercentage = totalPercentage + Number(inst.value);
          }
        } else {
          totalPercentage = price !== 0 ? totalPercentage + Math.round((Number(inst.totalTransfered) * 100) / price) : 0;
        }
      } else {
        if (resetedTotalPercent) {
          totalPercentage = totalPercentage + Number(inst.value);
        } else {
          resetedTotalPercent = true;
          totalPercentage = totalPercentage + Number(inst.value);
        }
      }
    }

    if (installments.length === 1) {
      totalPercentage = price !== 0 ? totalPercentage + Math.round((currentTotalTransfered * 100) / price) : 0;
    }

    // Đồng bộ % thanh toán hợp đồng => sản phẩm
    this.syncTotalPaymentToProperty(totalPercentage, contract);

    return totalPercentage;
  }

  // Đồng bộ % thanh toán hợp đồng  => sản phẩm
  async syncTotalPaymentToProperty(totalPercentage, contract) {

    let updatePropery = {
      query: { id: contract.primaryTransaction.propertyUnit.id },
      model: { $set: { totalPercentageContract: totalPercentage } }
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
  }

  async sendDeliveryNotify(user, dto: SendPrimaryContractDeliveryNotifyDto, actionName) {
    this.loggerService.log(this.context, 'send Delivery Notify');

    // lấy thiết lập bàn giao
    const handOverSetting = await this.handoverQueryService.findOneByQuery({ id: dto.id, status: StatusEnum.ACTIVE });

    if (!handOverSetting) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Thiết lập') });
    }
    const project = await this.propertyClient.sendDataPromise({ id: handOverSetting.project.id }, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);

    if (!project) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Dự án') });
    }

    let contracts = dto.contracts;
    const ids = contracts.map(contract => contract.id);
    const notifyData = [];
    const mailerData = [];

    // Người dùng chỉ có thể chọn các căn có trạng thái "Đã lên lịch" cho bàn giao sản phẩm 
    //trạng thái đã lên lịch,Sản phẩm đủ điều kiện làm sổ, Sản phẩm đang làm sổ, Sản phẩm đã có sổ đợi bàn giao cho bàn giao sổ
    const listContractSchedule = await this.queryRepository.find({
      id: { $in: ids },
      $or: [
        { handoverStatus: HandoverStatusEnum.scheduled },
        {
          cerHandoverStatus: {
            $in: [
              HandoverStatusEnum.Eligible,
              HandoverStatusEnum.Cer_handed_over,
              HandoverStatusEnum.Cer_ready_handover
            ]
          }
        }
      ]
    });

    const isValidCerStatus = (status: string) =>
      status === HandoverStatusEnum.Cer_handed_over ||
      status === HandoverStatusEnum.Eligible ||
      status === HandoverStatusEnum.Cer_ready_handover;
    let ownershipCertificate: any;
    if (listContractSchedule.some(c => isValidCerStatus(c.cerHandoverStatus))) {
      ownershipCertificate = await this.ownershipCertificateQueryRepository.findOne({ "project.id": handOverSetting.project.id, isActive: StatusEnum.ACTIVE, softDelete: false })
      if (!ownershipCertificate) {
        throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, 'Thiết lập') });
      }
    }

    const listContractScheduleIds = listContractSchedule.map(x => x.id);
    contracts = contracts.filter(x => listContractScheduleIds.includes(x.id));
    let handoverList = [];
    let cerList = [];
    let notifications = [];
    for (let contract of contracts) {
      contract = listContractSchedule.find(x => x.id === contract.id);
      const handoverSchedule = await this.handoverScheduleQueryService.findOneByQuery({
        "handoverApartment.id": contract.id,
        softDelete: false,
      });

      const data = {
        customer: {
          name: contract.primaryTransaction?.customer?.personalInfo?.name
        },
        property: {
          code: contract.primaryTransaction?.propertyUnit?.code
        },
        project: {
          name: contract.project?.name
        },
        hotline: ownershipCertificate?.hotline
      };

      if (contract.handoverStatus === HandoverStatusEnum.scheduled) {
        handoverList.push(contract.id);
        this.handleContractNotification({
          contract,
          handoverSchedule,
          setting: handOverSetting,
          data,
          notifications,
          notifyData,
          hotline: handOverSetting?.hotline || ''
        });
      } else if (contract.cerHandoverStatus === HandoverStatusEnum.Eligible) {
        this.handleContractNotification({
          contract,
          handoverSchedule,
          setting: ownershipCertificate?.emailForEligible,
          data,
          notifications,
          notifyData,
          hotline: ownershipCertificate?.hotline || ''
        });
      } else if (contract.cerHandoverStatus === HandoverStatusEnum.Cer_ready_handover) {
        this.handleContractNotification({
          contract,
          handoverSchedule,
          setting: ownershipCertificate?.emailForCerReadyHandover,
          data,
          notifications,
          notifyData,
          hotline: ownershipCertificate?.hotline || ''
        });
      } else if (!contract.isCerSendDelivery &&
        contract.cerHandoverStatus === HandoverStatusEnum.Cer_handed_over) {
        cerList.push(contract.id);
        this.handleContractNotification({
          contract,
          handoverSchedule,
          setting: ownershipCertificate?.emailCerHandedOver,
          data,
          notifications,
          notifyData,
          hotline: ownershipCertificate?.hotline || ''
        });
      }
    }


    // gửi SMS
    console.log('SEND_SMS_DELIVERY', notifyData)
    this.notificationClient.sendDeliverySMS({ notifyData }, user);

    //gửi email
    for (let noti of notifications) {
      await this.notiService.push(noti);
    }

    if (handoverList && handoverList.length >= 1) {
      await this.updateMany({ id: { $in: handoverList } }, {
        $set: {
          isSendDelivery: true,
          modifiedDate: new Date(),
          modifiedBy: user.id
        }
      });
    }

    if (cerList && cerList.length >= 1) {
      await this.updateMany({ id: { $in: cerList } }, {
        $set: {
          isCerSendDelivery: true,
          modifiedDate: new Date(),
          modifiedBy: user.id
        }
      });
    }
    return this.getResponse("0");
  }
  private handleContractNotification({
    contract,
    handoverSchedule,
    setting,
    data,
    notifications,
    notifyData,
    hotline
  }: {
    contract: any;
    handoverSchedule: any;
    setting: any;
    data: any;
    notifications: notificationPayloadDto[];
    notifyData: any[];
    hotline: string;
  }) {
    const time = contract.deliveryDate ? moment(contract.deliveryDate).format('HH:mm') : '';
    const date = contract.deliveryDate ? moment(contract.deliveryDate).format('DD/MM/YYYY') : '';
    const { code, identityNumber, customerName } = contract;

    const smsTemplate = setting?.smsTemplate || '';
    const smsBrandName = setting?.smsBrandName || '';
    const emailTitle = setting?.emailTitle || '';
    const emailTemplateRaw = setting?.emailTemplate || '';
    const emailTemplate = this.renderTemplate(emailTemplateRaw.replace(/(?:\r\n|\r|\n)/g, '<br>'), data);

    const emailCC: string[] = (setting?.emailCC || '')
      .split(';')
      .map(e => e.trim())
      .filter(e => e !== '');
    const emailFrom: string[] = (setting?.emailFrom || '')
      .split(';')
      .map(e => e.trim())
      .filter(e => e !== '');
    let emailBCC: string[] = (setting?.emailBCC || '')
      .split(';')
      .map(e => e.trim())
      .filter(e => e !== '');

    if (handoverSchedule?.supportEmployee?.email) {
      emailBCC.push(handoverSchedule.supportEmployee.email);
    }

    // Gửi notify app
    this.notificationClient.createNotificationCare(
      "care_deliveryReminderSend",
      null,
      identityNumber,
      "primary-contract",
      contract.id,
      { code, time, date, hotline, identityNumber }
    );

    // Gửi notify SMS
    notifyData.push({
      phone: contract.phone,
      contentSMS: smsTemplate,
      brandName: smsBrandName,
      code, time, date, hotline, customerName
    });

    // Gửi notify Email

    const payload = Object.assign(new notificationPayloadDto(), {
      subject: emailTitle,
      content: emailTemplate,
      receives: emailFrom,
      ccReceives: emailCC,
      bccReceives: emailBCC,
    });

    notifications.push(payload);
  }

  renderTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/{{(.*?)}}/g, (_, key) => {
      const path = key.trim().split('.');
      let value = data;

      for (const segment of path) {
        if (value === null || value === undefined) return '';
        value = value[segment];
      }

      return (value !== null && value !== undefined) ? String(value) : '';
    });
  }
  async updateMany(query, updateQuery) {
    return await this.queryRepository.updateMany(query, updateQuery);
  }

  async updateDepositConfirm(data) {
    this.loggerService.log(this.context, clc.green('updateDepositConfirm'));
    const oldDto: any = await this.queryRepository.findOne({ id: data.id });
    if (!oldDto) {
      return;
    }
    oldDto.depositConfirmFromCustomer = data.status;

    this.commandId = uuid.v4();
    return await this.executeCommand(Action.UPDATE, null, this.commandId, oldDto);
  }

  async updateTransferConfirm(data) {
    this.loggerService.log(this.context, clc.green('updateTransferConfirm'));
    const oldDto: any = await this.queryRepository.findOne({ id: data.id });
    if (!oldDto) {
      return;
    }
    oldDto.transferConfirmFromCustomer = data.status;

    this.commandId = uuid.v4();
    return await this.executeCommand(Action.UPDATE, null, this.commandId, oldDto);
  }

  async updateShowReceipt(dto: UpdateShowReceiptDto) {
    this.loggerService.log(this.context, clc.green('updateShowReceipt'));
    let oldData = await this.queryRepository.findOne({
      id: dto.id,
      "policyPayment.schedule.installments.receipts.id": dto.receiptId
    });

    let installments: any[] = oldData?.policyPayment?.schedule?.installments || [];
    if (installments.length) {
      for (let i of installments) {
        let receipts: any[] = i.receipts || [];
        const index = receipts.findIndex((r) => r.id === dto.receiptId);
        if (index > -1) {
          receipts[index].isShowedReceipt = dto.isShowedReceipt;
          i.receipts = receipts;
        }
      }
      oldData.policyPayment.schedule.installments = installments;
    }

    this.commandId = uuid.v4();
    return this.executeCommand(Action.UPDATE, null, this.commandId, oldData);
  }

  async importFiles(user, dto, files, actionName: string) {
    this.loggerService.log(this.context, clc.green('create service'));
    let contracts = await CommonUtils.convertToJson(files);
    let unitsReject: any[] = [];
    let unitsImport: any[] = [];

    const history = [];
    for (const contract of contracts) {

      let unit = contract;
      let amount, price = 0, landPrice = 0, housePrice = 0;
      let receipt = {};
      let projectSetting = {};
      const model: any = Object.assign({},
        contract
        , {
          isDebtRemind: contract['isDebtRemind'] === 'x',
          calcCurrencyFirst: contract['calcCurrencyFirst'] === 'x',
          type: ContractEnum.DEPOSIT
        });

      if (contract['startDate']) {
        if (!moment(contract.startDate, 'DD/MM/YYYY', true).isValid()) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai định dạng "DD/MM/YYYY": Ngày bắt đầu`
          });
        } else {
          model.startDate = moment(contract['startDate'], 'DD/MM/YYYY').toDate();
        }
      }
      if (contract['expiredDate']) {
        if (!moment(contract.expiredDate, 'DD/MM/YYYY', true).isValid()) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai định dạng "DD/MM/YYYY": Ngày kết thúc`
          });
        } else {
          model.expiredDate = moment(contract['expiredDate'], 'DD/MM/YYYY').toDate();
        }
      }
      if (contract['maintenanceFee'] && contract['maintenanceFee']['type']) {
        model.maintenanceFee.type = contract['maintenanceFee']['type'] === 'VND' ? 'currency' : 'percent';
      }
      if (contract['maintenanceFee'] && contract['maintenanceFee']['value']) {
        model.maintenanceFee.value = parseInt(contract['maintenanceFee']['value']);
      }

      this.commandId = uuid.v4();

      model.modifiedBy = user.id;
      model.createdBy = user.id;
      model.createdDate = new Date();

      model.calcPriceVat = false;
      model.calcContractPrice = false;

      if (contract['calcPrice'] === 'Giá có VAT') {
        model.calcPriceVat = true
      } else if (contract['calcPrice'] === 'Giá không có VAT') {

      } else if (contract['calcPrice'] === 'Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT') {
        model.calcContractPrice = true;
      }

      if (!contract.signedDate) {
        unit = null;
        history.push({
          line: `${contract.stt}`,
          error: `Thiếu trường bắt buộc: Ngày ký kết`
        });
      } else {
        if (!moment(contract.signedDate, 'DD/MM/YYYY', true).isValid()) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai định dạng "DD/MM/YYYY": Ngày ký kết`
          });
        } else {
          model.signedDate = moment(contract['signedDate'], 'DD/MM/YYYY').toISOString();
        }
      }

      if (contract.transferType) {
        model.transferType = this.getTransferType(contract.transferType);
        if (!model.transferType) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Sai kiểu dữ liệu: Hình thức thanh toán`
          });
        }
      }

      let primaryTransaction;
      if (!contract.primaryTransactionCode) {
        unit = null;
        history.push({
          line: `${contract.stt}`,
          error: `Thiếu trường bắt buộc: Mã phiếu YCDCO`
        });
      } else {
        primaryTransaction = await this.propertyClient.sendDataPromise({
          code: contract.primaryTransactionCode
        }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_CODE);
        if (!primaryTransaction) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Không tìm thấy phiếu YCDCO`
          });
        } else if (primaryTransaction.status !== 'SUCCESS') {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Phiếu YCDCO trạng thái không hợp lệ`
          });
        } else if (primaryTransaction.contract) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Phiếu YCDCO đã có hợp đồng`
          });
        }
        if (!unit) {
          unitsReject.push(contract);
          continue;
        }
        if (contract.hasCustomer2 && contract.customer2.name) {
          unit = await this.checkErrorCustomer(history, contract, contract.customer2, unit);
          let address = {
            country: contract.customer2['country'],
            province: contract.customer2['province'],
            district: contract.customer2['district'],
            ward: contract.customer2['ward'],
            address: contract.customer2['address'],
            fullAddress: ''
          };
          let rootAddress = {
            country: contract.customer2['rootCountry'],
            province: contract.customer2['rootProvince'],
            district: contract.customer2['rootDistrict'],
            ward: contract.customer2['rootWard'],
            address: contract.customer2['rootAddress'],
            fullAddress: ''
          };

          let customer2: any = {
            name: contract.customer2['name'],
            gender: contract.customer2['gender'] === 'Nam' ? 'male' : 'female',
            birthday: contract.customer2['onlyYear'] === 'x' ? contract.customer2['dob'] : moment(contract.customer2['dob'], 'DD/MM/YYYY'),
            birthdayYear: contract.customer2['dob'],
            onlyYear: contract.customer2['onlyYear'] === 'x',
            phone: contract.customer2['phone'],
            email: contract.customer2['email'],
            identityNumber: contract.customer2['identityNumber'],
            identityIssueDate: contract.customer2['identityDate'],
            identityIssueLocation: contract.customer2['identityLocation'],
            taxCode: contract.customer2['taxCode'] ? contract.customer2['taxCode'] : '',
            bankInfo: {
              code: contract['bankCode'],
              value: contract['bankValue'],
            },
            address: address,
            rootAddress: rootAddress,
            code: '',
            company: '',
            position: '',
            type: ''
          };
          primaryTransaction.customer2 = CommonUtils.getCustomerMapping(customer2);
        }
        model.primaryTransaction = primaryTransaction;

        if (model.calcContractPrice) {
          price = primaryTransaction.propertyUnit.contractPrice;
          housePrice = 0;
          landPrice = 0;
        } else {
          price = model.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
          housePrice = model.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
          landPrice = model.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
        }
        model.maintenanceFee = {
          ...model.maintenanceFee,
          contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
        }

        receipt = primaryTransaction.reciept;
        amount = primaryTransaction.amount;
        projectSetting = primaryTransaction.project.setting
      }

      if (contract.policyDiscountCodes) {
        const codes = contract.policyDiscountCodes.split('|');
        const policyDiscounts: any[] = await this.policyQueryService.findByCodes(codes);
        if (!policyDiscounts || !policyDiscounts.length) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Không tìm thấy chính sách chiết khấu`
          });
        } else {
          model.policyDiscounts = policyDiscounts;

          // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
          if (price > 0 && !housePrice && !landPrice) {
            if (
              policyDiscounts.some(
                e => e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
                  e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
            ) {
              unit = null;
              history.push({
                line: `${contract.stt}`,
                error: `Chính sách chiết khẩu không phù hợp`
              });
            }
          } else {
            if (
              policyDiscounts.some(e => !e.discount.typeRealEstate || e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
            ) {
              unit = null;
              history.push({
                line: `${contract.stt}`,
                error: `Chính sách chiết khẩu không phù hợp`
              });
            }
          }
        }
      } else {
        model.policyDiscounts = [];
      }

      if (contract.policyPaymentCode) {
        const policyPayment = await this.policyQueryService.findOne({
          code: contract.policyPaymentCode, type: PolicyTypeEnum.PAYMENT
        });
        if (!policyPayment) {
          unit = null;
          history.push({
            line: `${contract.stt}`,
            error: `Không tìm thấy chính sách thanh toán`
          });
        } else {
          model.policyPayment = this.transformPolicyPayment(
            model,
            price,
            projectSetting,
            model.signedDate,
            receipt,
            policyPayment,
            housePrice,
            landPrice,
            model.maintenanceFee);
        }
      } else {
        unit = null;
        history.push({
          line: `${contract.stt}`,
          error: `Thiếu trường bắt buộc: Mã chính sách thanh toán`
        });
      }

      if (!unit) {
        unitsReject.push(contract);
        continue;
      }

      if (!model.code) {
        const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-`;
        model.code = await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
      }
      model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`

      if (model.policyPayment && !model.paymentPercent) {

        let totalPercentage = 0;
        let currentTotalTransfered = 0;

        model.policyPayment.schedule.installments.forEach((inst, index) => {
          currentTotalTransfered = currentTotalTransfered + inst.totalTransfered;
          if (index > 0) { // bắt đầu tính từ đợt 1;
            if (inst.totalTransfered < inst.totalAmount || inst.type === 'currency') {
              totalPercentage = totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
            } else {
              totalPercentage = totalPercentage + inst.value;
            }
          }
        });

        if (model.policyPayment.schedule.installments.length === 1) {
          totalPercentage = totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
        }

        model.paymentPercent = totalPercentage;
      }

      if (contract.companyInformation && contract.companyInformation.haveValue) {
        model.companyInformation = Object.assign({}, contract.companyInformation, {
          haveValue: true,
          dateOfIssue: contract.companyInformation.dateOfIssue ? moment(contract.companyInformation.dateOfIssue, 'DD/MM/YYYY').toDate() : ''
        })
      }

      if (contract.receipt && contract.receipt.length) {
        contract.receipt = contract.receipt.filter(r => !!r.amount);
        const receipts = [];
        contract.receipt.forEach(function (receipt, idx) {
          const installmentName = model.policyPayment.schedule.installments[idx].name;
          const amounts = receipt.amount.split('|') || [];
          const dates = receipt.date.split('|') || [];
          const receiptNums = receipt.receiptNum.split('|') || [];
          if (amounts.length) {
            amounts.forEach(function (amount, aIdx) {
              receipts.push({
                amount: amount,
                date: dates[aIdx],
                receiptNum: receiptNums[aIdx],
                installmentName
              })
            });
          }
        });
        model.receipts = receipts;
        model.user = user;
      }

      unitsImport.push(contract);

      model.status = StatusContractEnum.ACCOUNTANT_WAITING;

      //  create resident
      let projectInfo: any = {};
      projectInfo = await this.propertyClient.sendDataPromise(model.primaryTransaction.project, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);
      let newObject = {};
      if (projectInfo) {
        newObject = {
          ...model.primaryTransaction, project: projectInfo,
          customer: model.primaryTransaction.customer,
          propertyUnit: model.primaryTransaction.propertyUnit,
          contractId: this.commandId
        };
      }

      // send transaction to care service
      await this.careClient.sendDataPromise({
        primaryTransaction: newObject
      }, CmdPatternConst.CARE.GET_TRANSACTION);

      await this.socialClient.sendDataPromise({
        projectId: model.primaryTransaction.project.id
      }, CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP);

      const { personalInfo, info } = (model['primaryTransaction'].customer || {}) || {};
      const { email, phone, name, identities } = personalInfo;

      // if(email && phone ) {
      //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)=> {

      //     if(!customer) {
      //       const payload = { personalInfo: { name, email, phone, identities }, accessSystem: user.notiSystem ? [user.notiSystem] : null  };
      //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

      //     }else if(!customer.isActive) {

      //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
      //     }

      //   })
      // }

      model.id = this.commandId;
      await this.queryRepository.create(model);
      await this.updateContractInTracsaction(model);

      if (model.receipts && model.receipts.length) {
        const transactionData = {
          action: 'property-ticketCreated',
          receipts: model.receipts,
          contractId: model.id,
          user: model.user
        }
        await this.transactionClient.sendDataPromise(transactionData, CmdPatternConst.LISTENER.PRIMARY_CONTRACT_CREATE_TRANSFERRED_TICKET_LISTENER);
      }

      console.log('model', model)
    }

    // Tạo history.
    const file = await this.uploadClient.sendData(files[0].originalname, files[0].buffer);
    const historyModel = {
      fileName: files[0].originalname,
      createdBy: user.id,
      fail: unitsReject.length,
      success: unitsImport.length,
      description: history,
      type: "IMPORT_CONTRACT_DEPOSIT",
      eventName: 'importHDC',
      file
    };
    await this.historyRepository.create(historyModel);
  }

  updateContractInTracsaction(event) {
    try {
      let data;
      data = {
        primaryTransactionId: event.primaryTransaction.id,
        id: event.id,
        code: event.code,
        type: event.type,
        isTransferred: event.isTransferred
      };

      if (event.oldPrimaryTransaction) data.oldPrimaryTransactionId = event.oldPrimaryTransaction;

      // khi HĐC đã sang HĐMB thì không update sang primary transaction nữa
      if (!event.purchase) {
        this.propertyClient.sendData(data, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CONTRACT_IN_TRANSACTION_BY_ID);
      }
    } catch (error) {
      console.log('[Error] Update primary transaction failed, detail: ', error);
    }
  }

  getTransferType(type) {
    switch (type) {
      case 'Tiền mặt':
        return 'CASH';
      case 'Chuyển khoản':
        return 'TRANSFER';
      case 'Khác':
        return 'OTHER';
      default:
        return '';
    }
  }

  checkErrorCustomer(history, ticket, customer, unit) {
    let customer2Name = '(đồng sở hữu)';

    // Check đồng sở hữu
    if (ticket.hasCustomer2 === 'x' && !ticket.customer2) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu thông tin đồng sở hữu`
      });
      return unit;
    }

    // Check error
    if (!customer.name) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Tên khách hàng` + customer2Name
      });
    }
    // if (!customer.dob) {
    //   unit = null;
    //   history.push({
    //     line: `${ticket.stt}`,
    //     error: `Thiếu trường bắt buộc: Ngày sinh` + customer2Name
    //   });
    // }
    if (!customer.gender) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Giới tính` + customer2Name
      });
    }
    // if (!customer.phone) {
    //   unit = null;
    //   history.push({
    //     line: `${ticket.stt}`,
    //     error: `Thiếu trường bắt buộc: Số điện thoại` + customer2Name
    //   });
    // }
    if (customer.phone && !customer.phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Số điện thoại không hợp lệ` + customer2Name
      });
    }
    // if (!customer.email) {
    //   unit = null;
    //   history.push({
    //     line: `${ticket.stt}`,
    //     error: `Thiếu trường bắt buộc: Địa chỉ Email` + customer2Name
    //   });
    // }
    if (customer.email && !customer.email.toString().match(CommonConst.REGEX_EMAIL)) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Email: Sai định dạng email` + customer2Name
      });
    }
    if (!customer.identityNumber) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Số CMND/Hộ chiếu` + customer2Name
      });
    }
    if (!customer.identityDate) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Ngày cấp` + customer2Name
      });
    }
    if (!customer.identityLocation) {
      unit = null;
      history.push({
        line: `${ticket.stt}`,
        error: `Thiếu trường bắt buộc: Nơi cấp` + customer2Name
      });
    }
    return unit;
  }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.UPDATE:
        commandObject = new UpdatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.DELETE:
        commandObject = new DeletePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  checkErrorCustomer2SyncErp(history, customer2, unit, isCustomer2 = false) {
    let customer2Name = isCustomer2 ? '(đồng sở hữu)' : '';

    // Check error
    if (!customer2.name) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Tên khách hàng` + customer2Name
      });
    }
    if (!customer2.birthday && !isCustomer2) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Ngày sinh` + customer2Name
      });
    }
    if (!customer2.gender && !isCustomer2) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Giới tính` + customer2Name
      });
    }
    if (!customer2.phone && !isCustomer2) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Số điện thoại` + customer2Name
      });
    }
    if (customer2.phone && !customer2.phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      unit = null;
      history.push({
        error: `Số điện thoại không hợp lệ` + customer2Name
      });
    }
    // if (!customer2.email) {
    //   unit = null;
    //   history.push({
    //     error: `Thiếu trường bắt buộc: Địa chỉ Email` + customer2Name
    //   });
    // }
    if (customer2.email && !customer2.email.toString().match(CommonConst.REGEX_EMAIL)) {
      unit = null;
      history.push({
        error: `Email: Sai định dạng email` + customer2Name
      });
    }
    if (!customer2.identityNumber) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Số CMND/Hộ chiếu` + customer2Name
      });
    }
    if (!customer2.identityIssueDate) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Ngày cấp` + customer2Name
      });
    }
    if (!customer2.identityIssueLocation) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Nơi cấp` + customer2Name
      });
    }
    return unit;
  }

  async createdContractSyncErp(contract) {
    this.loggerService.log(this.context, clc.green('created contract sync ERP'));
    let historyModel: any = {};
    const history = [];
    let unit = contract;
    let amount, price = 0, landPrice = 0, housePrice = 0;
    let receipt = [];
    let projectSetting = {};
    let updatePrimaryTransactionQuery;

    let oldContract;
    // check trùng
    if (contract.contractid) {
      oldContract = await this.queryRepository.findOne({ 'syncErpData.contractid': contract.contractid });
      if (oldContract) {
        for (const installment of oldContract.policyPayment.schedule.installments) {
          if (installment.receipts && installment.receipts.length) {
            receipt = [...receipt, ...installment.receipts];
          }
        }
      }
    }

    let paymentConfig, discountConfig;
    if (!contract.project) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: mã dự án ERP`
      });

      // Return báo lỗi
      historyModel = {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }

    let configErp = await this.syncErpClient.sendDataPromise(contract.project, CmdPatternConst.LISTENER.SEARCH_CONFIG_ERP_BY_CAMPAIGN);
    if (!configErp) {
      unit = null;
      history.push({
        error: `Không tìm thấy cấu hình ERP`
      });
    } else {
      paymentConfig = configErp.paymentMethod && configErp.paymentMethod.length > 0 ? configErp.paymentMethod.find(e => e.paymentMethodCRM === contract.pymtterm) : null;
      if (!paymentConfig) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách thanh toán`
        });
      }
      discountConfig = configErp.discountTable && configErp.discountTable.length > 0 ? configErp.discountTable.find(e => e.discountTableCRM === contract.policyDiscountCodes) : null;
      if (contract.policyDiscountCodes && !discountConfig) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách chiết khấu`
        });
      }
    }

    // Return báo lỗi
    if (!unit) {
      historyModel = {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }

    const model: any = Object.assign({},
      contract
      , {
        isDebtRemind: false,
        calcCurrencyFirst: false,
        type: ContractEnum.DEPOSIT
      });

    if (!contract.startdate) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Ngày ký kết`
      });
    } else {
      model.startDate = new Date(momentTz(contract.startdate, ('DD/MM/YYYY HH:mm:ss')).tz('Asia/Ho_Chi_Minh'));
      model.signedDate = new Date(momentTz(contract.startdate, ('DD/MM/YYYY HH:mm:ss')).tz('Asia/Ho_Chi_Minh'));
    }
    if (contract.enddate) {
      model.expiredDate = new Date(momentTz(contract.enddate, ('DD/MM/YYYY HH:mm:ss')).tz('Asia/Ho_Chi_Minh'));
    }

    this.commandId = uuid.v4();

    // model.modifiedBy = user.id;
    // model.createdBy = user.id;
    if (!oldContract) {
      model.createdDate = new Date();
    }

    model.calcPriceVat = false;
    model.calcContractPrice = true;
    model.transferType = null;

    let primaryTransaction;
    // Tìm YCDCO theo systemno
    if (!contract.anal_pct5) {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Mã phiếu YCDCO`
      });
    } else {
      primaryTransaction = await this.propertyClient.sendDataPromise({
        'syncErpData.systemno': contract.anal_pct5
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_QUERY);
      if (!primaryTransaction) {
        unit = null;
        history.push({
          error: `Không tìm thấy phiếu YCDCO`
        });
      } else if (primaryTransaction.status !== 'SUCCESS') {
        unit = null;
        history.push({
          error: `Phiếu YCDCO trạng thái không hợp lệ`
        });
      } else if (primaryTransaction.contract && (!oldContract || primaryTransaction.contract.id !== oldContract.id)) {
        unit = null;
        history.push({
          error: `Phiếu YCDCO đã có hợp đồng`
        });
      }

      // Return báo lỗi
      if (!unit) {
        historyModel = {
          isSuccess: false,
          description: history,
        };
        return historyModel;
      }

      // update primary transaction
      updatePrimaryTransactionQuery = {
        id: primaryTransaction.id
      };

      // Khách hàng
      let customerAddress = contract.address || '';
      let customerRootAddress = contract.contactaddress || '';
      let address = {
        country: 'Việt Nam',
        province: customerAddress.split(",")[3],
        district: customerAddress.split(",")[2],
        ward: customerAddress.split(",")[1],
        address: customerAddress.split(",")[0],
        fullAddress: customerAddress
      };
      let rootAddress = {
        country: 'Việt Nam',
        province: customerRootAddress.split(",")[3],
        district: customerRootAddress.split(",")[2],
        ward: customerRootAddress.split(",")[1],
        address: customerRootAddress.split(",")[0],
        fullAddress: customerRootAddress
      };
      let birthArr = contract.birthdate.split("/");
      birthArr = birthArr.filter(e => e != '0');
      let customer: any = {
        name: contract.clientname,
        gender: (contract.gender) === 'M' ? 'male' : 'female',
        birthday: moment(contract.birthdate, 'DD/MM/YYYY'),
        birthdayYear: birthArr[birthArr.length - 1],
        onlyYear: birthArr.length < 3,
        phone: contract.telephone,
        email: contract.email,
        identityNumber: contract.idcard,
        identityIssueDate: moment(contract.issueddate, 'DD/MM/YYYY'),
        identityIssueLocation: contract.issuedplace,
        address: address,
        rootAddress: rootAddress,
        company: '',
        position: '',
        type: '',
        taxCode: contract.taxcode
      };
      unit = await this.checkErrorCustomer2SyncErp(history, customer, unit);
      primaryTransaction.customer = CommonUtils.getCustomerMapping(customer);
      updatePrimaryTransactionQuery.customer = primaryTransaction.customer;

      const members = (contract.membername || '').split(' | ');
      const hasCustomer2 = members.length > 1;

      // Đồng sở hữu
      if (hasCustomer2) {
        let customer2Address = contract.maddress.split(' | ').pop() || '';
        let customer2RootAddress = contract.mcontactaddress.split(' | ').pop() || '';
        let address = {
          country: 'Việt Nam',
          province: customer2Address.split(",")[3],
          district: customer2Address.split(",")[2],
          ward: customer2Address.split(",")[1],
          address: customer2Address.split(",")[0],
          fullAddress: customer2Address
        };
        let rootAddress = {
          country: 'Việt Nam',
          province: customer2RootAddress.split(",")[3],
          district: customer2RootAddress.split(",")[2],
          ward: customer2RootAddress.split(",")[1],
          address: customer2RootAddress.split(",")[0],
          fullAddress: customer2RootAddress
        };
        let birthArr = [contract.mbirthday.split(' | ').pop(), contract.mbirthmonth.split(' | ').pop(), contract.mbirthyear.split(' | ').pop()]
        birthArr = birthArr.filter(e => e !== '0');
        const onlyYear = birthArr.length < 3;
        let customer2: any = {
          name: contract.membername.split(' | ').pop(),
          gender: (contract.mgender.split(' | ').pop()) === 'M' ? 'male' : 'female',
          birthday: onlyYear ? birthArr[birthArr.length - 1] : moment(`${contract.mbirthday.split(' | ').pop()}/${contract.mbirthmonth.split(' | ').pop()}/${contract.mbirthyear.split(' | ').pop()}`, 'DD/MM/YYYY'),
          birthdayYear: birthArr[birthArr.length - 1],
          onlyYear: onlyYear,
          phone: contract.mtelephone.split(' | ').pop(),
          email: contract.memail.split(' | ').pop(),
          identityNumber: contract.midcard.split(' | ').pop(),
          identityIssueDate: moment(contract.missueddate.split(' | ').pop(), 'DD/MM/YYYY'),
          identityIssueLocation: contract.missuedplace.split(' | ').pop(),
          address: address,
          rootAddress: rootAddress,
          company: '',
          position: '',
          type: ''
        };
        unit = await this.checkErrorCustomer2SyncErp(history, customer2, unit, true);
        primaryTransaction.customer2 = CommonUtils.getCustomerMapping(customer2);
        updatePrimaryTransactionQuery.customer2 = primaryTransaction.customer2;
      }

      // Return báo lỗi
      if (!unit) {
        historyModel = {
          isSuccess: false,
          description: history,
        };
        return historyModel;
      }

      model.primaryTransaction = primaryTransaction;

      if (model.calcContractPrice) {
        price = parseInt(contract.contractvalue);
        housePrice = 0;
        landPrice = 0;
      }
      model.maintenanceFee = {
        ...model.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      receipt = [...receipt, ...primaryTransaction.reciept];
      projectSetting = primaryTransaction.project.setting
    }

    // Chính sách chiết khấu
    if (discountConfig && discountConfig.discountTableDXG) {
      const policyDiscounts: any[] = await this.policyQueryService.findByCodes([discountConfig.discountTableDXG]);
      if (!policyDiscounts || !policyDiscounts.length) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách chiết khấu`
        });
      } else {
        model.policyDiscounts = policyDiscounts;

        // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà & đất
        if (price > 0 && !housePrice && !landPrice) {
          if (
            policyDiscounts.some(
              e => e.discount.typeRealEstate === DiscountTypeRealEstateEnum.HOUSE ||
                e.discount.typeRealEstate === DiscountTypeRealEstateEnum.LAND)
          ) {
            unit = null;
            history.push({
              error: `Chính sách chiết khẩu không phù hợp`
            });
          }
        } else {
          if (
            policyDiscounts.some(e => !e.discount.typeRealEstate || e.discount.typeRealEstate === DiscountTypeRealEstateEnum.DEFAULT)
          ) {
            unit = null;
            history.push({
              error: `Chính sách chiết khẩu không phù hợp`
            });
          }
        }
      }
    } else {
      model.policyDiscounts = [];
    }

    // Chính sách thanh toán
    if (paymentConfig && paymentConfig.paymentMethodDXG) {
      const policyPayment = await this.policyQueryService.findOne({
        code: paymentConfig.paymentMethodDXG, type: PolicyTypeEnum.PAYMENT
      });
      if (!policyPayment) {
        unit = null;
        history.push({
          error: `Không tìm thấy chính sách thanh toán`
        });
      } else {
        model.policyPayment = this.transformPolicyPayment(
          model,
          price,
          projectSetting,
          model.signedDate,
          receipt,
          policyPayment,
          housePrice,
          landPrice,
          model.maintenanceFee);
      }
    } else {
      unit = null;
      history.push({
        error: `Thiếu trường bắt buộc: Mã chính sách thanh toán`
      });
    }

    // Return báo lỗi
    if (!unit) {
      historyModel = {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }
    if (!oldContract) {
      if (!model.code) {
        const prefix = `${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-`;
        model.code = await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
      }
      model.name = `${model.code}-${model.primaryTransaction.customer.personalInfo.name}`
    } else {
      model.name = oldContract.name;
      model.code = oldContract.code;
    }
    if (model.policyPayment && !model.paymentPercent) {

      let totalPercentage = 0;
      let currentTotalTransfered = 0;

      model.policyPayment.schedule.installments.forEach((inst, index) => {
        currentTotalTransfered = currentTotalTransfered + inst.totalTransfered;
        if (index > 0) { // bắt đầu tính từ đợt 1;
          if (inst.totalTransfered < inst.totalAmount || inst.type === 'currency') {
            totalPercentage = totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
          } else {
            totalPercentage = totalPercentage + inst.value;
          }
        }
      });

      if (model.policyPayment.schedule.installments.length === 1) {
        totalPercentage = totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
      }

      model.paymentPercent = totalPercentage;
    }

    if (contract.companyInformation && contract.companyInformation.haveValue) {
      model.companyInformation = Object.assign({}, contract.companyInformation, {
        haveValue: true,
        dateOfIssue: contract.companyInformation.dateOfIssue ? moment(contract.companyInformation.dateOfIssue, 'DD/MM/YYYY').toDate() : ''
      })
    }

    if (!oldContract) {
      model.status = StatusContractEnum.ACCOUNTANT_WAITING;
    } else {
      delete model.status;
    }

    //  create resident
    let projectInfo: any = {};
    projectInfo = await this.propertyClient.sendDataPromise(model.primaryTransaction.project, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);
    let newObject = {};
    if (projectInfo) {
      newObject = {
        ...model.primaryTransaction, project: projectInfo,
        customer: model.primaryTransaction.customer,
        propertyUnit: model.primaryTransaction.propertyUnit,
        contractId: this.commandId
      };
    }

    // send transaction to care service
    await this.careClient.sendDataPromise({
      primaryTransaction: newObject
    }, CmdPatternConst.CARE.GET_TRANSACTION);

    await this.socialClient.sendDataPromise({
      projectId: model.primaryTransaction.project.id
    }, CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP);

    const { personalInfo, info } = (model['primaryTransaction'].customer || {}) || {};
    const { email, phone, name, identities } = personalInfo;

    // if(email && phone ) {
    //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)=> {

    //     if(!customer) {
    //       const payload = { personalInfo: { name, email, phone, identities }, accessSystem: "DX_AGENT"};
    //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

    //     }else if(!customer.isActive) {

    //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
    //     }

    //   })
    // }
    model.syncErpData = contract;
    // update price
    model.primaryTransaction.propertyUnit.contractPrice = price;
    updatePrimaryTransactionQuery.contractPrice = price;
    updatePrimaryTransactionQuery.propetyUnitId = primaryTransaction.propertyUnit.id;

    if (updatePrimaryTransactionQuery) {
      await this.propertyClient.sendDataPromise(updatePrimaryTransactionQuery, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CUSTOMER_FROM_CONTRACT);
    }
    if (!oldContract) {
      model.id = this.commandId;
      await this.queryRepository.create(model);
      await this.updateContractInTracsaction(model);
    } else {
      model.id = oldContract.id;
      await this.queryRepository.update(model);
    }

    // Return báo hoàn thành
    historyModel = {
      isSuccess: true,
      description: history,
    };
    return historyModel;
  }

}
