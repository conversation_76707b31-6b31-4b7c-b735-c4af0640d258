import {
  Controller,
  Post,
  Put,
  Body,
  Headers,
  UseGuards,
  UseInterceptors,
  Param,
  Delete,
  Req,
} from "@nestjs/common";
// import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { LiquidationDomainService } from "./service";
import { Action } from "../shared/enum/action.enum";
import { RolesGuard } from "../../common/guards/roles.guard";
import {
  CreateLiquidationDto,
  LiquidationStatusDto,
  UpdateLiquidationDto,
} from "./dto/liquidation.dto";
import { ACGuard, UseRoles } from "nest-access-control";
import { Usr } from "../shared/services/user/decorator/user.decorator";
import { PermissionEnum } from "../shared/enum/permission.enum";
import { ValidationPipe } from "../../common/pipes/validation.pipe";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
import { Request } from 'express';

@Controller("v1/liquidation")
@UseGuards(JwtAuthGuard)
export class LiquidationDomainController {
  private actionName: string = Action.NOTIFY;
  private resSuccess = { success: true };
  constructor(private readonly liquidationService: LiquidationDomainService) {}

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.LIQUIDATION_CREATE)
  @Post()
  async createLiquidation(@User() user, @Body(new ValidationPipe()) dto: CreateLiquidationDto, @Headers("act") actionName?: string) {
    this.actionName = actionName || this.actionName;
    return await this.liquidationService.createLiquidation(user, dto, this.actionName);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.LIQUIDATION_UPDATE)
  @Put()
  async updateLiquidation(@User() user, @Body(new ValidationPipe()) dto: UpdateLiquidationDto, @Headers("act") actionName?: string) {
    this.actionName = actionName || this.actionName;
    return await this.liquidationService.updateLiquidation(user, dto, this.actionName);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.LIQUIDATION_REQUEST_APPROVED)
  @Post('requestApproveLiquidation')
  async requestApproveLiquidation(
    @User() user: any,
    @Body(new ValidationPipe()) dto: LiquidationStatusDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.liquidationService.requestApproveLiquidation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.LIQUIDATION_APPROVED)
  @Post('approveLiquidation')
  async approveLiquidation(
    @User() user: any,
    @Body(new ValidationPipe()) dto: LiquidationStatusDto,
    @Req() req: Request,
    @Headers('act') actionName?: string
  ) {
    this.actionName = (actionName || this.actionName);
    const authorization = req.headers["authorization"];
    return await this.liquidationService.approveLiquidation(user, dto, this.actionName, authorization) || this.resSuccess;
  }
  
  @UseGuards(RoleGuard)
  @Roles(PermissionEnum.LIQUIDATION_DELETE)
  @Delete(":id")
  async deleteLiquidation(@User() user, @Param("id") id: string, @Headers("act") actionName?: string ) {
    this.actionName = actionName || this.actionName;
    return await this.liquidationService.deleteLiquidation(user, id, this.actionName);
  }
}
