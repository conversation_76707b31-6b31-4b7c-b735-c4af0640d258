import { Inject, Injectable } from '@nestjs/common';

import { Model } from 'mongoose';
import { BaseRepository } from '../../../../shared-modules';
import { ProposalRepositoryInterface } from './proposal.repository.interface';
import { CommonConst } from '../../shared/constant';
import { IProposalDocument } from 'src/modules/proposal.queryside/interfaces/document.interface';
import { CommonUtils } from '../../shared/classes/class-utils';
import _ = require("lodash");

@Injectable()
export class ProposalRepository extends BaseRepository<IProposalDocument> implements ProposalRepositoryInterface {
  constructor(@Inject(CommonConst.PROPOSAL_QUERY_MODEL_TOKEN) private readonly proposalModel: Model<IProposalDocument>) {
    super(proposalModel);
  }

  async findAll(query): Promise<IProposalDocument[]> {
      let project: any = { _id: 0 };
      if (!_.isEmpty(query._fields)) {
        const fields = query._fields.split(",");
        fields.forEach((f) => {
          project[f.trim()] = 1;
        });
        delete query._fields;
      }
      let sort: any = {
        createdDate: -1,
      };
      if (!_.isEmpty(query.sort)) {
        sort = CommonUtils.transformSort(query.sort) || {
          createdDate: -1,
        };
      }
      if (query.isPaging) {
        const page = query.page;
        const pageSize = query.pageSize;
        delete query.isPaging;
        delete query.page;
        delete query.pageSize;
        return await this.proposalModel
          .find(query, project)
          .sort(sort)
          .skip(page * pageSize - pageSize)
          .limit(pageSize);
      }
      return await this.proposalModel.find(query, project).sort(sort);
    }
    
    async countAll(query) {
      delete query.isPaging;
      delete query.page;
      delete query.pageSize;
      return await this.proposalModel.countDocuments(query);
    }
}
